package com.mi.info.intl.retail.org.domain.country.service.impl;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.mi.info.intl.retail.api.country.CountryTimeZoneApiService;
import com.mi.info.intl.retail.api.country.dto.CountryDTO;
import com.mi.info.intl.retail.component.ComponentLocator;
import com.mi.info.intl.retail.constant.CacheType;
import com.mi.info.intl.retail.core.utils.CacheUtils;
import com.mi.info.intl.retail.dto.LabelValueDTO;
import com.mi.info.intl.retail.org.domain.country.service.IntlCountryTimeZoneService;
import com.mi.info.intl.retail.org.domain.utils.ConvertUtils;
import com.mi.info.intl.retail.org.infra.entity.IntlRmsCountryTimezone;
import com.mi.info.intl.retail.org.infra.mapper.IntlRmsCountryTimezoneMapper;

import lombok.extern.slf4j.Slf4j;

/**
 * 国家 /地区时区服务
 *
 * <AUTHOR>
 * @date 2025/07/30
 */
@Slf4j
@Service
public class IntlCountryTimeZoneServiceImpl extends ServiceImpl<IntlRmsCountryTimezoneMapper, IntlRmsCountryTimezone>
    implements IntlCountryTimeZoneService, CountryTimeZoneApiService {

    /**
     * 获取地区国家 /地区时区列表
     *
     * @return {@link List }<{@link LabelValueDTO }>
     */
    @Override
    public List<LabelValueDTO> getAreaCountryTimeZoneList() {
        LambdaQueryWrapper<IntlRmsCountryTimezone> wrapper = new LambdaQueryWrapper<>();
        wrapper = wrapper.select(IntlRmsCountryTimezone::getArea, IntlRmsCountryTimezone::getAreaCode,
            IntlRmsCountryTimezone::getCountryName, IntlRmsCountryTimezone::getCountryCode);
        wrapper.isNotNull(IntlRmsCountryTimezone::getArea).ne(IntlRmsCountryTimezone::getArea, "");
        List<IntlRmsCountryTimezone> list = this.list(wrapper);
        if (CollectionUtils.isEmpty(list)) {
            return Lists.newArrayList();
        }
        return list.stream().collect(Collectors.groupingBy(IntlRmsCountryTimezone::getArea)).values().stream()
            .filter(CollectionUtils::isNotEmpty).map(intlRmsCountryTimezones -> {
                LabelValueDTO dto = new LabelValueDTO();
                dto.setId(intlRmsCountryTimezones.get(0).getAreaCode());
                dto.setTitle(intlRmsCountryTimezones.get(0).getArea());
                dto.setChildren(intlRmsCountryTimezones.stream().map(item -> {
                    LabelValueDTO labelValueDTO = new LabelValueDTO();
                    labelValueDTO.setTitle(item.getCountryName());
                    labelValueDTO.setId(item.getCountryCode());
                    return labelValueDTO;
                }).collect(Collectors.toList()));
                return dto;
            }).collect(Collectors.toList());
    }

    @Override
    public Map<String, CountryDTO> getAllCountryInfo() {
        List<IntlRmsCountryTimezone> list = this.list();
        if (CollectionUtils.isEmpty(list)) {
            return Maps.newHashMap();
        }
        return list.stream().map(ConvertUtils::getCountryDTO)
            .collect(Collectors.toMap(CountryDTO::getCountryCode, countryDTO -> countryDTO));
    }

    @Override
    public Optional<CountryDTO> getCountryInfoByCode(String countryCode) {
        IntlRmsCountryTimezone countryTimezone = getIntlRmsCountryTimezone(countryCode);
        if (countryTimezone == null) {
            return Optional.empty();
        }
        CountryDTO countryDTO = ConvertUtils.getCountryDTO(countryTimezone);
        return Optional.of(countryDTO);
    }

    @Override
    public List<CountryDTO> getCountryInfoByCodeList(List<String> countryCodeList) {
        if (CollectionUtils.isEmpty(countryCodeList)) {
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<IntlRmsCountryTimezone> wrapper = new LambdaQueryWrapper<>();
        wrapper = wrapper.select(IntlRmsCountryTimezone::getId, IntlRmsCountryTimezone::getCountryName,
            IntlRmsCountryTimezone::getCountryCode, IntlRmsCountryTimezone::getArea,
            IntlRmsCountryTimezone::getAreaCode, IntlRmsCountryTimezone::getCreatedAt,
            IntlRmsCountryTimezone::getUpdatedAt, IntlRmsCountryTimezone::getStateCode,
            IntlRmsCountryTimezone::getTimezoneCode);
        wrapper.in(IntlRmsCountryTimezone::getCountryCode, countryCodeList);
        List<IntlRmsCountryTimezone> countryList = this.list(wrapper);
        return ComponentLocator.getConverter().convertList(countryList, CountryDTO.class);
    }

    private IntlRmsCountryTimezone getIntlRmsCountryTimezone(String countryCode) {
        LambdaQueryWrapper<IntlRmsCountryTimezone> wrapper = new LambdaQueryWrapper<>();
        wrapper = wrapper.select(IntlRmsCountryTimezone::getId, IntlRmsCountryTimezone::getCountryName,
            IntlRmsCountryTimezone::getCountryCode, IntlRmsCountryTimezone::getArea,
            IntlRmsCountryTimezone::getAreaCode, IntlRmsCountryTimezone::getCreatedAt,
            IntlRmsCountryTimezone::getUpdatedAt, IntlRmsCountryTimezone::getStateCode,
            IntlRmsCountryTimezone::getTimezoneCode);
        wrapper.eq(IntlRmsCountryTimezone::getCountryCode, countryCode);
        return this.getOne(wrapper);
    }

    @Override
    public List<CountryDTO> getCountryInfo() {

        List<IntlRmsCountryTimezone> list = this.list();

        return list.stream().map(ConvertUtils::getCountryDTO).collect(Collectors.toList());
    }

    /**
     * 从缓存获取国家信息
     *
     * @param countryCode 国家代码
     * @return {@link Optional }<{@link CountryDTO }>
     */
    @Override
    public Optional<CountryDTO> getCountryInfoFromCache(String countryCode) {

        CountryDTO dto = CacheUtils.get(CacheType.COUNTRY_INFO, countryCode, CountryDTO.class);
        if (dto != null) {
            return Optional.of(dto);
        }
        IntlRmsCountryTimezone countryTimezone = getIntlRmsCountryTimezone(countryCode);
        if (countryTimezone == null) {
            return Optional.empty();
        }
        CountryDTO countryDTO = ConvertUtils.getCountryDTO(countryTimezone);
        CacheUtils.put(CacheType.COUNTRY_INFO, countryCode, countryDTO);
        return Optional.of(countryDTO);
    }

}
