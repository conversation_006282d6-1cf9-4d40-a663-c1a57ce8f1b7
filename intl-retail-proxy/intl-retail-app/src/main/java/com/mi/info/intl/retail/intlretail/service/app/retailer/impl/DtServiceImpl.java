package com.mi.info.intl.retail.intlretail.service.app.retailer.impl;

import com.mi.info.intl.retail.intlretail.service.api.retailer.DtService;
import com.mi.info.intl.retail.intlretail.service.api.store.dto.DtTokenRequest;
import com.mi.info.intl.retail.intlretail.service.api.store.dto.DtTokenResponse;
import com.mi.info.intl.retail.utils.Md5Util;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@DubboService(interfaceClass = DtService.class)
public class DtServiceImpl implements DtService {

    private static final String TOKEN_SECRET = "55680ff220e443fa9f09bcf118bba442";
    private static final String APP_SECRET = "037033f763864311ae9bb8729161ccfd";

    //考试分析看板类型码：
    private static final String EXAM_TYPE = "exam";
    //考试详情分析看板类型码：
    private static final String EXAM_DETAIL_TYPE = "examDetail";
    //考试分析看板测试环境秘钥：9dd947bc8174454b907438a55cf81a81 正式环境密钥：037033f763864311ae9bb8729161ccfd
    @Value("${bi.examBorder.secretKey:037033f763864311ae9bb8729161ccfd}")
    private String secretKey;

    @Override
    public DtTokenResponse getToken(DtTokenRequest request) {

        String token = this.getkey(request);

        String content = String.format("%s_%s_%s_%s_%s_%s",
                token,
                request.getTs(),
                request.getVersion(),
                request.getUserName(),
                request.getSysId(),
                request.getPageId());

        // 使用Md5Util工具类进行MD5加密(结果已经是大写)
        String hashToken = Md5Util.getMd5Digest(content);

        // 返回token信息
        return new DtTokenResponse(hashToken, content);
    }

    private String getkey(DtTokenRequest request) {

        String boardType = request.getBoardType();
        if (boardType == null || boardType.isEmpty()) {
            return TOKEN_SECRET;
        }
        switch (boardType) {
            case "Inspection":
                return APP_SECRET;
            case "TaskDetail":
                return TOKEN_SECRET;
            case "exam":
                return secretKey;
            case "examDetail":
                return secretKey;
            default:
                return APP_SECRET;
        }
    }
}
