<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.info.intl.retail.org.infra.mapper.IntlRmsPositionMapper">
    <resultMap id="BaseResultMap" type="com.mi.info.intl.retail.org.infra.entity.IntlRmsPosition">
        <id property="id" column="id"/>
        <result property="positionId" column="position_id"/>
        <result property="code" column="code"/>
        <result property="name" column="name"/>
        <result property="storeId" column="store_id"/>
        <result property="storeName" column="store_name"/>
        <result property="abbreviation" column="abbreviation"/>
        <result property="state" column="state"/>
        <result property="stateName" column="state_name"/>
        <result property="distributorId" column="distributor_id"/>
        <result property="distributorName" column="distributor_name"/>
        <result property="accountId" column="account_id"/>
        <result property="accountName" column="account_name"/>
        <result property="retailerId" column="retailer_id"/>
        <result property="retailerName" column="retailer_name"/>
        <result property="channelType" column="channel_type"/>
        <result property="channelTypeName" column="channel_type_name"/>
        <result property="type" column="type"/>
        <result property="typeName" column="type_name"/>
        <result property="level" column="level"/>
        <result property="levelName" column="level_name"/>
        <result property="isPromotionStore" column="is_promotion_store"/>
        <result property="countryId" column="country_id"/>
        <result property="countryName" column="country_name"/>
        <result property="cityId" column="city_id"/>
        <result property="cityName" column="city_name"/>
        <result property="address" column="address"/>
        <result property="modifiedOn" column="modified_on"/>
        <result property="createdOn" column="created_on"/>
        <result property="ownerId" column="owner_id"/>
        <result property="ownerName" column="owner_name"/>
        <result property="stateCode" column="state_code"/>
        <result property="area" column="area"/>
        <result property="areaCode" column="area_code"/>
        <result property="createdAt" column="created_at"/>
        <result property="updatedAt" column="updated_at"/>
        <result property="crpsCode" column="crpscode"/>
        <result property="positionCategory" column="position_category"/>
        <result property="furnitureTtl" column="furniture_ttl"/>
        <result property="displayCapacityExpansionStatus" column="display_capacity_expansion_status"/>
        <result property="positionLocation" column="position_location"/>
        <result property="positionLongitude" column="position_longitude"/>
        <result property="positionLatitude" column="position_latitude"/>
        <result property="countryShortcode" column="country_shortcode"/>
        <result property="supplierCode" column="supplier_code"/>
        <result property="organizationCode" column="organization_code"/>
        <result property="cityCode" column="city_code"/>
        <result property="hasPc" column="has_pc"/>
        <result property="hasSr" column="has_sr"/>
        <result property="provinceCode" column="province_code"/>
        <result property="positionClass" column="posiiton_class"/>
        <result property="constructionType" column="construction_type"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, position_id, code, name, store_id, store_name, abbreviation, state, state_name, distributor_id, distributor_name,
    account_id, account_name, retailer_id, retailer_name, channel_type, channel_type_name, type, type_name, level, level_name,
    is_promotion_store, country_id, country_name, city_id, city_name, address, modified_on, created_on, owner_id, owner_name,
    state_code, area, area_code, created_at, updated_at, crpscode, position_category, furniture_ttl, display_capacity_expansion_status,
    position_location, position_longitude, position_latitude, country_shortcode, supplier_code,
    organization_code, city_code, has_pc, has_sr, province_code, position_class, construction_type
    </sql>

    <!-- 查询所有记录 -->
    <select id="selectAll" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM intl_rms_position
        WHERE retailer_id IN
        <foreach collection="retailerCode" item="code" open="(" separator="," close=")">
            #{code}
        </foreach>
    </select>

    <select id="getStoreInfo" resultType="com.mi.info.intl.retail.org.infra.entity.IntlRmsStore">
        SELECT s.store_id          as storeId,
               p.channel_type_name as channelTypeName,
               s.name              as name,
               p.channel_type      as channelType
        FROM intl_rms_position p
                 JOIN
             intl_rms_store s ON p.store_id = s.store_id
        WHERE p.code = #{rmsPosition.positionCode}
    </select>

    <!-- 根据阵地code查询阵地和门店的联合信息 -->
    <select id="selectPositionWithStoreByCode"
            resultType="com.mi.info.intl.retail.api.front.position.dto.PositionStoreInfoDTO">
        SELECT p.id            as positionId,
               p.code          as positionCode,
               r.id            as retailerId,
               r.name          as retailerCode,
               p.type          as positionType,
               p.type_name     as positionTypeName,
               p.crpscode      as crpsCode,
               p.supplier_code as supplierCode,
               s.id            as storeId,
               s.code          as storeCode,
               s.type          as storeType,
               s.grade         as storeGrade,
               s.has_pc        as storeHasPC,
               s.has_sr        as storeHasSR,
               s.channel_type  as storeChannelType,
               s.crss_code     as crssCode,
               s.store_class   as storeClass,
               s.operation_status as storeStatus
        FROM intl_rms_position p
                 LEFT JOIN intl_rms_store s ON p.store_id = s.store_id
                 LEFT JOIN intl_rms_retailer r ON s.retailer_id = r.retailer_id
        WHERE p.code = #{positionCode}
          AND p.state_code = 0
          AND s.state_code = 0
    </select>

    <!-- 查询门店下所有阵地 -->
    <select id="getPositionsByStoreCode" resultMap="BaseResultMap">
        SELECT p.id,
               p.position_id,
               p.code,
               p.name,
               p.store_id,
               p.store_name,
               p.abbreviation,
               p.state,
               p.state_name,
               p.distributor_id,
               p.distributor_name,
               p.account_id,
               p.account_name,
               p.retailer_id,
               p.retailer_name,
               p.channel_type,
               p.channel_type_name,
               p.type,
               p.type_name,
               p.level,
               p.level_name,
               p.is_promotion_store,
               p.country_id,
               p.country_name,
               p.city_id,
               p.city_name,
               p.address,
               p.modified_on,
               p.created_on,
               p.owner_id,
               p.owner_name,
               p.state_code,
               p.area,
               p.area_code,
               p.crpscode,
               p.position_category,
               p.furniture_ttl,
               p.display_capacity_expansion_status,
               p.position_location,
               p.position_longitude,
               p.position_latitude,
               p.country_shortcode
        FROM intl_rms_position p
                 JOIN intl_rms_store s ON p.store_id = s.store_id
        WHERE (s.code = #{storeCode} OR s.crss_code = #{storeCode})
          AND p.state_code = 0
          AND s.state_code = 0
          AND p.state = 1
        ORDER BY CASE p.type_name
                     WHEN 'SIS' THEN 1
                     WHEN 'ES' THEN 2
                     WHEN 'DZ' THEN 3
                     WHEN 'DC' THEN 4
                     WHEN 'POS' THEN 5
                     ELSE 6
                     END,
                 p.created_on ASC
    </select>

    <!-- 一次性查询门店信息、用户门店关系和阵地信息（用于IMEI导入校验） -->
    <select id="getStoreValidationInfo"
            resultType="com.mi.info.intl.retail.api.front.position.dto.StoreValidationInfoDTO">
        SELECT s.store_id          as storeId,
               s.code              as storeCode,
               s.crss_code         as crssCode,
               s.name              as storeName,
               s.country_shortcode as countryShortcode,
               s.created_on        as storeCreatedOn,
               s.state_code        as storeStateCode,
               u.rms_userid        as rmsUserid,
               u.mi_id             as miId,
               u.job_id            as jobId,
               up.position_id      as positionId,
               up_pos.code         as positionCode,
               up_pos.name         as positionName
        FROM intl_rms_store s
                 LEFT JOIN intl_rms_position up_pos ON up_pos.store_id = s.store_id
                 LEFT JOIN intl_rms_personnel_position up ON up.position_id = up_pos.position_id
                 LEFT JOIN intl_rms_user u ON u.rms_userid = up.user_id
        WHERE (s.code = #{storeCode} OR s.crss_code = #{storeCode})
          AND s.state_code = 0
          AND s.operation_status = 100000000
          AND u.mi_id = #{miId}
          AND u.is_disabled = 0
          AND up.state_code = 0
          AND up_pos.state_code = 0
        LIMIT 1
    </select>

    <!-- 检查门店是否存在 -->
    <select id="getStoreByCode" resultType="com.mi.info.intl.retail.api.front.dto.RmsStoreInfoDto">
        SELECT s.store_id         AS storeId,
               s.code             AS storeCode,
               s.crss_code        AS crssCode,
               s.operation_status AS operationStatus
        FROM intl_rms_store s
        WHERE (s.code = #{storeCode} OR s.crss_code = #{storeCode})
          AND s.state_code = 0
    </select>

    <select id="getPositionsByPositionIds" resultType="com.mi.info.intl.retail.api.front.dto.RmsPositionIAndStoreRes">
        SELECT
        p.position_id,
        p.`code` as positionCodeRMS,
        s.`code` as storeCodeRMS,
        s.crss_code as storeCodeNew,
        l.name as retailerCode
        FROM
        intl_rms_position p
        LEFT JOIN intl_rms_store s ON p.store_id = s.store_id
        left join intl_rms_retailer l on s.retailer_id=l.retailer_id
        WHERE
        p.position_id IN
        <foreach item="item" collection="positionIds" separator="," close=")" open="(" index="index">
            #{item}
        </foreach>
    </select>

    <insert id="insertList">
        insert into intl_rms_position (position_id, code, name, store_id, store_name, abbreviation, state, state_name,
        distributor_id, distributor_name,
        account_id, account_name, retailer_id, retailer_name, channel_type, channel_type_name, type, type_name, level,
        level_name,
        is_promotion_store, country_id, country_name, city_id, city_name, address, modified_on, created_on, owner_id,
        owner_name,
        state_code, area, area_code, created_at, updated_at, crpscode, position_category, furniture_ttl,
        display_capacity_expansion_status,
        position_location, position_longitude, position_latitude, country_shortcode, supplier_code,
        organization_code, city_code, has_pc, has_sr, province_code, position_class, construction_type)
        values
        <foreach item="item" collection="positionsToInsert" separator=",">
            (#{item.positionId}, #{item.code}, #{item.name}, #{item.storeId}, #{item.storeName}, #{item.abbreviation},
            #{item.state}, #{item.stateName}, #{item.distributorId}, #{item.distributorName}, #{item.accountId},
            #{item.accountName}, #{item.retailerId}, #{item.retailerName}, #{item.channelType}, #{item.channelTypeName},
            #{item.type}, #{item.typeName}, #{item.level}, #{item.levelName}, #{item.isPromotionStore},
            #{item.countryId},
            #{item.countryName}, #{item.cityId}, #{item.cityName}, #{item.address}, #{item.modifiedOn},
            #{item.createdOn},
            #{item.ownerId}, #{item.ownerName}, #{item.stateCode}, #{item.area}, #{item.areaCode}, #{item.createdAt},
            #{item.updatedAt}, #{item.crpsCode}, #{item.positionCategory}, #{item.furnitureTtl},
            #{item.displayCapacityExpansionStatus}, #{item.positionLocation}, #{item.positionLongitude},
            #{item.positionLatitude}, #{item.countryShortcode}, #{item.supplierCode}, #{item.organizationCode},
            #{item.cityCode}, #{item.hasPc}, #{item.hasSr}, #{item.provinceCode}, #{item.positionClass},
            #{item.constructionType})
        </foreach>
    </insert>
</mapper>
