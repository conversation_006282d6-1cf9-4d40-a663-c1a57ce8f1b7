package com.mi.info.intl.retail.so.domain.upload.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.info.intl.retail.so.domain.upload.entity.IntlRmsProvince;
import com.mi.info.intl.retail.so.domain.upload.service.IntlRmsProvinceService;
import com.mi.info.intl.retail.so.infra.database.mapper.upload.IntlRmsProvinceMapper;
import org.springframework.stereotype.Service;

/**
 * 省份表服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-31 10:27:23
 */
@Service
public class IntlRmsProvinceServiceImpl extends ServiceImpl<IntlRmsProvinceMapper, IntlRmsProvince> implements IntlRmsProvinceService {

}
