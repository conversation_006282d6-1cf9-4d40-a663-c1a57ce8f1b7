package com.mi.info.intl.retail.http.response.handle;

import com.mi.info.intl.retail.http.utils.Utils;
import lombok.Getter;
import lombok.Setter;
import okhttp3.Response;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.File;
import java.io.IOException;

/**
 * 文件处理器,一般用于从远程下载资源(如图片、报表等)
 * 支持自动获取文件名，也支持自定义文件名
 *
 * <AUTHOR>
 * @date 2025/08/04
 */
@Getter
@Setter
public class FileDataHandler implements DataHandler<File> {

    /**
     * 返回保存的文件目录
     */
    private final String dirPath;

    /**
     * 返回保存的文件名
     */
    private String filename;

    public FileDataHandler(String dirPath) {
        this.dirPath = dirPath;
    }

    public FileDataHandler(String dirPath, String filename) {
        this(dirPath);
        this.filename = filename;
    }

    /**
     * 得到相应结果后,将相应数据转为需要的数据格式
     *
     * @param response 需要转换的对象
     * @return 存储的文件信息
     * @throws IOException 出现异常
     */
    @Override
    public File handle(final Response response) throws IOException {
        String name = this.filename;
        if (StringUtils.isEmpty(name)) {
            name = Utils.getFilename(response);
        }
        File saveFile = new File(this.dirPath, name);
        FileUtils.copyInputStreamToFile(response.body().byteStream(), saveFile);
        return saveFile;
    }
}
