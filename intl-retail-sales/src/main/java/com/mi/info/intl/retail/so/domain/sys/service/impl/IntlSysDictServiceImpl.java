package com.mi.info.intl.retail.so.domain.sys.service.impl;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Maps;
import com.mi.info.intl.retail.api.country.CountryTimeZoneApiService;
import com.mi.info.intl.retail.dto.LabelValueDTO;
import com.mi.info.intl.retail.intlretail.service.api.so.sys.dto.DictSysDTO;
import com.mi.info.intl.retail.intlretail.service.api.so.sys.request.DictSysRequest;
import com.mi.info.intl.retail.so.domain.rule.service.IntlSoRuleDetailService;
import com.mi.info.intl.retail.so.domain.sys.constants.SysConstants;
import com.mi.info.intl.retail.so.domain.sys.service.IntlSysDictService;
import com.mi.info.intl.retail.so.infra.database.dataobject.sys.IntlSysDict;
import com.mi.info.intl.retail.so.infra.database.mapper.sys.IntlSysDictMapper;

/**
 * 数据字典表 服务实现类
 *
 * <AUTHOR>
 * @date 2025/07/25
 */
@Service
public class IntlSysDictServiceImpl extends ServiceImpl<IntlSysDictMapper, IntlSysDict> implements IntlSysDictService {
    @Resource
    private CountryTimeZoneApiService countryTimeZoneApiService;

    @Resource
    private IntlSoRuleDetailService intlSoRuleDetailService;

    @Override
    public Map<String, List<LabelValueDTO>> getLabelValueListByDictCode(DictSysRequest request) {
        List<DictSysDTO> dictCodeList = request.getDictCodeList();
        Map<String, List<LabelValueDTO>> map = Maps.newHashMap();

        buildCountryMap(request, map);
        if (CollectionUtils.isEmpty(dictCodeList)) {
            return map;
        }
        LambdaQueryWrapper<IntlSysDict> wrapper = new LambdaQueryWrapper<>();
        wrapper = wrapper.select(IntlSysDict::getDictCode, IntlSysDict::getDictLabel, IntlSysDict::getDictValue,
            IntlSysDict::getSort);
        wrapper.and(t -> {
            for (DictSysDTO dictCode : request.getDictCodeList()) {
                t = t.or(query -> query.eq(IntlSysDict::getDictType, dictCode.getDictType())
                    .eq(IntlSysDict::getDictCode, dictCode.getDictCode()));
            }
        });

        List<IntlSysDict> labelValueList = this.list(wrapper);
        map.putAll(labelValueList.stream().collect(Collectors.groupingBy(IntlSysDict::getDictCode)).entrySet().stream()
            .collect(Collectors.toMap(Map.Entry::getKey,
                t -> t.getValue().stream().sorted(Comparator.comparing(IntlSysDict::getSort).reversed())
                    .map(dict -> new LabelValueDTO(dict.getDictValue(), dict.getDictLabel(), null, null))
                    .collect(Collectors.toList()))));
        return map;
    }

    /**
     * 获取国家列表，如果有展示disable的话，需 要从零售列表中获取已经创建过规则的国家
     *
     * @param req req
     * @param map 地图
     */
    private void buildCountryMap(DictSysRequest req, Map<String, List<LabelValueDTO>> map) {
        List<DictSysDTO> dictCodeList = req.getDictCodeList();
        boolean country =
            dictCodeList.stream().anyMatch(dictCode -> Objects.equals(dictCode.getDictType(), SysConstants.SYS)
                && Objects.equals(dictCode.getDictCode(), SysConstants.COUNTRY));
        if (!country) {
            return;
        }
        dictCodeList.removeIf(dictCode -> Objects.equals(dictCode.getDictType(), SysConstants.SYS)
            && Objects.equals(dictCode.getDictCode(), SysConstants.COUNTRY));

        List<LabelValueDTO> dtoList = countryTimeZoneApiService.getAreaCountryTimeZoneList();
        // 如果不展示disabled，直接返回国家列表
        if (req.getCountryDisplay() != 1) {
            map.put(SysConstants.COUNTRY, dtoList);
            return;
        }
        List<String> list = intlSoRuleDetailService.getAllSoRuleCountryCode();
        if (CollectionUtils.isEmpty(list)) {
            map.put(SysConstants.COUNTRY, dtoList);
            return;
        }
        // 转换成map
        Map<String, String> displayCountryMap = list.stream().collect(Collectors.toMap(t -> t, t -> t));
        dtoList.stream().filter(t -> t != null && CollectionUtils.isNotEmpty(t.getChildren()))
            .flatMap(t -> t.getChildren().stream())
            .forEach(t -> t.setDisabled(displayCountryMap.containsKey(t.getId())));
        map.put(SysConstants.COUNTRY, dtoList);
    }

}
