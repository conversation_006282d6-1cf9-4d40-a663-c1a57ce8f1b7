package com.mi.info.intl.retail.org.infra.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 国家时区表
 *
 * @TableName intl_rms_country_timezone
 */
@TableName(value = "intl_rms_country_timezone")
@Data
public class IntlRmsCountryTimezone implements Serializable {

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    private String countryTimezoneId;
    private String name;
    private String countryId;
    private String countryName;
    private String countryCode;
    private String timezoneName;
    private String timezoneCode;
    private String bias;
    private Integer stateCode;
    private String area;
    private String areaCode;
    private Long createdAt;
    private Long updatedAt;
    private String code;

}