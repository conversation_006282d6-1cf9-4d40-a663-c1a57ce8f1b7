spring:
  application:
    name: intl-retail
    # 默认根据环境开启相应 profile 配置， 即本地默认开启 dev profile。详见 https://docs.mit.mi.com/mit-commons-java/mit-starter/env-profile
  profiles:
    include: rms, dubbo, mq, db, firebase, conf, http, es
    active: @spring.profiles.active@
  servlet:
    multipart:
      max-file-size: 1024MB
      max-request-size: 1024MB
  cache:
    type: redis
    redis:
      time-to-live: 86400000  # 缓存过期时间（毫秒）默认24小时
      cache-null-values: false  # 不缓存空值
      use-key-prefix: true  # 使用键前缀
      key-prefix: "intl-retail:"  # 键前缀
    enabled: false #自定义的属性，默认不开启，各自环境根据自己需要开启

dubbo:
  scan:
    base-packages: com.mi.info.intl.retail.intlretail
  registry:
    address: ${nacos.address}
  protocol:
    id: dubbo
    name: dubbo
    port: -1
  provider:
    retries: 1
    validation: false
    filter: errorcodeExceptionFilter, -exception
server:
  port: 10020

comb:
  x5:
    request-secrets:
      - app-id: xm_test
        #        app-key@kc-sid: keycenter-test  # app-key原文: 18f34bde4bb73c10f8ba1b02e3cf3efd
        app-key: GDAfiYp0JfVuGHVDyICPBjypUOx/BLwoxxDPfdiZWqOpQveexQVCcwhZZY4AyOcEOloYEo+OHExnG0Gso0VnL9f7qkPz/xgQ8mZTe0l2S9OZKwJbSSID2RgUP4+lS5TRex8o3Jta4fgXX+A+RW4A
        allow-methods: findUserList
  # 替换为你的 mapper 接口所在包

mybatis-plus:
  configuration: ### 开启打印sql配置
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  type-handlers-package: com.mi.info.intl.retail.cooperation.task.config

guard:
  mail:
    host: mail.b2c.srv
    token: <EMAIL>

MiApi:
  allowPush: true # 是否允许推送
  autoUpdate: false # 是否自动更新
  openSpring: false # http类型配置
  openApacheAnno: false # dubbo类型配置
  projectId2Gen: 123696 # api项目id
  opUser: default_user
  updateMsg: auto_update # 自动更新时commit message


#nr-job
proretail:
  project:
    id: 11
    name: intl-retail

job:
  admin:
    addresses: http://nr-job.test.be.mi.com
  accessToken:
  executor:
    appname: intl-retail
    ip: ""
    port: 9991
    logpath: /home/<USER>/log/xmstore-objective/jobhandler
    logretentiondays: 30

intl-retail:
  rocketmq:
    so-sync:
      topic: rms_sync_to_retail
      group: rms_sync_to_retail_group
      enabled: true
    to-rms:
      topic: retail_sync_to_rms
      group: retail_sync_to_rms_group
      enabled: true
    so-to-es:
      topic: sync_so_to_es
      group: sync_so_to_es_group
      max-batch-size: 500
      enabled: true