package com.mi.info.intl.retail.so.infra.database.dataobject.rule;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;

/**
 * 零售商规则配置表
 *
 * <AUTHOR>
 */
@Data
@TableName("intl_so_rule_retailers")
public class IntlSoRuleRetailer implements Serializable {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 区域编码
     */
    @TableField("region_code")
    private String regionCode;

    /**
     * 国家编码
     */
    @TableField("country_code")
    private String countryCode;

    /**
     * 类型，用于区分是主数据还是变更记录，0-主数据 1-变更记录
     */
    @TableField("category")
    private Integer category;

    /**
     * 规则ID
     */
    @TableField("rule_id")
    private Long ruleId;

    /**
     * 零售商编码
     */
    @TableField("retailer_code")
    private String retailerCode;

    /**
     * 零售商名称
     */
    @TableField("retailer_name")
    private String retailerName;

    /**
     * 渠道类型
     */
    @TableField("channel_type")
    private String channelType;

    /**
     * 该零售商是否校验imei规则(0:否,1:是)
     */
    @TableField("imei_switch")
    private Integer imeiSwitch;

    /**
     * 该零售商是否校验qty规则(0:否,1:是)
     */
    @TableField("qty_switch")
    private Integer qtySwitch;

    /**
     * 零售商首次创建时间
     */
    @TableField("create_retailer_time")
    private Long createRetailerTime;

    /**
     * 创建日期
     */
    @TableField("created_at")
    private Long createdAt;

    /**
     * 创建人ID
     */
    @TableField("created_by")
    private String createdBy;

    /**
     * 修改人ID
     */
    @TableField("updated_by")
    private String updatedBy;

    /**
     * 目标修改日期
     */
    @TableField("updated_at")
    private Long updatedAt;

    /**
     * 是否是新零售商
     */
    @TableField(value = "is_new")
    private int isNew;

} 