package com.mi.info.intl.retail.intlretail.service.app.inspection.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mi.info.intl.retail.advice.excel.export.ExportExcelAspect;
import com.mi.info.intl.retail.component.ComponentLocator;
import com.mi.info.intl.retail.cooperation.task.dto.IntlInspectionTaskQuery;
import com.mi.info.intl.retail.cooperation.task.infra.entity.InspectionAction;
import com.mi.info.intl.retail.cooperation.task.infra.entity.IntlInspectionTaskConf;
import com.mi.info.intl.retail.cooperation.task.infra.entity.IntlRmsCountryTimezone;
import com.mi.info.intl.retail.cooperation.task.infra.mapper.read.CountryTimezoneReadMapper;
import com.mi.info.intl.retail.cooperation.task.inspection.BigPromotionConfigService;
import com.mi.info.intl.retail.cooperation.task.inspection.IntlInspectionTaskConfService;
import com.mi.info.intl.retail.intlretail.domain.common.utils.JsonUtil;
import com.mi.info.intl.retail.intlretail.service.api.fds.FdsService;
import com.mi.info.intl.retail.intlretail.service.api.fds.dto.FdsUploadResult;
import com.mi.info.intl.retail.intlretail.service.api.inspection.InspectionService;
import com.mi.info.intl.retail.intlretail.service.api.request.BigPromotionConfCreateRequest;
import com.mi.info.intl.retail.intlretail.service.api.request.BigPromotionConfRequest;
import com.mi.info.intl.retail.intlretail.service.api.request.BigPromotionConfStopRequest;
import com.mi.info.intl.retail.intlretail.service.api.request.IntlInspectionTaskRequest;
import com.mi.info.intl.retail.intlretail.service.api.request.PersonAreaRequest;
import com.mi.info.intl.retail.intlretail.service.api.request.StopInspectionTaskRequest;
import com.mi.info.intl.retail.intlretail.service.api.result.BigPromotionConfigTaskDto;
import com.mi.info.intl.retail.intlretail.service.api.result.InspectionTaskConfDTO;
import com.mi.info.intl.retail.intlretail.service.api.result.PersonAreaResponse;
import com.mi.info.intl.retail.intlretail.service.api.result.SupervisorTaskListResponse;
import com.mi.info.intl.retail.intlretail.service.api.retailer.dto.CommonResponse;
import com.mi.info.intl.retail.model.CommonApiResponse;
import com.xiaomi.nr.eiam.api.dto.userinfo.GetUserInfoResp;
import com.xiaomi.nr.eiam.api.dto.userinfo.ListUserInfoReq;
import com.xiaomi.nr.eiam.api.dto.userinfo.Position;
import com.xiaomi.nr.eiam.api.provider.UserProvider;
import com.xiaomi.youpin.infra.rpc.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.io.File;
import java.text.SimpleDateFormat;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/6/12
 **/
@DubboService(group = "${center.dubbo.group:}", interfaceClass = InspectionService.class)
@Service
@Slf4j
public class InspectionServiceImpl implements InspectionService {

    @Resource
    private IntlInspectionTaskConfService intlInspectionTaskConfService;

    @Resource
    private BigPromotionConfigService bigPromotionConfigService;

    @Resource
    private FdsService fdsService;

    private static final String NEW_PRODUCT_TASK_COMPLETION_ACTION_STANDARD = "Only need to finishing [Sales Reporting] + [Inventory Reporting] + "
            +
            "any one of the following: " + "[LDU Reporting], [Display Reporting], " +
            "[Training Reporting]," + " or [Store Check] at the position, " +
            "regardless of inspection duration.";

    @DubboReference(group = "${eiam.dubbo.group:}", check = false, timeout = 20000, retries = 0)
    private UserProvider userProvider;

    @Resource
    private CountryTimezoneReadMapper countryTimezoneReadMapper;

    @Override
    public CommonApiResponse<PersonAreaResponse> getPersonAreaInfo(PersonAreaRequest request) {
        log.info("getPersonAreaInfo_request:{}", JsonUtil.bean2json(request));

        // 1. 参数校验
        if (request == null || request.getMiIdList().isEmpty()) {
            return new CommonApiResponse<>(null);
        }
        PersonAreaResponse response = new PersonAreaResponse();
        // 2. 调用组织中台接口获取areaId列表
        ListUserInfoReq userInfoRequest = new ListUserInfoReq();
        userInfoRequest.setScene("new_retail");
        userInfoRequest.setMiIdList(request.getMiIdList());

        Result<List<GetUserInfoResp>> result;

        try {
            result = userProvider.listUserInfo(userInfoRequest);
            log.info("listUserInfo_result:{}", JsonUtil.bean2json(result));
        } catch (Exception e) {
            log.error("listUserInfo_error:{}", e.getMessage());
            return new CommonApiResponse<>(response);
        }

        if (result == null || result.getData() == null || result.getData().isEmpty()) {
            return new CommonApiResponse<>(response);
        }

        // 3. 提取并去重areaId
        List<String> areaIds = result.getData().stream()
                .flatMap(userInfo -> userInfo.getPositionList().stream())
                .map(Position::getAreaId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());

        if (areaIds.isEmpty()) {
            return new CommonApiResponse<>(response);
        }

        // 4. 查询国家时区信息
        List<IntlRmsCountryTimezone> timezones = countryTimezoneReadMapper.selectCountryIds(areaIds);

        if (timezones.isEmpty()) {
            return new CommonApiResponse<>(response);
        }

        // 5.1 处理国家信息(根据areaId和countryName去重) 并按照首字母升序排序
        List<PersonAreaResponse.CountryInfoDTO> countryList = timezones.stream()
                .map(tz -> {
                    PersonAreaResponse.CountryInfoDTO dto = new PersonAreaResponse.CountryInfoDTO();
                    dto.setAreaId(tz.getCountryCode());
                    dto.setCountryName(tz.getCountryName());
                    dto.setAreaCode(tz.getAreaCode());
                    return dto;
                })
                .distinct()
                .sorted(Comparator.comparing(PersonAreaResponse.CountryInfoDTO::getCountryName))
                .collect(Collectors.toList());

        // 5.2 处理区域信息(根据area和areaCode去重) 并按照首字母升序排序
        List<PersonAreaResponse.AreaInfoDTO> areaList = timezones.stream()
                .map(tz -> {
                    PersonAreaResponse.AreaInfoDTO dto = new PersonAreaResponse.AreaInfoDTO();
                    dto.setArea(tz.getArea());
                    dto.setAreaCode(tz.getAreaCode());
                    return dto;
                })
                .distinct()
                .sorted(Comparator.comparing(PersonAreaResponse.AreaInfoDTO::getArea))
                .collect(Collectors.toList());

        response.setCountryList(countryList);
        response.setAreaList(areaList);

        log.info("getPersonAreaInfo_response:{}", JsonUtil.bean2json(response));

        return new CommonApiResponse<>(response);
    }

    @Override
    public CommonApiResponse<IPage<InspectionTaskConfDTO>> pageList(IntlInspectionTaskRequest request) {
        IntlInspectionTaskQuery query = convert2Query(request);
        IPage<com.mi.info.intl.retail.cooperation.task.dto.dto.InspectionTaskConfDTO> inspectionTaskConfDTOIPage = intlInspectionTaskConfService
                .pageList(query);
        List<com.mi.info.intl.retail.cooperation.task.dto.dto.InspectionTaskConfDTO> dtoList = inspectionTaskConfDTOIPage
                .getRecords();
        IPage<InspectionTaskConfDTO> page = new Page<>();
        page.setPages(inspectionTaskConfDTOIPage.getPages());
        page.setTotal(inspectionTaskConfDTOIPage.getTotal());
        page.setCurrent(inspectionTaskConfDTOIPage.getCurrent());
        page.setSize(inspectionTaskConfDTOIPage.getSize());

        if (CollUtil.isEmpty(dtoList)) {
            return new CommonApiResponse<>(page);
        }

        List<InspectionTaskConfDTO> inspectionTaskConfDTOS = dtoList.stream().map(item -> {
            InspectionTaskConfDTO dto = ComponentLocator.getConverter().convert(item, InspectionTaskConfDTO.class);
            List<String> hasPromoterFrontInspectionAction = item.getHasPromoterFrontInspectionAction().stream()
                    .map(InspectionAction::getActionName).collect(Collectors.toList());
            dto.setHasPromoterFrontInspectionAction(hasPromoterFrontInspectionAction);
            List<String> noPromoterFrontInspectionAction = item.getNoPromoterFrontInspectionAction().stream()
                    .map(InspectionAction::getActionName).collect(Collectors.toList());
            dto.setNoPromoterFrontInspectionAction(noPromoterFrontInspectionAction);
            List<String> hasPromoterPosInspectionAction = item.getHasPromoterPosInspectionAction().stream()
                    .map(InspectionAction::getActionName).collect(Collectors.toList());
            dto.setHasPromoterPosInspectionAction(hasPromoterPosInspectionAction);
            List<String> noPromoterPosInspectionAction = item.getNoPromoterPosInspectionAction().stream()
                    .map(InspectionAction::getActionName).collect(Collectors.toList());
            dto.setNoPromoterPosInspectionAction(noPromoterPosInspectionAction);
            dto.setDurationOfNewProductsInStore(0);
            dto.setNewProductTaskCompletionActionStandard(NEW_PRODUCT_TASK_COMPLETION_ACTION_STANDARD);
            return dto;
        }).collect(Collectors.toList());

        page.setRecords(inspectionTaskConfDTOS);
        return new CommonApiResponse<>(page);
    }

    @Override
    public CommonApiResponse<String> export(IntlInspectionTaskRequest request) {
        File tempFile = null;
        ExcelWriter excelWriter = null;
        try {
            tempFile = File.createTempFile("inspection_task_", ".xlsx");

            excelWriter = EasyExcel.write(tempFile, InspectionTaskConfDTO.class)
                    .registerWriteHandler(new ExportExcelAspect.AdaptiveWidthStyleStrategy()).build();
            WriteSheet writeSheet = EasyExcel.writerSheet("任务列表").build();

            long pageSize = 1000L;
            long currentPage = 1L;
            boolean hasNext = true;

            while (hasNext) {
                request.setPageNum(currentPage);
                request.setPageSize(pageSize);

                CommonApiResponse<IPage<InspectionTaskConfDTO>> iPageCommonApiResponse = this.pageList(request);
                List<InspectionTaskConfDTO> records = iPageCommonApiResponse.getData().getRecords();
                if (CollUtil.isEmpty(records)) {
                    hasNext = false;
                    continue;
                }
                // 导出只有是新品大促期间的才展示大促时间
                records.stream()
                        .filter(record -> !record.getIsNewProductPromotion())
                        .forEach(record -> {
                            record.setPromotionStartTime(null);
                            record.setPromotionEndTime(null);
                            record.setNewProductTaskCompletionActionStandard(null);
                            record.setDurationOfNewProductsInStore(null);
                        });

                excelWriter.write(records, writeSheet);

                hasNext = currentPage * pageSize < iPageCommonApiResponse.getData().getTotal();
                currentPage++;
            }

            if (excelWriter != null) {
                excelWriter.finish();
            }
            String timestamp = String.valueOf(System.currentTimeMillis() / 1000L);
            return new CommonApiResponse<>(fdsService.upload("inspectionTask" + timestamp + ".xlsx", tempFile, true).getUrl());
        } catch (Exception e) {
            log.error("导出任务列表异常, request: {}", request, e);
            return new CommonApiResponse<>("");
        } finally {
            if (excelWriter != null) {
                excelWriter.finish();
            }
            if (tempFile != null && tempFile.exists()) {
                tempFile.delete();
            }
        }
    }

    @Override
    public CommonApiResponse<String> stopTask(StopInspectionTaskRequest request) {

        Assert.isTrue(null != request.getMiId(), "MiId is Empty");

        IntlInspectionTaskConf taskConf = intlInspectionTaskConfService.getById(request.getTaskId());

        if (Objects.isNull(taskConf)) {
            return CommonApiResponse.failure(400, "任务不存在");
        }
        // 设置为停用/启用
        taskConf.setIsDisabled(request.getStatus());
        taskConf.setUpdatedAt((int) (System.currentTimeMillis() / 1000L));
        taskConf.setUpdatedBy(Long.valueOf(request.getMiId()));
        intlInspectionTaskConfService.updateById(taskConf);
        return new CommonApiResponse<>("success");
    }

    @Override
    public CommonResponse<SupervisorTaskListResponse> getBigPromotionTaskList(BigPromotionConfRequest request) {
        return bigPromotionConfigService.getTaskList(request);
    }

    @Override
    public CommonResponse<String> bigPromotionCreate(BigPromotionConfCreateRequest request) {
        if (StringUtils.isEmpty(request.getCountry()) || StringUtils.isEmpty(request.getStartTime())
                || StringUtils.isEmpty(request.getName()) || StringUtils.isEmpty(request.getEndTime())) {
            return CommonResponse.failure(400, "parameter error");
        }
        // 校验时间格式为 yyyy-MM-dd，且结束时间晚于开始时间
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            sdf.setLenient(false); // 严格校验格式
            Date startDate = sdf.parse(request.getStartTime());
            Date endDate = sdf.parse(request.getEndTime());
            if (!endDate.after(startDate)) {
                return CommonResponse.failure(400, "End time must be after start time");
            }
        } catch (Exception e) {
            return CommonResponse.failure(400, "Invalid date format. Expected yyyy-MM-dd");
        }
        // 开始时间增加 00:00:00
        request.setStartTime(request.getStartTime() + " 00:00:00");
        request.setEndTime(request.getEndTime() + " 23:59:59");
        request.setCreatedBy(request.getMiID());
        log.info("create request:{}", JsonUtil.bean2json(request));
        return bigPromotionConfigService.create(request);
    }

    @Override
    public CommonResponse<String> bigPromotionStop(BigPromotionConfStopRequest request) {
        request.setUpdatedBy(request.getMiID());
        log.info("stop request:{}", request);
        return bigPromotionConfigService.stop(request);
    }

    @Override
    public CommonResponse<String> bigPromotionStopExportTask(BigPromotionConfRequest request) {
        File tempFile = null;
        ExcelWriter excelWriter = null;
        String timestamp = String.valueOf(System.currentTimeMillis() / 1000L);
        try {
            tempFile = File.createTempFile("inspection_bigPromotion_task_" + timestamp, ".xlsx");
            excelWriter = EasyExcel.write(tempFile, BigPromotionConfigTaskDto.class).build();
            WriteSheet writeSheet = EasyExcel.writerSheet("new_product_task_list").build();

            int pageSize = 1000;
            int currentPage = 1;
            boolean hasNext = true;

            while (hasNext) {
                request.setPageNum(currentPage);
                request.setPageSize(pageSize);

                CommonResponse<SupervisorTaskListResponse> promotionTaskList = this.getBigPromotionTaskList(request);
                List<BigPromotionConfigTaskDto> records = promotionTaskList.getData().getList();
                if (CollUtil.isEmpty(records)) {
                    hasNext = false;
                    continue;
                }

                excelWriter.write(records, writeSheet);

                hasNext = currentPage * pageSize < promotionTaskList.getData().getTotal();
                currentPage++;
            }
            if (excelWriter != null) {
                excelWriter.finish();
            }
            FdsUploadResult upload = fdsService.upload("inspection_bigPromotion_task_" + timestamp + ".xlsx", tempFile,
                    true);
            return new CommonResponse<>(upload.getUrl());
        } catch (Exception e) {
            log.error("导出新品任务列表异常, request: {}", request, e);
            return CommonResponse.failure(-1, "export err");
        } finally {
            if (excelWriter != null) {
                excelWriter.finish();
            }
            if (tempFile != null && tempFile.exists()) {
                tempFile.delete();
            }
        }
    }

    private IntlInspectionTaskQuery convert2Query(IntlInspectionTaskRequest request) {
        IntlInspectionTaskQuery query = new IntlInspectionTaskQuery();
        query.setRegionList(request.getRegionList());
        query.setCountryList(request.getCountryList());
        query.setPageNum(request.getPageNum());
        query.setPageSize(request.getPageSize());
        return query;
    }

}
