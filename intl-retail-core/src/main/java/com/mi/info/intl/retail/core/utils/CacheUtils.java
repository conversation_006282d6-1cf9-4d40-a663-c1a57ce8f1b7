package com.mi.info.intl.retail.core.utils;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;

import javax.annotation.PostConstruct;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.data.redis.core.BoundHashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

@Component
public class CacheUtils implements ApplicationContextAware {
    private static final Logger LOG = LoggerFactory.getLogger(CacheUtils.class);

    // 配置常量
    private static final String DEFAULT_KEY_PREFIX = "intl-retail:";
    private static final long DEFAULT_EXPIRE_TIME = 86400000L; // 1天
    private static final String REDIS_TEMPLATE_BEAN_NAME = "redisTemplate";
    private static final String CONFIG_PREFIX = "spring.cache.redis";
    // 用于Spring注入的非静态字段
    @Value("${" + CONFIG_PREFIX + ".key-prefix:" + DEFAULT_KEY_PREFIX + "}")
    private String keyPrefixConfig;
    @Value("${" + CONFIG_PREFIX + ".time-to-live:" + DEFAULT_EXPIRE_TIME + "}")
    private long expireTimeConfig;

    /**
     * 获取默认过期时间
     *
     * @return 默认过期时间（毫秒）
     */
    private static long getDefaultExpireTime() {
        return CacheHolder.expireTime;
    }

    /**
     * 获取默认键前缀
     *
     * @return 默认键前缀
     */
    private static String getDefaultKeyPrefix() {
        return CacheHolder.keyPrefix;
    }

    public static Cache getCache(String cacheName) {
        if (cacheName == null) {
            LOG.warn("cacheName cannot be null");
            return null;
        }

        CacheManager manager = CacheHolder.getCacheManager();
        if (manager == null) {
            return null;
        }

        try {
            return manager.getCache(cacheName);
        } catch (Exception e) {
            LOG.error("Failed to get cache for cacheName {}: {}", cacheName, e.getMessage());
            return null;
        }
    }

    // 获取缓存值
    public static <T> T get(String cacheName, Object key, Class<T> clazz) {
        if (cacheName == null || key == null || clazz == null) {
            LOG.warn("Invalid parameters: cacheName={}, key={}, clazz={}", cacheName, key, clazz);
            return null;
        }
        Cache cache = getCache(cacheName);
        if (cache == null) {
            LOG.warn("Cache not found for cacheName: {}", cacheName);
            return null;
        }
        try {
            return cache.get(key, clazz);
        } catch (ClassCastException e) {
            LOG.error("Failed to cast cache value to type {}: {}", clazz.getName(), e.getMessage());
            return null;
        } catch (Exception e) {
            LOG.error("Failed to get cache value: {}", e.getMessage());
            return null;
        }
    }

    // 存入缓存
    public static void put(String cacheName, Object key, Object value) {
        if (cacheName == null || key == null) {
            LOG.warn("Invalid parameters: cacheName={}, key={}", cacheName, key);
            return;
        }
        Cache cache = getCache(cacheName);
        if (cache != null) {
            try {
                cache.put(key, value);
                LOG.debug("Put cache value for cacheName: {}, key: {}", cacheName, key);
            } catch (Exception e) {
                LOG.error("Failed to put cache value: {}", e.getMessage());
            }
        } else {
            LOG.warn("Cache not found for cacheName: {}", cacheName);
        }
    }

    /**
     * 删除缓存
     *
     * @param cacheName 缓存名称
     * @param key 缓存key
     */
    public static void removeCache(String cacheName, Object key) {
        if (key == null) {
            LOG.warn("Invalid parameters: key= {}", key);
            return;
        }
        Cache cache = getCache(cacheName);
        if (cache != null) {
            try {
                cache.evict(key);
            } catch (Exception e) {
                LOG.error("Failed to remove cache value: {}", e.getMessage());
            }
        }
    }

    // 删除缓存
    public static void evict(String cacheName, Object key) {
        if (cacheName == null || key == null) {
            LOG.warn("Invalid parameters: cacheName={}, key={}", cacheName, key);
            return;
        }
        Cache cache = getCache(cacheName);
        if (cache != null) {
            try {
                cache.evict(key);
                LOG.debug("Evicted cache value for cacheName: {}, key: {}", cacheName, key);
            } catch (Exception e) {
                LOG.error("Failed to evict cache value: {}", e.getMessage());
            }
        } else {
            LOG.warn("Cache not found for cacheName: {}", cacheName);
        }
    }

    /**
     * 原生 Redis Hash：获取指定字段
     */
    public static <T> T hget(String key, Object hashKey, Class<T> clazz) {
        if (key == null || hashKey == null || clazz == null) {
            LOG.warn("Invalid parameters: key={}, hashKey={}, clazz={}", key, hashKey, clazz);
            return null;
        }

        RedisTemplate<String, Object> template = CacheHolder.getRedisTemplate();
        if (template == null) {
            return null;
        }

        try {
            Object val = template.opsForHash().get(key, hashKey);
            return castValue(val, clazz);
        } catch (Exception e) {
            LOG.error("Failed to get hash value: {}", e.getMessage());
            return null;
        }
    }

    public static <T> List<T> hMultiGet(String key, Collection<?> hashKeys, Class<T> clazz) {
        if (key == null || hashKeys == null || clazz == null) {
            LOG.warn("Invalid parameters: key={}, hashKeys={}, clazz={}", key, hashKeys, clazz);
            return Collections.emptyList();
        }
        if (hashKeys.isEmpty()) {
            LOG.warn("Empty hashKeys collection");
            return Collections.emptyList();
        }
        RedisTemplate<String, Object> template = CacheHolder.getRedisTemplate();
        if (template == null) {
            return Collections.emptyList();
        }
        try {
            List<Object> values = template.opsForHash().multiGet(key, (Collection<Object>) hashKeys);
            return convertList(values, clazz);
        } catch (Exception e) {
            LOG.error("Redis operation failed: {}", e.getMessage());
            return Collections.emptyList();
        }
    }

    /**
     * 批量设置Hash缓存
     *
     * @param key 键
     * @param map 对应多个键值
     * @return true成功 false失败
     */
    public static void hputAll(String key, Map<String, Object> map) {
        if (key == null || map == null) {
            LOG.warn("Invalid parameters: key={}, map={}", key, map);
            return;
        }
        RedisTemplate<String, Object> template = CacheHolder.getRedisTemplate();
        if (template != null) {
            try {
                template.opsForHash().putAll(key, map);
                LOG.debug("Put all hash values for key: {}", key);
            } catch (Exception e) {
                LOG.error("Failed to put all hash values: {}", e.getMessage());
            }
        }
    }

    /**
     * 批量设置Hash缓存，默认过期时间1天
     *
     * @param key 键
     * @param map 对应多个键值
     * @return true成功 false失败
     */
    public static boolean hputAllWithDefaultExpire(String key, Map<String, Object> map) {
        try {
            return hputAllWithExpire(key, map, getDefaultExpireTime(), TimeUnit.MILLISECONDS);
        } catch (Exception e) {
            LOG.error("hputAll error, key: {}, map: {}", key, map, e);
            return false;
        }
    }

    /**
     * 批量设置Hash缓存（带过期时间）
     *
     * @param key 键
     * @param map 对应多个键值
     * @param timeout 过期时间
     * @param unit 时间单位
     * @return true成功 false失败
     */
    public static boolean hputAllWithExpire(String key, Map<String, Object> map, long timeout, TimeUnit unit) {
        if (key == null || map == null || unit == null) {
            LOG.warn("Invalid parameters: key={}, map={}, unit={}", key, map, unit);
            return false;
        }
        if (timeout <= 0) {
            LOG.warn("Invalid timeout value: {}", timeout);
            return false;
        }
        RedisTemplate<String, Object> template = CacheHolder.getRedisTemplate();
        if (template == null) {
            LOG.warn("RedisTemplate is null");
            return false;
        }
        try {
            // 1. 检查 Key 是否已存在
            if (!template.hasKey(key)) {
                // 2. 如果 Key 不存在，则设置 Hash 字段和过期时间
                BoundHashOperations<String, Object, Object> ops = template.boundHashOps(key);
                ops.putAll(map);
                ops.expire(timeout, unit);
                LOG.debug("Initialized hash with expire for key: {}, timeout: {} {}", key, timeout, unit);
            } else {
                // 3. 如果 Key 存在，仅更新 Hash 字段，不设置过期时间
                BoundHashOperations<String, Object, Object> ops = template.boundHashOps(key);
                ops.putAll(map);
                LOG.debug("Updated hash without expire for key: {}", key);
            }
            return true;
        } catch (Exception e) {
            LOG.error("Failed to put all hash values with expire: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 原生 Redis Hash：设置单个字段
     */
    public static void hput(String key, Object hashKey, Object value) {
        if (key == null || hashKey == null) {
            LOG.warn("Invalid parameters: key={}, hashKey={}", key, hashKey);
            return;
        }
        RedisTemplate<String, Object> template = CacheHolder.getRedisTemplate();
        if (template != null) {
            try {
                template.opsForHash().put(key, hashKey, value);
                LOG.debug("Put hash value for key: {}, hashKey: {}", key, hashKey);
            } catch (Exception e) {
                LOG.error("Failed to put hash value: {}", e.getMessage());
            }
        } else {
            LOG.warn("RedisTemplate is null");
        }
    }

    /**
     * 原生 Redis Hash：删除一个或多个字段
     */
    public static void hdel(String key, Object... hashKeys) {
        if (key == null || hashKeys == null) {
            LOG.warn("Invalid parameters: key={}, hashKey={}", key, hashKeys);
            return;
        }
        RedisTemplate<String, Object> template = CacheHolder.getRedisTemplate();
        if (template != null) {
            try {
                template.opsForHash().delete(key, hashKeys);
                LOG.debug("Deleted hash value for key: {}, hashKey: {}", key, hashKeys);
            } catch (Exception e) {
                LOG.error("Failed to delete hash value: {}", e.getMessage());
            }
        } else {
            LOG.warn("RedisTemplate is null");
        }
    }

    // 工具方法：参数验证
    private static boolean validateParams(Object... params) {
        for (Object param : params) {
            if (param == null) {
                LOG.warn("Invalid parameter: null");
                return false;
            }
        }
        return true;
    }

    // 工具方法：类型转换
    private static <T> T castValue(Object value, Class<T> clazz) {
        if (value == null) {
            return null;
        }
        try {
            return clazz.cast(value);
        } catch (ClassCastException e) {
            LOG.error("Failed to cast value [{}] to type {}: {}", value, clazz.getName(), e.getMessage());
            return null;
        }
    }

    // 工具方法：列表转换
    private static <T> List<T> convertList(List<Object> values, Class<T> clazz) {
        if (values == null || values.isEmpty()) {
            return Collections.emptyList();
        }

        List<T> results = new ArrayList<>(values.size());
        for (Object value : values) {
            results.add(castValue(value, clazz));
        }
        return results;
    }

    @PostConstruct
    private void initConfig() {
        CacheHolder.updateConfig(keyPrefixConfig, expireTimeConfig);
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) {
        try {
            // 初始化 CacheManager
            CacheHolder.initCacheManager(applicationContext.getBean(CacheManager.class));

            // 初始化 RedisTemplate
            Object template = applicationContext.getBean(REDIS_TEMPLATE_BEAN_NAME);
            if (template instanceof RedisTemplate) {
                @SuppressWarnings("unchecked")
                RedisTemplate<String, Object> redisTemplate = (RedisTemplate<String, Object>) template;
                CacheHolder.initRedisTemplate(redisTemplate);
            } else {
                LOG.error("RedisTemplate bean is not of expected type");
            }
        } catch (Exception e) {
            LOG.error("Failed to initialize CacheUtils: {}", e.getMessage());
        }
    }

    // 使用静态内部类实现线程安全的延迟加载
    private static class CacheHolder {
        private static final AtomicReference<CacheManager> CACHE_MANAGER_REF = new AtomicReference<>();
        private static final AtomicReference<RedisTemplate<String, Object>> REDIS_TEMPLATE_REF =
            new AtomicReference<>();
        private static volatile String keyPrefix = DEFAULT_KEY_PREFIX;
        private static volatile long expireTime = DEFAULT_EXPIRE_TIME;

        private static void initCacheManager(CacheManager manager) {
            if (CACHE_MANAGER_REF.compareAndSet(null, manager)) {
                LOG.info("CacheManager initialized successfully");
            }
        }

        private static void initRedisTemplate(RedisTemplate<String, Object> template) {
            if (REDIS_TEMPLATE_REF.compareAndSet(null, template)) {
                LOG.info("RedisTemplate initialized successfully");
            }
        }

        private static synchronized void updateConfig(String prefix, long time) {
            keyPrefix = prefix;
            expireTime = time;
            LOG.info("Cache configuration updated - prefix: {}, expireTime: {}", prefix, time);
        }

        private static CacheManager getCacheManager() {
            CacheManager manager = CACHE_MANAGER_REF.get();
            if (manager == null) {
                LOG.warn("CacheManager is not initialized yet");
            }
            return manager;
        }

        private static RedisTemplate<String, Object> getRedisTemplate() {
            RedisTemplate<String, Object> template = REDIS_TEMPLATE_REF.get();
            if (template == null) {
                LOG.warn("RedisTemplate is not initialized yet");
            }
            return template;
        }
    }
}
