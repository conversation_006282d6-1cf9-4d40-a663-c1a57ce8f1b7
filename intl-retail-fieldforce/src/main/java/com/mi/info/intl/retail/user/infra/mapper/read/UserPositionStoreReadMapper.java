package com.mi.info.intl.retail.user.infra.mapper.read;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mi.info.intl.retail.user.infra.entity.UserPositionStore;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface UserPositionStoreReadMapper extends BaseMapper<UserPositionStore> {

    List<UserPositionStore> getUserPositionStore(@Param("country") String country,
            @Param("titles") List<Integer> titles, @Param("storeGrades") List<String> storeGrades, @Param("midList") List<Long> midList);

    /**
     * 查询促销员用户阵地信息（新的MySQL查询逻辑）
     *
     * @param region 国家短代码（必填）
     * @param titleCodeList 职位代码列表
     * @param retailerCodeList 零售商代码列表
     * @param positionTypeList 阵地类型列表
     * @param positionCodeList 阵地代码列表
     * @param channelTypeList 渠道类型列表
     * @param pageNum 页码（从1开始）
     * @param pageSize 每页数量
     * @return 促销员用户阵地信息列表
     */
    List<UserPositionStore> getPromoterUserPositions(@Param("region") String region,
            @Param("titleCodeList") List<Integer> titleCodeList,
            @Param("retailerCodeList") List<String> retailerCodeList,
            @Param("positionTypeList") List<Integer> positionTypeList,
            @Param("positionCodeList") List<String> positionCodeList,
            @Param("channelTypeList") List<Integer> channelTypeList,
            @Param("pageNum") Integer pageNum,
            @Param("pageSize") Integer pageSize);

    /**
     * 统计促销员用户阵地信息总数（用于分页）
     *
     * @param region 国家短代码（必填）
     * @param titleCodeList 职位代码列表
     * @param retailerCodeList 零售商代码列表
     * @param positionTypeList 阵地类型列表
     * @param positionCodeList 阵地代码列表
     * @param channelTypeList 渠道类型列表
     * @return 总记录数
     */
    Integer countPromoterUserPositions(@Param("region") String region,
            @Param("titleCodeList") List<Integer> titleCodeList,
            @Param("retailerCodeList") List<String> retailerCodeList,
            @Param("positionTypeList") List<Integer> positionTypeList,
            @Param("positionCodeList") List<String> positionCodeList,
            @Param("channelTypeList") List<Integer> channelTypeList);

    /**
     * 根据midList查询促销员用户阵地信息（当midList有值时使用）
     *
     * @param region 国家短代码（必填）
     * @param midList MI ID列表
     * @param pageNum 页码（从1开始）
     * @param pageSize 每页数量
     * @return 促销员用户阵地信息列表
     */
    List<UserPositionStore> getPromoterUserPositionsByMidList(@Param("region") String region,
            @Param("midList") List<String> midList,
            @Param("pageNum") Integer pageNum,
            @Param("pageSize") Integer pageSize);

    /**
     * 根据midList统计促销员用户阵地信息总数
     *
     * @param region 国家短代码（必填）
     * @param midList MI ID列表
     * @return 总记录数
     */
    Integer countPromoterUserPositionsByMidList(@Param("region") String region,
            @Param("midList") List<String> midList);

}
