package com.mi.info.intl.retail.user.domain;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.mi.info.intl.retail.cooperation.task.config.InspectionConfig;
import com.mi.info.intl.retail.cooperation.task.infra.entity.IntlInspectionTaskConf;
import com.mi.info.intl.retail.cooperation.task.infra.mapper.read.IntlInspectionTaskConfReadMapper;
import com.mi.info.intl.retail.intlretail.service.api.store.dto.BusinessDataInputRequest;
import com.mi.info.intl.retail.intlretail.service.api.store.dto.BusinessDataResponse;
import com.mi.info.intl.retail.intlretail.service.api.store.dto.PositionInfoDto;
import com.mi.info.intl.retail.user.constant.PositionTypeEnum;
import com.mi.info.intl.retail.user.constant.StoreGradeEnum;
import com.mi.info.intl.retail.user.constant.UserTitleEnum;
import com.mi.info.intl.retail.user.infra.entity.UserPositionStore;
import com.mi.info.intl.retail.user.infra.mapper.read.UserPositionStoreReadMapper;
import com.mi.info.intl.retail.utils.JsonUtil;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.AbstractMap;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 用户领域服务
 *
 * <AUTHOR>
 * @date  2025/06/09
 */
@Service
public class UserInfoManager {

    @Autowired
    private UserPositionStoreReadMapper userPositionStoreReadMapper;

    @Autowired
    private IntlInspectionTaskConfReadMapper intlInspectionTaskConfReadMapper;

    @Autowired
    private InspectionConfig inspectionConfig;

    // 缓存门店巡检频次映射，避免重复构建
    private final Map<Integer, Map<StoreGradeEnum, String>> storeInspectionFrequencyCache = new HashMap<>();

    /**
     * 构建门店巡检频次映射表
     *
     * @param taskConf 巡检任务配置
     * @return 门店等级到巡检频次的映射
     */
    private Map<StoreGradeEnum, String> buildStoreInspectionFrequencyMap(IntlInspectionTaskConf taskConf) {
        // 使用任务配置ID作为缓存键
        Integer cacheKey = taskConf.getId();

        // 先从缓存中获取
        Map<StoreGradeEnum, String> cachedMap = storeInspectionFrequencyCache.get(cacheKey);
        if (cachedMap != null) {
            return cachedMap;
        }

        // 构建新的映射表
        Map<StoreGradeEnum, String> frequencyMap = new HashMap<>();
        frequencyMap.put(StoreGradeEnum.S, taskConf.getSStoreInspectionFrequency());
        frequencyMap.put(StoreGradeEnum.A, taskConf.getAStoreInspectionFrequency());
        frequencyMap.put(StoreGradeEnum.B, taskConf.getBStoreInspectionFrequency());
        frequencyMap.put(StoreGradeEnum.C, taskConf.getCStoreInspectionFrequency());
        frequencyMap.put(StoreGradeEnum.D, taskConf.getDStoreInspectionFrequency());

        // 存入缓存
        storeInspectionFrequencyCache.put(cacheKey, frequencyMap);

        return frequencyMap;
    }

    /**
     * 根据条件获取用户数据及关联的阵地信息(督导任务体系)
     *
     * @param userInfoRequestDTO 查询条件(国家/职位)
     * @return 用户信息列表
     */
    public List<BusinessDataResponse> getUserPositions(BusinessDataInputRequest userInfoRequestDTO) {
        // 初始化返回列表
        List<BusinessDataResponse> responseList = new ArrayList<>();

        // 参数校验，region不能为空, titleCodeList必须有数据, storeGradeList不能为空, PositionTypeList不能为空
        if (userInfoRequestDTO == null || userInfoRequestDTO.getRegion() == null
                || userInfoRequestDTO.getTitleCodeList() == null || userInfoRequestDTO.getTitleCodeList().isEmpty()
                || userInfoRequestDTO.getStoreGradeList() == null || userInfoRequestDTO.getPositionTypeList() == null) {
            return responseList; // 如果请求参数不完整，直接返回空列表
        }
        // 将组织中台的titleCode, 转为RMS的code, 比如246需转换为500900001, ORG_RMS_TITLE_MAP中不包含该titleCode则不转换
        List<Integer> titleCodeList = userInfoRequestDTO.getTitleCodeList().stream()
                .map(titleCode -> UserTitleEnum.ORG_RMS_TITLE_MAP.getOrDefault(titleCode, titleCode))
                .collect(Collectors.toList());

        // 根据国家短代码查询所有可用的任务配置表IntlInspectionTaskConf
        LambdaQueryWrapper<IntlInspectionTaskConf> lambdaQuery = Wrappers.lambdaQuery();
        lambdaQuery.eq(IntlInspectionTaskConf::getCountryCode, userInfoRequestDTO.getRegion());
        lambdaQuery.eq(IntlInspectionTaskConf::getIsDisabled, 0);

        List<IntlInspectionTaskConf> taskConfs = intlInspectionTaskConfReadMapper.selectList(lambdaQuery);
        if (taskConfs == null || taskConfs.isEmpty()) {
            return responseList; // 如果没有任务配置，直接返回空列表
        }

        // 获取灰度名单
        List<Long> midGrayList = inspectionConfig.getMidGrayList();

        // 查询符合条件的用户
        List<UserPositionStore> userPositions = userPositionStoreReadMapper.getUserPositionStore(
                userInfoRequestDTO.getRegion(), titleCodeList,
                userInfoRequestDTO.getStoreGradeList(), midGrayList);
        if (userPositions.isEmpty()) {
            return responseList; // 如果没有符合条件的用户，直接返回空列表
        }
        // 根据用户id、门店id分组, 遍历所有分组, 判断是否最优售点
        userPositions.stream()
                .collect(Collectors.groupingBy(up -> new AbstractMap.SimpleEntry<>(up.getMid(), up.getStoreId())))
                .forEach((key, group) -> {
                    // 获取分组中第一条数据
                    UserPositionStore firstUPS = group.get(0);
                    Integer titleCode = firstUPS.getUserTitle();
                    Integer titleCodeReturn;
                    // 转换为组织中台code
                    Integer titleCodeORG = UserTitleEnum.RMS_ORG_TITLE_MAP.getOrDefault(firstUPS.getUserTitle(), firstUPS.getUserTitle());
                    // 返回的code需要和传入的code一致
                    if (!userInfoRequestDTO.getTitleCodeList().contains(titleCode)) {
                        titleCodeReturn = titleCodeORG;
                    }
                    else {
                        titleCodeReturn = titleCode;
                    }
                    // 匹配对应职位的配置表
                    IntlInspectionTaskConf taskConf = taskConfs.stream()
                            .filter(t -> t.getUserTitleCodes().contains(titleCode.toString()))
                            .findFirst().orElse(null);
                    if (taskConf == null) {
                        return; // 如果没有匹配的配置表，直接跳过该用户
                    }

                    // 构造StoreInspectionFrequency字典
                    Map<StoreGradeEnum, String> storeInspectionFrequencyMap = buildStoreInspectionFrequencyMap(taskConf);

                    // 判断是否最优售点, 遍历售点类型（预定义静态数组，按优先级降序排序：SIS>ES>DZ>DC>POS），获取当前分组中对应售点类型的售点
                    for (PositionTypeEnum positionType : PositionTypeEnum.values()) {
                        // 获取当前分组中对应售点类型的售点
                        List<UserPositionStore> positionStores = group.stream()
                                .filter(up -> up.getPositionType().equals(positionType.getCode()))
                                .collect(Collectors.toList());
                        if (!positionStores.isEmpty()) {
                            // 判断售点类型是否包含于请求参数中的PositionTypeList
                            if (!userInfoRequestDTO.getPositionTypeList().contains(positionType.getCode())) {
                                break; // 如果请求参数中不包含该售点类型，则该门店下最优售点不符合要求，跳出循环
                            }
                            // 如果存在该售点类型的售点，则创建响应对象并添加到返回列表
                            BusinessDataResponse response = new BusinessDataResponse();
                            response.setMid(key.getKey());
                            response.setName(firstUPS.getUserName());
                            response.setAreaId(firstUPS.getCountryCode());
                            // 构造extraInfo
                            PositionInfoDto.PositionExtInfoDTO extInfo = new PositionInfoDto.PositionExtInfoDTO();
                            extInfo.setStoreId(key.getValue()); // 设置storeId, key的value
                            extInfo.setStoreGrade(firstUPS.getStoreGradeName());
                            extInfo.setStoreCode(firstUPS.getStoreCode());
                            extInfo.setUserTitle(titleCodeORG);
                            extInfo.setTimezoneCode(firstUPS.getTimezoneCode());
                            extInfo.setLanguageCode(firstUPS.getLanguageCode());
                            extInfo.setTaskConfId(taskConf.getId());
                            extInfo.setFrequency(storeInspectionFrequencyMap
                                    .get(StoreGradeEnum.getByCode(firstUPS.getStoreGrade())));
                            // 根据阵地类型(是否pos)设置检查时间
                            if (positionType == PositionTypeEnum.POS) {
                                extInfo.setInspectionTime(taskConf.getPosInspectionTime());
                            } else {
                                extInfo.setInspectionTime(taskConf.getFrontInspectionTime());
                            }
                            // 转换为JSON字符串
                            String extInfoJson = JsonUtil.bean2json(extInfo);

                            // 根据传入参数筛选有促无促售点(0无促, 1有促), 构造positionList
                            List<PositionInfoDto> positionList = positionStores.stream().filter(p -> {
                                if (userInfoRequestDTO.getIsPromotion() != null) {
                                    return userInfoRequestDTO.getIsPromotion().equals(p.getIsPromotion());
                                }
                                return true;
                            }).map(p -> {
                                PositionInfoDto positionInfo = new PositionInfoDto();
                                positionInfo.setPositionCode(p.getPositionCode());
                                positionInfo.setPositionName(p.getPositionName());
                                positionInfo.setTitleCode(titleCodeReturn);
                                positionInfo.setTitleName(firstUPS.getUserTitleName());
                                positionInfo.setExtInfo(extInfoJson);
                                return positionInfo;
                            }).collect(Collectors.toList());
                            // positionList有值时
                            if (!positionList.isEmpty()) {
                                response.setPositionList(positionList);
                                responseList.add(response);
                            }
                            break; // 找到最优售点后跳出循环
                        }
                    }
                });

        return responseList;
    }

    /**
     * 根据条件获取促销员用户数据及关联的阵地信息(新的MySQL查询逻辑)
     *
     * @param userInfoRequestDTO 查询条件
     * @return 用户信息列表
     */
    public List<BusinessDataResponse> getPromoterUserPositions(BusinessDataInputRequest userInfoRequestDTO) {
        // 初始化返回列表
        List<BusinessDataResponse> responseList = new ArrayList<>();

        // 参数校验，region不能为空
        if (userInfoRequestDTO == null || userInfoRequestDTO.getRegion() == null) {
            return responseList;
        }

        List<UserPositionStore> userPositions;

        // 判断是否有midList，如果有则使用专门的midList查询
        if (userInfoRequestDTO.getMidList() != null && !userInfoRequestDTO.getMidList().isEmpty()) {
            userPositions = userPositionStoreReadMapper.getPromoterUserPositionsByMidList(
                    userInfoRequestDTO.getRegion(),
                    userInfoRequestDTO.getMidList(),
                    userInfoRequestDTO.getPageNum(),
                    userInfoRequestDTO.getPageSize()
            );
        } else {
            // 使用常规查询
            userPositions = userPositionStoreReadMapper.getPromoterUserPositions(
                    userInfoRequestDTO.getRegion(),
                    userInfoRequestDTO.getTitleCodeList(),
                    userInfoRequestDTO.getRetailerCodeList(),
                    userInfoRequestDTO.getPositionTypeList(),
                    userInfoRequestDTO.getPositionCodeList(),
                    userInfoRequestDTO.getChannelTypeList(),
                    userInfoRequestDTO.getPageNum(),
                    userInfoRequestDTO.getPageSize()
            );
        }

        if (userPositions == null || userPositions.isEmpty()) {
            return responseList;
        }

        // 按用户ID分组处理数据
        Map<String, List<UserPositionStore>> userGroupMap = userPositions.stream()
                .collect(Collectors.groupingBy(UserPositionStore::getMid));

        // 构建返回结果
        for (Map.Entry<String, List<UserPositionStore>> entry : userGroupMap.entrySet()) {
            List<UserPositionStore> userPositionList = entry.getValue();
            if (userPositionList.isEmpty()) {
                continue;
            }

            UserPositionStore firstPosition = userPositionList.get(0);

            // 创建用户响应对象
            BusinessDataResponse response = new BusinessDataResponse();
            response.setMid(firstPosition.getMid());
            response.setName(firstPosition.getUserName());
            response.setAreaId(firstPosition.getAreaId());

            // 构建阵地信息列表
            List<PositionInfoDto> positionList = userPositionList.stream()
                    .map(position -> {
                        PositionInfoDto positionInfo = new PositionInfoDto();
                        positionInfo.setPositionCode(position.getPositionCode());
                        positionInfo.setPositionName(position.getPositionName());
                        positionInfo.setTitleCode(position.getUserTitle());
                        positionInfo.setTitleName(position.getUserTitleName());

                        // 设置渠道类型信息
                        if (position.getChannelTypeId() != null) {
                            positionInfo.setChannelTypeId(position.getChannelTypeId().toString());
                        }
                        positionInfo.setChannelTypeName(position.getChannelTypeName());

                        // 构建扩展信息
                        PositionInfoDto.PositionExtInfoDTO extInfo = new PositionInfoDto.PositionExtInfoDTO();
                        extInfo.setStoreId(position.getStoreId());
                        extInfo.setLanguageCode(position.getLanguageCode());

                        // 转换为JSON字符串
                        String extInfoJson = JsonUtil.bean2json(extInfo);
                        positionInfo.setExtInfo(extInfoJson);

                        return positionInfo;
                    })
                    .collect(Collectors.toList());

            response.setPositionList(positionList);
            responseList.add(response);
        }

        return responseList;
    }
}
