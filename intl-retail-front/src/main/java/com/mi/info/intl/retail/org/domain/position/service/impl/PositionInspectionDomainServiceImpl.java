package com.mi.info.intl.retail.org.domain.position.service.impl;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ZipUtil;
import cn.hutool.http.HttpUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.mi.info.intl.retail.cooperation.task.domain.RmsCountryTimezoneService;
import com.mi.info.intl.retail.cooperation.task.dto.dto.IntlRmsUserDto;
import com.mi.info.intl.retail.cooperation.task.inspection.IntlRmsUserService;
import com.mi.info.intl.retail.core.utils.IntlRetailAssert;
import com.mi.info.intl.retail.exception.RetailRunTimeException;
import com.mi.info.intl.retail.intlretail.infra.utils.CommonUtils;
import com.mi.info.intl.retail.intlretail.service.api.position.dto.FurniturePhotoGroup;
import com.mi.info.intl.retail.intlretail.service.api.position.dto.ImageCenterDto;
import com.mi.info.intl.retail.intlretail.service.api.position.dto.ImageFds;
import com.mi.info.intl.retail.intlretail.service.api.position.dto.InitInspectionByPositionCodeRequest;
import com.mi.info.intl.retail.intlretail.service.api.position.dto.InspectionSummaryDTO;
import com.mi.info.intl.retail.intlretail.service.api.position.dto.OptionalItem;
import com.mi.info.intl.retail.intlretail.service.api.position.dto.PhotoGroup;
import com.mi.info.intl.retail.intlretail.service.api.position.dto.PositionFurnitureRequest;
import com.mi.info.intl.retail.intlretail.service.api.position.dto.PositionImgBatchUploadRequest;
import com.mi.info.intl.retail.intlretail.service.api.position.dto.PositionInspectionAllDetailDTO;
import com.mi.info.intl.retail.intlretail.service.api.position.dto.PositionInspectionApproveRequest;
import com.mi.info.intl.retail.intlretail.service.api.position.dto.PositionInspectionDetailRequest;
import com.mi.info.intl.retail.intlretail.service.api.position.dto.PositionInspectionDetailResponse;
import com.mi.info.intl.retail.intlretail.service.api.position.dto.PositionInspectionHistoryItem;
import com.mi.info.intl.retail.intlretail.service.api.position.dto.PositionInspectionItem;
import com.mi.info.intl.retail.intlretail.service.api.position.dto.PositionInspectionRequest;
import com.mi.info.intl.retail.intlretail.service.api.position.dto.PositionInspectionSubmitRequest;
import com.mi.info.intl.retail.intlretail.service.api.position.dto.PositionItemListRequest;
import com.mi.info.intl.retail.intlretail.service.api.position.dto.PositionSelectorItemList;
import com.mi.info.intl.retail.intlretail.service.api.position.dto.TaskCenterChangeExecutorReq;
import com.mi.info.intl.retail.intlretail.service.api.position.dto.TaskCenterFinishReq;
import com.mi.info.intl.retail.intlretail.service.api.position.dto.TaskCenterNoNeedCompleteReq;
import com.mi.info.intl.retail.intlretail.service.api.position.dto.TaskCenterPushTaskReq;
import com.mi.info.intl.retail.intlretail.service.api.position.dto.UnRemindedTaskDTO;
import com.mi.info.intl.retail.intlretail.service.api.position.dto.UploadData;
import com.mi.info.intl.retail.intlretail.service.api.position.enums.AbnormalReasonEnum;
import com.mi.info.intl.retail.intlretail.service.api.position.enums.DisapproveReasonEnum;
import com.mi.info.intl.retail.intlretail.service.api.position.enums.I18nDesc;
import com.mi.info.intl.retail.intlretail.service.api.position.enums.InspectionBusinessTypeEnum;
import com.mi.info.intl.retail.intlretail.service.api.position.enums.InspectionStatusEnum;
import com.mi.info.intl.retail.intlretail.service.api.position.enums.PositionStageEnum;
import com.mi.info.intl.retail.intlretail.service.api.position.enums.StoreLimitedRangeEnum;
import com.mi.info.intl.retail.intlretail.service.api.position.enums.TaskStatusEnum;
import com.mi.info.intl.retail.intlretail.service.api.position.enums.VerifyActionEnum;
import com.mi.info.intl.retail.intlretail.service.api.proxy.dto.taskcenter.TaskCenterFinishTaskReq;
import com.mi.info.intl.retail.intlretail.service.api.store.dto.PositionInspectionResponsiblePersonDTO;
import com.mi.info.intl.retail.intlretail.service.api.upload.FileUploadInfo;
import com.mi.info.intl.retail.ldu.enums.FileUploadEnum;
import com.mi.info.intl.retail.ldu.service.IntlFileUploadService;
import com.mi.info.intl.retail.model.CommonApiResponse;
import com.mi.info.intl.retail.org.app.event.PositionImageBatchUploadEvent;
import com.mi.info.intl.retail.org.app.event.UpdateApprovalInfo;
import com.mi.info.intl.retail.org.app.event.UpdateApprovalStatusEvent;
import com.mi.info.intl.retail.org.domain.CalculateSphericalDistance;
import com.mi.info.intl.retail.org.domain.InspectionHistoryDomain;
import com.mi.info.intl.retail.org.domain.InspectionRecordDomain;
import com.mi.info.intl.retail.org.domain.PositionCommonItemList;
import com.mi.info.intl.retail.org.domain.PositionDomain;
import com.mi.info.intl.retail.org.domain.RuleConfigDomain;
import com.mi.info.intl.retail.org.domain.dto.InspectionRecordDTO;
import com.mi.info.intl.retail.org.domain.dto.Multimedia;
import com.mi.info.intl.retail.org.domain.enums.OperationType;
import com.mi.info.intl.retail.org.domain.position.service.PositionInspectionDomainService;
import com.mi.info.intl.retail.org.domain.repository.InspectionHistoryRepository;
import com.mi.info.intl.retail.org.domain.repository.InspectionRecordRepository;
import com.mi.info.intl.retail.org.domain.repository.PositionInspectionRepository;
import com.mi.info.intl.retail.org.domain.repository.PositionRepository;
import com.mi.info.intl.retail.org.domain.repository.RuleConfigRepository;
import com.mi.info.intl.retail.org.domain.util.BeanConverter;
import com.mi.info.intl.retail.org.domain.util.CountryTimeUtil;
import com.mi.info.intl.retail.org.domain.util.RpcUtil;
import com.mi.info.intl.retail.org.infra.entity.ImageLocal;
import com.mi.info.intl.retail.org.infra.entity.InspectionRecord;
import com.mi.info.intl.retail.org.infra.entity.PositionImageInfo;
import com.mi.info.intl.retail.org.infra.mapper.read.InspectionRecordReadMapper;
import com.mi.info.intl.retail.org.infra.mapper.read.PositionResponsiblePersonMapper;
import com.mi.info.intl.retail.org.infra.rpc.StoreRelateRpc;
import com.mi.info.intl.retail.org.infra.rpc.TaskCenterServiceRpc;
import com.mi.info.intl.retail.user.constant.PositionTypeEnum;
import com.mi.info.intl.retail.utils.JsonUtil;
import com.xiaomi.cnzone.brain.platform.api.model.req.app.CreateInstanceReq;
import com.xiaomi.cnzone.brain.platform.api.model.resp.app.CreateInstanceResp;
import com.xiaomi.cnzone.maindataapi.api.PositionProvider;
import com.xiaomi.cnzone.maindataapi.model.dto.store.ListPositionInfoResponse;
import com.xiaomi.cnzone.maindataapi.model.dto.store.PositionListResponse;
import com.xiaomi.cnzone.maindataapi.model.req.store.ListPositionInfoRequest;
import com.xiaomi.cnzone.maindataapi.model.req.store.PositionListRequest;
import com.xiaomi.cnzone.storeapi.api.channelbuild.common.ChannelFurnitureProvider;
import com.xiaomi.cnzone.storeapi.api.channelbuild.position.BuildChannelPositionProvider;
import com.xiaomi.cnzone.storeapi.model.channelbuild.business.req.PositionDataReq;
import com.xiaomi.cnzone.storeapi.model.channelbuild.business.resp.ChannelBaseV1Resp;
import com.xiaomi.cnzone.storeapi.model.channelbuild.business.resp.PositionImageCenterResp;
import com.xiaomi.cnzone.storeapi.model.channelbuild.common.req.FurnitureDetailReq;
import com.xiaomi.cnzone.storeapi.model.channelbuild.common.resp.ConfigFurnitureResp;
import com.xiaomi.nr.global.dev.base.RequestContextInfo;
import com.xiaomi.nr.global.dev.neptune.T;
import com.xiaomi.youpin.infra.rpc.Result;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.mutable.MutableInt;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.rpc.RpcContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.io.File;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

@Slf4j
@Service
public class PositionInspectionDomainServiceImpl implements PositionInspectionDomainService {

    /**
     * 职位优先级顺序
     */
    private static final Map<String, Integer> JOB_PRIORITY = new HashMap<>();

    static {
        JOB_PRIORITY.put("Promoter", 1);
        JOB_PRIORITY.put("Temporary Promoter", 2);
        JOB_PRIORITY.put("Supervisor", 3);
        JOB_PRIORITY.put("Supervisor without promoters", 4);
        JOB_PRIORITY.put("Merchandiser", 5);
    }

    @Autowired
    private InspectionRecordRepository inspectionRecordRepository;
    @Autowired
    private ObjectMapper objectMapper;
    @Resource
    private PositionInspectionRepository positionInspectionRepository;
    @Autowired
    private StoreRelateRpc storeRelateRpc;
    @DubboReference(group = "${dubbo-group.consumer.channelBuild}", version = "1.0", check = false, interfaceClass = PositionProvider.class)
    private PositionProvider positionProvider;
    @Autowired
    private ApplicationEventPublisher eventPublisher;
    @Autowired
    private RuleConfigRepository ruleConfigRepository;
    @Resource
    private PositionRepository positionRepository;
    @Autowired
    private InspectionHistoryRepository inspectionHistoryRepository;
    @Autowired
    private PositionResponsiblePersonMapper positionResponsiblePersonMapper;
    @Autowired
    private CountryTimeUtil countryTimeUtil;
    @Autowired
    private TaskCenterServiceRpc taskCenterServiceRpc;
    @Autowired
    private InspectionRecordReadMapper inspectionRecordReadMapper;
    @Autowired
    private IntlFileUploadService fileUploadService;

    @Resource
    private RmsCountryTimezoneService rmsCountryTimezoneService;

    @DubboReference(group = "${dubbo-group.consumer.channelBuild}", check = false, interfaceClass = ChannelFurnitureProvider.class)
    private ChannelFurnitureProvider channelFurnitureProvider;

    @DubboReference(group = "${dubbo-group.consumer.channelBuild}", timeout = 2000, check = false, interfaceClass = BuildChannelPositionProvider.class)
    private BuildChannelPositionProvider buildChannelPositionProvider;

    @Resource
    private IntlRmsUserService intlRmsUserService;

    public static void downloadAndUnZip(ImageFds imageFds, File targetDir,
                                        Map<String, PositionImageInfo> positionImageInfoMap,
                                        Function<PositionImageInfo, List<ImageLocal>> imageLocalFun, boolean isFurniture) {
        String url = imageFds.getUrl();
        File zipDir = FileUtil.file(targetDir, IdUtil.simpleUUID());
        FileUtil.mkdir(zipDir);
        try {
            HttpUtil.downloadFile(url, zipDir);
            List<String> fileNames = FileUtil.listFileNames(zipDir.getPath());
            IntlRetailAssert.checkArgument(fileNames.size() == 1, T.tr("inspection.zip_file_upload_failed"));
            String zipFileName = fileNames.get(0);
            IntlRetailAssert.checkArgument(zipFileName.endsWith(".zip") || zipFileName.endsWith(".rar"),
                    T.tr("inspection.file_type_only_supports_zip_or_rar"));
            File zipFile = FileUtil.file(zipDir, zipFileName);
            ZipUtil.unzip(zipFile, zipDir);
            FileUtil.del(zipFile);
        } catch (RetailRunTimeException e) {
            throw e;
        } catch (Exception e) {
            log.error("download file failed.", e);
            throw new RetailRunTimeException(T.tr("inspection.zip_file_upload_failed"));
        }
        List<File> unZipFile = FileUtil.loopFiles(zipDir);
        //检测到里面路径包含有__MACOSX直接过滤掉
        unZipFile.removeIf(file -> file.getPath().contains("__MACOSX") || file.getPath().contains(".DS_Store"));
        for (File file : unZipFile) {
            if (!file.isFile()) {
                continue;
            }
            String fatherDir = file.getParentFile().getName();
            String grandfatherDir = Optional.of(file).map(File::getParentFile).map(File::getParentFile)
                    .map(File::getName).orElse(null);
            if (file.isFile()) {
                String positionCode = isFurniture ? grandfatherDir : fatherDir;
                PositionImageInfo positionImageInfo = positionImageInfoMap.computeIfAbsent(positionCode,
                        k -> new PositionImageInfo(positionCode));
                imageLocalFun.apply(positionImageInfo)
                        .add(new ImageLocal(isFurniture ? fatherDir : imageFds.getFileName(), null, file, null));
            }
        }
    }

    /**
     * 检查打卡距离是否在限定范围内
     *
     * @param currentLatitude   当前纬度
     * @param currentLongitude  当前经度
     * @param positionLatitude  阵地纬度
     * @param positionLongitude 阵地经度
     * @param checkInDistance   允许打卡的最大距离
     * @return 是否在打卡范围内
     */
    private StoreLimitedRangeEnum isWithinCheckInRange(Double currentLatitude, Double currentLongitude,
                                                       Double positionLatitude, Double positionLongitude, Double checkInDistance) {
        // 计算距离
        double distance = CalculateSphericalDistance.getDistance(currentLatitude, currentLongitude, positionLatitude,
                positionLongitude);

        // 设置默认打卡范围为200米
        double maxDistance = 200.0;
        if (checkInDistance != null && checkInDistance > 0) {
            maxDistance = checkInDistance;
        }

        // 判断是否在打卡范围内
        boolean isWithinRange = distance <= maxDistance;

        if (!isWithinRange) {
            log.info("超出打卡范围: 当前距离={}, 配置距离={}", distance, maxDistance);
        }

        return isWithinRange ? StoreLimitedRangeEnum.YES : StoreLimitedRangeEnum.NO;
    }

    @Override
    public CommonApiResponse<String> submitPositionInspection(PositionInspectionSubmitRequest request) {
        try {
            // 参数验证
            if (request.getPositionInspectionId() == null) {
                log.error("The request parameters are incomplete");
                return new CommonApiResponse<>(500, "The request parameters are incomplete", "");
            }
            log.info("Received a position inspection submission request: positionInspectionId={}", request.getPositionInspectionId());
            // 获取阵地巡检信息
            InspectionRecordDomain inspectionRecordDomain = inspectionRecordRepository
                    .getById(request.getPositionInspectionId());
            if (inspectionRecordDomain == null) {
                log.error("Position inspection record does not exist: positionInspectionId={}", request.getPositionInspectionId());
                return new CommonApiResponse<>(500, "Position inspection record does not exist", "");
            }
            //校验记录是否为未完成的任务状态
            if (inspectionRecordDomain.getTaskStatus() != TaskStatusEnum.NOT_COMPLETED) {
                log.error("阵地巡检记录状态为非未完成: positionInspectionId={}", request.getPositionInspectionId());
                return new CommonApiResponse<>(500, "The status of the position inspection record is not incomplete", "");
            }
            // 当前登陆人信息从Token获取
            IntlRmsUserDto intlRmsUserDto = intlRmsUserService.getIntlRmsUserByDomainName(request.getOwner());
            IntlRetailAssert.nonNull(intlRmsUserDto, "The current login user information was not obtained: owner=" + request.getOwner());
            IntlRetailAssert.nonNull(intlRmsUserDto.getMiId(), "The MiId of the current login user was not obtained: owner=" + request.getOwner());
            if (!intlRmsUserDto.getMiId().equals(inspectionRecordDomain.getInspectionOwnerMiId())) {
                log.error("当前登录人与巡检负责人不一致: currentUserMiId={}, inspectionOwnerMiId={}",
                        request.getCurrentUserMiId(), inspectionRecordDomain.getInspectionOwnerMiId());
                return new CommonApiResponse<>(500, "The current login person is inconsistent with the inspection person in charge", "");
            }

            // 验证位置信息
            if (request.getLatitude() == null || request.getLongitude() == null ||
                    request.getPositionLatitude() == null || request.getPositionLongitude() == null) {
                log.error("Position inspection location information is incomplete");
                return new CommonApiResponse<>(500, "Position inspection location information is incomplete", "");
            }
            // 获取阵地信息(数字门店阵地编码转RMS编码)
            PositionDomain positionDomain = positionRepository
                    .getByPositionCode(inspectionRecordDomain.getPositionCode());
            if (positionDomain == null) {
                log.error("Position information does not exist: positionCode={}", inspectionRecordDomain.getPositionCode());
                return new CommonApiResponse<>(500, "Position information does not exist", null);
            }
            // 获取当前时间,到毫秒级
            Instant utcInstant = Instant.now().truncatedTo(ChronoUnit.MILLIS);
            // 设置是否在打卡范围内
            StoreLimitedRangeEnum withinRange = isWithinCheckInRange(request.getLatitude(), request.getLongitude(),
                    request.getPositionLatitude(), request.getPositionLongitude(), request.getCheckInDistance());
            inspectionRecordDomain.setStoreLimitedRange(withinRange);
            inspectionRecordDomain.setModifiedOn(utcInstant.toEpochMilli());
            // 记录修改人为巡检负责人
            inspectionRecordDomain.setModifiedBy(inspectionRecordDomain.getInspectionOwner());
            // 提交后更新巡检记录状态
            inspectionRecordDomain.setInspectionStatus(InspectionStatusEnum.TO_BE_VERIFIED);
            //清空被驳回二次提交时校验驳回的时间
            if (inspectionRecordDomain.getVerificationTime() != null) {
                //设置成0L，前端会自动识别为空
                inspectionRecordDomain.setVerificationTime(0L);
            }
            boolean result;

            // 根据需要/不需要完成任务进行不同的逻辑处理
            if (request.getNoNeedToComplete()) {
                // 任务中心状态变更为无需完成
                TaskCenterNoNeedCompleteReq taskCenterNoNeedCompleteReq = new TaskCenterNoNeedCompleteReq();
                // 查询TaskBatchId
                RuleConfigDomain ruleConfig = ruleConfigRepository.getByRuleCode(inspectionRecordDomain.getRuleCode());
                taskCenterNoNeedCompleteReq.setTaskBatchId(ruleConfig.getTaskBatchId());
                taskCenterNoNeedCompleteReq.setMid(inspectionRecordDomain.getInspectionOwnerMiId());
                taskCenterNoNeedCompleteReq.setOrgId(positionDomain.getPositionCode());
                taskCenterNoNeedCompleteReq.setType(1);
                taskCenterServiceRpc.noNeedCompleteTask(taskCenterNoNeedCompleteReq);
                // 提交后更新任务状态-无需完成
                inspectionRecordDomain.markAsNoNeedToComplete(Optional.ofNullable(intlRmsUserDto.getMiId()).map(Object::toString).orElse(""));
            } else {
                // 构建上传数据的JSON
                UploadData uploadData = new UploadData();
                // 更新fileUpload表，建立guid和fds_url映射,如果是离线上传，则由离线上传接口来创建
                if (!request.getIsOffline()) {
                    List<FileUploadInfo.MetaData> metaDataList = extractMetaDataFromRequest(request);
                    // 调用文件上传服务保存guid和url映射关系
                    if (!CollectionUtils.isEmpty(metaDataList)) {
                        try {
                            fileUploadService.saveSimple(metaDataList, FileUploadEnum.POSITION_INSPECTION, request.getOwner());
                            log.info("成功保存文件上传映射关系，共{}条记录", metaDataList.size());
                        } catch (Exception e) {
                            log.error("保存文件上传映射关系失败", e);
                            // 这里不抛出异常，避免影响主流程
                        }
                    }
                }


                uploadData.setStoreGate(request.getStoreGate());
                uploadData.setPositionLandingPhoto(request.getPositionLandingPhoto());
                uploadData.setPositionDisplay(request.getPositionDisplay());
                uploadData.setFurniturePictures(request.getFurniturePictures());
                // 将数据转换为JSON字符串
                String uploadDataJson;
                try {
                    uploadDataJson = objectMapper.writeValueAsString(uploadData);
                    log.info("生成的上传数据JSON: {}", uploadDataJson);
                } catch (Exception e) {
                    log.error("JSON序列化失败", e);
                    return new CommonApiResponse<>(500, "Data processing failed", "");
                }
                // 保存到阵地巡检记录
                inspectionRecordDomain.setUploadData(uploadDataJson);
                // 任务中心状态变更为结束
                TaskCenterFinishReq taskCenterFinishReq = new TaskCenterFinishReq();
                // 查询TaskBatchId
                RuleConfigDomain ruleConfig = ruleConfigRepository.getByRuleCode(inspectionRecordDomain.getRuleCode());
                IntlRetailAssert.nonNull(ruleConfig, "RuleConfig not found: ruleCode=" + inspectionRecordDomain.getRuleCode());
                taskCenterFinishReq.setTaskBatchId(ruleConfig.getTaskBatchId());
                taskCenterFinishReq.setMid(inspectionRecordDomain.getInspectionOwnerMiId());
                taskCenterFinishReq.setOperatorMid(inspectionRecordDomain.getInspectionOwnerMiId());
                taskCenterFinishReq.setOrgId(positionDomain.getPositionCode());
                taskCenterServiceRpc.outerTaskFinish(taskCenterFinishReq);
                // 提交后更新任务状态-已完成
                inspectionRecordDomain.setTaskStatus(TaskStatusEnum.COMPLETED);
            }
            inspectionRecordDomain.setTaskCompletionTime(utcInstant.toEpochMilli());
            // 补充事务用来回滚状态
            result = inspectionRecordRepository.update(inspectionRecordDomain);
            log.info("更新阵地巡检记录: id={}, result={}", inspectionRecordDomain.getId(), result);
            if (!result) {
                log.error("阵地巡检数据更新失败");
                return new CommonApiResponse<>(500, "Data save failed", "");
            } else {
                // 新建历史操作记录单
                InspectionHistoryDomain history = new InspectionHistoryDomain();
                history.setInspectionRecordId(inspectionRecordDomain.getId());
                history.setOperationType(OperationType.SUBMIT);
                history.setOperator(inspectionRecordDomain.getInspectionOwner());
                // 暂时使用operator作为operatorName
                history.setOperatorName(
                        Optional.ofNullable(intlRmsUserDto)
                                .map(IntlRmsUserDto::getEnglishName)
                                .orElse(inspectionRecordDomain.getInspectionOwner()));
                history.setOperationTime(utcInstant.toEpochMilli());
                history.setCreateTime(utcInstant.toEpochMilli());
                boolean historyResult = inspectionHistoryRepository.save(history);
                if (!historyResult) {
                    log.warn("保存巡检历史记录失败，但主流程已完成");
                }
                // 记录history对象的详细信息到日志
                log.info("保存的巡检历史记录信息: {}", history);
                return new CommonApiResponse<>("");
            }

        } catch (Exception e) {
            log.error("阵地巡检提交处理异常", e);
            return new CommonApiResponse<>(500, "System Exception: " + e.getMessage(), "");
        }
    }

    /**
     * 将PhotoGroup转换为FileUploadInfo.MetaData列表
     *
     * @param photoGroup PhotoGroup对象
     * @return MetaData列表
     */
    private List<FileUploadInfo.MetaData> convertPhotoGroupToMetaDataList(PhotoGroup photoGroup) {
        List<FileUploadInfo.MetaData> metaDataList = new ArrayList<>();
        if (photoGroup != null && StringUtils.isNotEmpty(photoGroup.getGuid())) {
            FileUploadInfo.MetaData metaData = new FileUploadInfo.MetaData(
                    photoGroup.getGuid(),
                    photoGroup.getImages()
            );
            metaDataList.add(metaData);
        }
        return metaDataList;
    }


    /**
     * 将FurniturePhotoGroup列表转换为FileUploadInfo.MetaData列表
     *
     * @param furniturePhotoGroups FurniturePhotoGroup列表
     * @return MetaData列表
     */
    private List<FileUploadInfo.MetaData> convertFurniturePhotoGroupsToMetaDataList(List<FurniturePhotoGroup> furniturePhotoGroups) {
        List<FileUploadInfo.MetaData> metaDataList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(furniturePhotoGroups)) {
            for (FurniturePhotoGroup furniture : furniturePhotoGroups) {
                if (furniture != null && StringUtils.isNotEmpty(furniture.getGuid())) {
                    FileUploadInfo.MetaData metaData = new FileUploadInfo.MetaData(
                            furniture.getGuid(),
                            furniture.getImages()
                    );
                    metaDataList.add(metaData);
                }
            }
        }
        return metaDataList;
    }

    /**
     * 从request中提取所有PhotoGroup并转换为MetaData列表
     *
     * @param request 请求对象
     * @return MetaData列表
     */
    private List<FileUploadInfo.MetaData> extractMetaDataFromRequest(PositionInspectionSubmitRequest request) {
        List<FileUploadInfo.MetaData> metaDataList = new ArrayList<>();

        // 处理StoreGate
        metaDataList.addAll(convertPhotoGroupToMetaDataList(request.getStoreGate()));

        // 处理PositionLandingPhoto
        metaDataList.addAll(convertPhotoGroupToMetaDataList(request.getPositionLandingPhoto()));

        // 处理PositionDisplay
        metaDataList.addAll(convertPhotoGroupToMetaDataList(request.getPositionDisplay()));

        // 处理FurniturePictures - 使用新的方法
        metaDataList.addAll(convertFurniturePhotoGroupsToMetaDataList(request.getFurniturePictures()));

        return metaDataList;
    }

    @Override
    public CommonApiResponse<PositionInspectionAllDetailDTO> getPositionInspectionAllDetail(
            PositionInspectionDetailRequest request) {
        log.info("获取阵地巡检PC详情: positionInspectionId={}", request.getPositionInspectionId());
        try {
            // 参数验证
            if (request.getPositionInspectionId() == null || request.getPositionInspectionId() <= 0) {
                log.error("阵地巡检ID不能为空");
                return new CommonApiResponse<>(500, "阵地巡检ID不能为空", null);
            }
            // 根据id查询对应巡检记录
            InspectionRecordDomain inspectionRecordDomain = inspectionRecordRepository
                    .getById(request.getPositionInspectionId());
            if (inspectionRecordDomain == null) {
                log.error("阵地巡检记录不存在: positionInspectionId={}", request.getPositionInspectionId());
                return new CommonApiResponse<>(500, "阵地巡检记录不存在", null);
            }
            // 获取阵地信息
            PositionDomain positionDomain = positionRepository
                    .getByPositionCode(inspectionRecordDomain.getPositionCode());
            if (positionDomain == null) {
                log.error("阵地信息不存在: positionCode={}", inspectionRecordDomain.getPositionCode());
                return new CommonApiResponse<>(500, "阵地信息不存在", null);
            }
            PositionInspectionAllDetailDTO response = new PositionInspectionAllDetailDTO();
            // 设置阵地信息
            response.setPositionBasicInfo(buildPositionBasicInfo(positionDomain, inspectionRecordDomain));

            // 设置巡检记录信息
            response.setInspectionInfo(buildInspectionInfo(inspectionRecordDomain));

            // 设置图片信息
            PositionInspectionAllDetailDTO.PhotoGalleries photoGalleries = new PositionInspectionAllDetailDTO.PhotoGalleries();

            // 当未完成任务或任务无需完成时, 来自阵地巡检图片为空
            if (inspectionRecordDomain.getTaskStatus().equals(TaskStatusEnum.COMPLETED)) {
                // 解析上传的数据并设置图片
                if (StringUtils.isNotEmpty(inspectionRecordDomain.getUploadData())) {
                    try {
                        // 将图片数据转换为UploadData格式
                        UploadData uploadData = JsonUtil.json2bean(inspectionRecordDomain.getUploadData(),
                                UploadData.class);
                        // 处理UploadData中的图片数据，通过guid获取对应的图片URL
                        uploadData = processUploadDataImages(uploadData);
                        photoGalleries.setInspectionUpload(uploadData);
                    } catch (Exception e) {
                        log.error("解析巡检图片数据异常", e);
                    }
                }
            } else {
                log.info("阵地巡检任务不需要图片数据: positionInspectionId={}, taskStatus={}", request.getPositionInspectionId(),
                        inspectionRecordDomain.getTaskStatus());
            }

            // 获取建店图片
            // 先获取当前巡检记录的动作编码
            PositionDataReq positionDataReq = new PositionDataReq();
            positionDataReq.setReportCode(inspectionRecordDomain.getActionCode());
            Result<List<PositionImageCenterResp>> result = buildChannelPositionProvider.imageCenter(positionDataReq);

            // 处理建店图片数据，转换为UploadData格式
            if (result != null && !CollectionUtils.isEmpty(result.getData())) {
                try {
                    UploadData positionCreationData = convertPositionImageCenterRespToUploadData(result.getData());
                    photoGalleries.setPositionCreation(positionCreationData);
                } catch (Exception e) {
                    log.error("转换建店图片数据异常", e);
                }
            } else {
                log.info("建店图片数据为空或获取失败: actionCode={}, result={}", inspectionRecordDomain.getActionCode(), result);
            }

            response.setPhotoGalleries(photoGalleries);
            // 返回成功响应
            return new CommonApiResponse<>(response);

        } catch (Exception e) {
            log.error("获取阵地巡检详情异常", e);
            return new CommonApiResponse<>(500, "系统异常: " + e.getMessage(), null);
        }
    }

    /**
     * 构建阵地基本信息
     *
     * @param positionDomain         阵地领域对象
     * @param inspectionRecordDomain 巡检记录领域对象
     * @return 阵地基本信息对象
     */
    private PositionInspectionAllDetailDTO.PositionBasicInfo buildPositionBasicInfo(PositionDomain positionDomain,
                                                                                    InspectionRecordDomain inspectionRecordDomain) {
        PositionInspectionAllDetailDTO.PositionBasicInfo positionBasicInfo = new PositionInspectionAllDetailDTO.PositionBasicInfo();
        PositionFurnitureRequest request = new PositionFurnitureRequest();
        request.setPositionCode(inspectionRecordDomain.getPositionCode());
        //计算家具数量
        Optional.ofNullable(this.getPositionFurnitureList(request))
                .ifPresent(list -> positionBasicInfo.setFurnitureTotal(list.size()));
        positionBasicInfo.setCountry(positionDomain.getCountryName());
        positionBasicInfo.setPositionCode(inspectionRecordDomain.getPositionCode());
        positionBasicInfo.setPositionName(positionDomain.getPositionName());
        positionBasicInfo.setPositionType(positionDomain.getPositionTypeName());

        positionBasicInfo.setCreationTime(inspectionRecordDomain.getPositionCreationTime());
        positionBasicInfo.setPositionConstructionType(Optional.ofNullable(inspectionRecordDomain.getPositionConstructionType())
                .map(InspectionBusinessTypeEnum::getDesc).orElse(null));


        PositionItemListRequest positionItemListRequest = new PositionItemListRequest();
        positionItemListRequest.setBusinessScene("channelRetail");
        //所有的映射枚举
        PositionSelectorItemList positionSelectorItemList = this.getSelectorList(positionItemListRequest);

        Map<String, String> positionCategory = Optional.ofNullable(positionSelectorItemList)
                .map(PositionSelectorItemList::getPositionCategory)
                .orElse(Collections.emptyList())
                .stream()
                .filter(item -> item != null && item.getKey() != null)
                .collect(Collectors.toMap(OptionalItem::getKey, OptionalItem::getValue));

        // 将positionDomain.getPositionCategory()的List<Integer>映射为String
        if (positionDomain.getPositionCategory() != null && !positionDomain.getPositionCategory().isEmpty()) {
            String categoryStr = positionDomain.getPositionCategory().stream()
                    .map(String::valueOf)
                    .map(positionCategory::get)
                    .filter(Objects::nonNull)
                    .filter(s -> !s.isEmpty())
                    .collect(Collectors.joining(","));
            positionBasicInfo.setPositionCategory(categoryStr);
        } else {
            positionBasicInfo.setPositionCategory("");
        }

        // PositionLocation选项集映射
        if (positionDomain.getPositionLocation() != null) {
            Map<String, String> positionLocationMap = Optional.ofNullable(positionSelectorItemList)
                    .map(PositionSelectorItemList::getPositionLocation)
                    .orElse(Collections.emptyList())
                    .stream()
                    .filter(item -> item != null && item.getKey() != null)
                    .collect(Collectors.toMap(OptionalItem::getKey, OptionalItem::getValue));
            positionBasicInfo.setPositionLocation(
                    Optional.ofNullable(positionLocationMap.get(String.valueOf(positionDomain.getPositionLocation())))
                            .orElse(""));
        }
        // DisplayStandardization选项集映射
        if (positionDomain.getDisplayCapacityExpansionStatus() != null) {
            Map<String, String> displayStandardizationMap = Optional.ofNullable(positionSelectorItemList)
                    .map(PositionSelectorItemList::getDisplayStandardization)
                    .orElse(Collections.emptyList())
                    .stream()
                    .filter(item -> item != null && item.getKey() != null)
                    .collect(Collectors.toMap(OptionalItem::getKey, OptionalItem::getValue));
            positionBasicInfo.setDisplayStandardization(
                    Optional.ofNullable(displayStandardizationMap.get(String.valueOf(positionDomain.getDisplayCapacityExpansionStatus())))
                            .orElse(""));
        }

        return positionBasicInfo;
    }

    /**
     * 构建巡检记录信息
     *
     * @param inspectionRecordDomain 巡检记录领域对象
     * @return 巡检记录信息对象
     */
    private PositionInspectionAllDetailDTO.InspectionInfo buildInspectionInfo(
            InspectionRecordDomain inspectionRecordDomain) {
        PositionInspectionAllDetailDTO.InspectionInfo inspectionInfo = new PositionInspectionAllDetailDTO.InspectionInfo();
        inspectionInfo.setInspectionOwner(inspectionRecordDomain.getInspectionOwner());
        inspectionInfo.setTaskStatus(Optional.ofNullable(inspectionRecordDomain.getTaskStatus())
                .map(com.mi.info.intl.retail.intlretail.service.api.position.enums.TaskStatusEnum::getDesc).orElse(null));
        inspectionInfo.setTaskCompletionTime(inspectionRecordDomain.getTaskCompletionTime());
        // 设置验证人，使用修改者信息
        inspectionInfo.setVerifier(inspectionRecordDomain.getVerifier());
        List<InspectionHistoryDomain> historyList = inspectionHistoryRepository
                .getByInspectionRecordId(inspectionRecordDomain.getId());
        // 查询历史提交次数
        int submitCount = 0;
        if (!CollectionUtils.isEmpty(historyList)) {
            submitCount = (int) historyList.stream()
                    .filter(history -> OperationType.SUBMIT.equals(history.getOperationType()))
                    .count();
        }
        inspectionInfo.setSubmitCount(submitCount);
        inspectionInfo.setTaskCreationTime(inspectionRecordDomain.getCreateInstanceTime());
        inspectionInfo.setVerificationStatus(Optional.ofNullable(inspectionRecordDomain.getInspectionStatus())
                .map(com.mi.info.intl.retail.intlretail.service.api.position.enums.InspectionStatusEnum::getDesc).orElse(null));
        inspectionInfo.setVerificationTime(inspectionRecordDomain.getVerificationTime());
        return inspectionInfo;
    }

    @Override
    public CommonApiResponse<PositionInspectionDetailResponse> getPositionInspectionDetail(
            PositionInspectionDetailRequest request) {
        log.info("获取阵地巡检APP详情: positionInspectionId={}", request.getPositionInspectionId());

        try {
            // 参数验证
            if (request.getPositionInspectionId() == null || request.getPositionInspectionId() <= 0) {
                log.error("阵地巡检ID不能为空");
                return new CommonApiResponse<>(500, "阵地巡检ID不能为空", null);
            }

            // 根据id查询对应巡检记录
            InspectionRecordDomain inspectionRecordDomain = inspectionRecordRepository
                    .getById(request.getPositionInspectionId());
            if (inspectionRecordDomain == null) {
                log.error("阵地巡检记录不存在: positionInspectionId={}", request.getPositionInspectionId());
                return new CommonApiResponse<>(500, "阵地巡检记录不存在", null);
            }

            // 获取阵地信息
            PositionDomain positionDomain = positionRepository
                    .getByPositionCode(inspectionRecordDomain.getPositionCode());
            if (positionDomain == null) {
                log.error("阵地信息不存在: positionCode={}", inspectionRecordDomain.getPositionCode());
                return new CommonApiResponse<>(500, "阵地信息不存在", null);
            }

            // 构建响应对象
            PositionInspectionDetailResponse response = new PositionInspectionDetailResponse();

            // 设置门店信息
            PositionInspectionDetailResponse.StoreInfo storeInfo = new PositionInspectionDetailResponse.StoreInfo();
            storeInfo.setStoreName(positionDomain.getStoreName());
            storeInfo.setFrontName(positionDomain.getPositionName());
            if (inspectionRecordDomain.getPositionConstructionType() != null) {
                storeInfo.setPositionConstructionType(inspectionRecordDomain.getPositionConstructionType().getDesc());
            }
            storeInfo.setCreationTime(inspectionRecordDomain.getPositionCreationTime());
            // 安全设置状态，避免空指针
            storeInfo.setStatus(
                    inspectionRecordDomain.getInspectionStatus() != null
                            ? inspectionRecordDomain.getInspectionStatus().getDesc()
                            : null);

            // 安全设置remark，避免空指针
            if (inspectionRecordDomain.getDisapproveReason() != null
                    && DisapproveReasonEnum.OTHER.getCode()
                    .equals(inspectionRecordDomain.getDisapproveReason().getCode())) {
                storeInfo.setRemark(inspectionRecordDomain.getRemark());
            } else {
                storeInfo.setRemark(
                        inspectionRecordDomain.getDisapproveReason() != null
                                ? inspectionRecordDomain.getDisapproveReason().getDesc()
                                : null);
            }
            response.setStoreInfo(storeInfo);

            // 解析上传的数据
            if (StringUtils.isNotEmpty(inspectionRecordDomain.getUploadData())) {
                UploadData uploadData = JsonUtil.json2bean(inspectionRecordDomain.getUploadData(), UploadData.class);
                if (uploadData != null) {
                    // 处理UploadData中的图片数据，通过guid获取对应的图片URL
                    uploadData = processUploadDataImages(uploadData);
                    response.setSections(uploadData);
                }
            }
            // 返回成功响应
            return new CommonApiResponse<>(response);
        } catch (Exception e) {
            log.error("获取阵地巡检详情异常", e);
            return new CommonApiResponse<>(500, "系统异常: " + e.getMessage(), null);
        }
    }

    public Page<PositionInspectionItem> pagePositionInspection(PositionInspectionRequest request) {
        log.info("分页查询阵地巡检信息: {}", request);


        // 创建分页对象
        Page<PositionInspectionItem> page = new Page<>(request.getPageNum(), request.getPageSize());

        // 调用仓储层查询数据
        return positionInspectionRepository.pagePositionInspection(page, request);
    }

    @Override
    public String approvePositionInspection(PositionInspectionApproveRequest request) {
        log.info("审批阵地巡检: {}", request);

        // 参数校验
        if (request.getPositionInspectionId() == null || request.getPositionInspectionId() <= 0) {
            return "阵地巡检ID不能为空";
        }
        // 如果是拒绝操作，必须提供备注和拒绝原因
        VerifyActionEnum verifyActionEnum = VerifyActionEnum.fromCode(request.getVerifyAction());
        if (verifyActionEnum.equals(VerifyActionEnum.DISAPPROVE)) {
            if (request.getReason() == null) {
                return "拒绝操作必须提供拒绝原因类型";
            }

            // 如果拒绝原因是OTHER，必须提供备注
            DisapproveReasonEnum disapproveReason = DisapproveReasonEnum.getByCode(request.getReason());
            if (disapproveReason != null && disapproveReason.equals(DisapproveReasonEnum.OTHER)) {
                if (request.getRemark() == null || request.getRemark().isEmpty()) {
                    return "选择其他原因时必须提供具体理由";
                }
            }
        }
        // 根据阵地巡检ID查询记录
        InspectionRecordDomain inspectionRecord = inspectionRecordRepository.getById(request.getPositionInspectionId());
        if (inspectionRecord == null) {
            log.error("未找到阵地巡检记录, positionInspectionId={}", request.getPositionInspectionId());
            return "未找到阵地巡检记录";
        }
        TaskStatusEnum taskStatus = inspectionRecord.getTaskStatus();
        InspectionStatusEnum inspectionStatus = inspectionRecord.getInspectionStatus();
        // 判断Task status为已完成或者无需完成，并且Inspection status为待核验
        if ((TaskStatusEnum.COMPLETED.equals(taskStatus) || TaskStatusEnum.NO_NEED_TO_DO.equals(taskStatus)) &&
                InspectionStatusEnum.TO_BE_VERIFIED.equals(inspectionStatus)) {
            log.info("");
        } else {
            return "当前记录状态不满足审批条件：Task status必须已完成或者无需完成，并且Inspection status为待核验";
        }
        // 创建更新审核状态信息对象
        UpdateApprovalInfo updateApprovalInfo = new UpdateApprovalInfo();
        updateApprovalInfo.setVerifyAction(request.getVerifyAction());
        updateApprovalInfo.setRemark(request.getRemark());
        updateApprovalInfo.setReason(request.getReason());
        updateApprovalInfo.setPositionInspectionId(request.getPositionInspectionId());
        if (RpcContext.getContext().getAttachments().get("$upc_userName") != null) {
            updateApprovalInfo.setVerifier(RpcContext.getContext().getAttachments().get("$upc_userName"));
        }
        if (RpcContext.getContext().getAttachments().get("$upc_miID") != null) {
            updateApprovalInfo
                    .setVerifierMiid(Long.parseLong(RpcContext.getContext().getAttachments().get("$upc_miID")));
        }

        // 发布更新审核状态事件
        log.info("发布更新审核状态事件: {}", updateApprovalInfo);
        eventPublisher.publishEvent(new UpdateApprovalStatusEvent(this, updateApprovalInfo));

        return "审批请求已提交";
    }

    /**
     * 获取阵地负责人信息
     *
     * @param positionCode 阵地代码
     * @return 负责人DTO对象，如果没有找到则返回null
     */
    @Override
    public PositionInspectionResponsiblePersonDTO getResponsiblePersonByPositionCode(String positionCode) {
        if (!org.springframework.util.StringUtils.hasText(positionCode)) {
            log.warn("获取阵地负责人失败：阵地代码为空");
            return null;
        }

        // 查询阵地负责人信息
        List<Map<String, Object>> results = positionResponsiblePersonMapper
                .getPositionInspectionResponsiblePerson(positionCode);

        if (org.springframework.util.CollectionUtils.isEmpty(results)) {
            log.info("阵地[{}]无负责人信息", positionCode);
            return null;
        }

        // 按照职位优先级和创建时间排序
        Map<String, Object> responsiblePerson = createPositionInspectionRecord(results);

        if (responsiblePerson == null) {
            log.info("阵地[{}]无法确定负责人", positionCode);
            return null;
        }

        return convertToDTO(responsiblePerson);
    }

    @Override
    public CommonApiResponse<String> taskReminder() {
        log.info("执行阵地巡检定时任务提醒逻辑");
        try {
            // 1. 查询待提醒数据
            List<UnRemindedTaskDTO> activeTasks = inspectionRecordRepository.findActiveTasksWithActiveRules();
            if (CollectionUtils.isEmpty(activeTasks)) {
                log.info("无待提醒的任务");
                return new CommonApiResponse<>(200, "无待提醒的任务", "无待提醒的任务");
            }

            int totalRemindCount = 0;
            int totalSuccessCount = 0;
            int totalFailCount = 0;
            // 获取当前时间,到毫秒级
            Instant utcInstant = Instant.now().truncatedTo(ChronoUnit.MILLIS);

            // 直接循环每条activeTasks
            for (UnRemindedTaskDTO dto : activeTasks) {
                // 获取阵地信息
                PositionDomain positionDomain = positionRepository.getByPositionCode(dto.getBusinessCode());
                if (positionDomain == null) {
                    log.error("阵地信息不存在: positionCode={}", dto.getBusinessCode());
                    return new CommonApiResponse<>(500, "阵地信息不存在", "阵地信息不存在");
                }
                // 获取阵地编码 SELECT code FROM `intl_rms_position` where crpscode='CID001915'

                // 组装TaskCenterPushTaskReq.OrgAndMid
                TaskCenterPushTaskReq.OrgAndMid orgAndMid = new TaskCenterPushTaskReq.OrgAndMid();
                orgAndMid.setMid(dto.getInspectionOwnerMiId());
                orgAndMid.setOrgId(positionDomain.getPositionCode());

                // 组装TaskCenterPushTaskReq
                TaskCenterPushTaskReq pushTaskReq = new TaskCenterPushTaskReq();
                pushTaskReq.setTaskBatchId(dto.getTaskBatchId());
                pushTaskReq.setList(Collections.singletonList(orgAndMid));

                boolean pushSuccess = false;
                try {
                    taskCenterServiceRpc.pushTask(pushTaskReq);
                    pushSuccess = true;
                } catch (Exception e) {
                    log.error("调用pushTask失败, taskBatchId={}, error={}", dto.getTaskBatchId(), e.getMessage(), e);
                }

                totalRemindCount++;
                if (pushSuccess) {
                    totalSuccessCount++;
                    // 任务提醒成功后，更新reminder_time
                    try {
                        long reminderDays = Optional.ofNullable(dto.getReminderDays()).orElse(0).longValue();
                        long newReminderTime = utcInstant.toEpochMilli() + reminderDays * 24 * 60 * 60 * 1000L;
                        // 查询巡检记录
                        InspectionRecordDomain record = inspectionRecordRepository.getById(dto.getId());
                        if (record != null) {
                            record.setReminderTime(newReminderTime);
                            // 更新巡检记录
                            inspectionRecordRepository.update(record);
                        }
                    } catch (Exception e) {
                        log.error("更新reminder_time失败, inspectionRecordId={}, error={}", dto.getId(), e.getMessage(), e);
                    }
                } else {
                    totalFailCount++;
                    log.warn("pushTask失败, taskBatchId={}, 该任务未提醒", dto.getTaskBatchId());
                }
            }

            String msg = String.format("定时任务提醒执行完成，总计：%d，成功：%d，失败：%d", totalRemindCount, totalSuccessCount,
                    totalFailCount);
            log.info(msg);
            return new CommonApiResponse<>(200, msg, msg);
        } catch (Exception e) {
            log.error("执行阵地巡检定时任务提醒异常", e);
            return new CommonApiResponse<>(500, "定时任务提醒执行失败: " + e.getMessage(), "定时任务提醒执行失败: " + e.getMessage());
        }
    }

    @Override
    public PositionInspectionResponsiblePersonDTO getPositionInspectionResponsiblePerson(String positionCode) {
        if (!org.springframework.util.StringUtils.hasText(positionCode)) {
            log.warn("获取阵地巡检负责人失败：参数无效");
            return null;
        }

        // 获取阵地负责人
        PositionInspectionResponsiblePersonDTO responsiblePerson = getResponsiblePersonByPositionCode(positionCode);

        if (responsiblePerson == null) {
            return null;
        }

        // 更新巡检记录表中的负责人信息
        Map<String, Object> personMap = new HashMap<>();
        personMap.put("user_id", responsiblePerson.getUserId());
        personMap.put("mi_id", responsiblePerson.getMiId());
        updateInspectionRecordOwner(positionCode, personMap);

        return responsiblePerson;
    }

    /**
     * 更新巡检记录表中的负责人信息
     *
     * @param positionCode      阵地代码
     * @param responsiblePerson 负责人信息
     */
    private void updateInspectionRecordOwner(String positionCode, Map<String, Object> responsiblePerson) {
        if (responsiblePerson == null || !org.springframework.util.StringUtils.hasText(positionCode)) {
            log.warn("更新巡检记录负责人失败：参数无效");
            return;
        }

        try {
            // 获取未审批完成的巡检记录并更新负责人
            updatePendingInspectionRecordOwner(positionCode, responsiblePerson);
        } catch (Exception e) {
            log.error("更新阵地[{}]巡检记录负责人异常", positionCode, e);
        }
    }

    /**
     * 获取未审批完成的巡检记录并更新负责人
     *
     * @param positionCode      阵地代码
     * @param responsiblePerson 负责人信息
     */
    private void updatePendingInspectionRecordOwner(String positionCode, Map<String, Object> responsiblePerson) {
        // 获取该阵地的未审批完成的巡检记录
        List<InspectionRecordDomain> records = inspectionRecordRepository
                .getPendingApprovalByBusinessCode(positionCode);
        if (org.springframework.util.CollectionUtils.isEmpty(records)) {
            log.info("阵地[{}]无未审批完成的巡检记录，无需更新负责人信息", positionCode);
            return;
        }

        // 提取负责人信息
        String userId = (String) responsiblePerson.get("user_id");
        String miIdStr = (String) responsiblePerson.get("mi_id");
        // 获取当前时间,到毫秒级
        Instant utcInstant = Instant.now().truncatedTo(ChronoUnit.MILLIS);

        if (!org.springframework.util.StringUtils.hasText(userId)) {
            log.warn("更新巡检记录负责人失败：负责人ID为空");
            return;
        }

        // 转换miId为Long类型
        Long miId = null;
        if (org.springframework.util.StringUtils.hasText(miIdStr)) {
            try {
                miId = Long.parseLong(miIdStr);
            } catch (NumberFormatException e) {
                log.warn("转换miId失败：{}", miIdStr, e);
            }
        }

        // 批量更新记录
        long currentTime = utcInstant.toEpochMilli();
        int successCount = 0;
        int skipCount = 0;

        for (InspectionRecordDomain record : records) {
            // 判断负责人是否一致，如果一致则跳过更新
            if (userId.equals(record.getInspectionOwner()) && ((miId == null && record.getInspectionOwnerMiId() == null)
                    || (miId != null && miId.equals(record.getInspectionOwnerMiId())))) {
                skipCount++;
                continue;
            }

            record.setInspectionOwner(userId);
            record.setInspectionOwnerMiId(miId);
            record.setModifiedBy("SYSTEM");
            record.setModifiedOn(currentTime);

            if (inspectionRecordRepository.update(record)) {
                successCount++;
            }
        }

        log.info("阵地[{}]巡检记录负责人更新完成，成功{}条，跳过{}条，总计{}条", positionCode, successCount, skipCount, records.size());
    }

    /**
     * 按照职位优先级和创建时间查找负责人
     * 优先级顺序：Promoter > Temporary Promoter > Supervisor > Supervisor without
     * promoters > Merchandiser
     * 同一职位按created_on时间降序排序
     *
     * @param personnelList 人员列表
     * @return 负责人信息
     */
    private Map<String, Object> createPositionInspectionRecord(List<Map<String, Object>> personnelList) {
        if (org.springframework.util.CollectionUtils.isEmpty(personnelList)) {
            return null;
        }

        // 按职位分组
        Map<String, List<Map<String, Object>>> groupByTitle = personnelList.stream()
                .filter(item -> item.get("user_title") != null)
                .collect(Collectors.groupingBy(item -> (String) item.get("user_title")));

        // 按职位优先级排序
        List<String> sortedTitles = groupByTitle.keySet().stream()
                .sorted(Comparator.comparing(title -> JOB_PRIORITY.getOrDefault(title, 999)))
                .collect(Collectors.toList());

        // 没有匹配的职位
        if (sortedTitles.isEmpty()) {
            return null;
        }

        // 获取优先级最高的职位
        String highestPriorityTitle = sortedTitles.get(0);
        List<Map<String, Object>> highestPriorityPersonnel = groupByTitle.get(highestPriorityTitle);

        // 按创建时间降序排序
        highestPriorityPersonnel.sort((a, b) -> {
            Object dateAObj = a.get("created_on");
            Object dateBObj = b.get("created_on");

            // 处理null值情况
            if (dateBObj == null)
                return (dateAObj == null) ? 0 : 1;
            if (dateAObj == null)
                return -1;

            // 只考虑两种情况：Date与Date比较，LocalDateTime与LocalDateTime比较
            if (dateAObj instanceof java.util.Date && dateBObj instanceof java.util.Date) {
                return ((java.util.Date) dateBObj).compareTo((java.util.Date) dateAObj); // 降序
            } else if (dateAObj instanceof java.time.LocalDateTime && dateBObj instanceof java.time.LocalDateTime) {
                return ((java.time.LocalDateTime) dateBObj).compareTo((java.time.LocalDateTime) dateAObj); // 降序
            } else {
                // 其他情况，使用toString比较
                return dateBObj.toString().compareTo(dateAObj.toString());
            }
        });

        // 返回创建时间最新的人员
        return highestPriorityPersonnel.get(0);
    }

    /**
     * 将Map转换为DTO
     *
     * @param result 查询结果
     * @return DTO对象
     */
    private PositionInspectionResponsiblePersonDTO convertToDTO(Map<String, Object> result) {
        if (result == null) {
            return null;
        }

        PositionInspectionResponsiblePersonDTO dto = new PositionInspectionResponsiblePersonDTO();
        dto.setUserId((String) result.get("user_id"));
        dto.setUserName((String) result.get("user_name"));
        dto.setUserTitle((String) result.get("user_title"));
        dto.setUserTitleCode((Integer) result.get("user_title_code"));
        dto.setMiId((Long) result.get("mi_id"));
        dto.setLanguageCode((String) result.get("language_code"));
        dto.setPositionCode((String) result.get("psition_code"));
        return dto;
    }

    @Override
    public PositionSelectorItemList getSelectorList(PositionItemListRequest request) {
        String language = RequestContextInfo.getLanguage();
        // 调用3C系统获取通用数据
        PositionCommonItemList commonItemList = storeRelateRpc.get3CCommonPositionItemList(
                request.getInternationalAreaId(),
                request.getBusinessScene(), language);
        PositionSelectorItemList positionSelectorItemList = BeanConverter.INSTANCE.map(commonItemList);

        // 设置任务状态和巡检状态
        List<OptionalItem<Integer>> taskStatusItemList = I18nDesc.toOptionalItems(TaskStatusEnum.class);
        taskStatusItemList.removeIf(item -> StringUtils.isEmpty(item.getValue()));
        positionSelectorItemList.setTaskStatus(taskStatusItemList);
        positionSelectorItemList.setInspectionStatus(I18nDesc.toOptionalItems(InspectionStatusEnum.class));
        positionSelectorItemList.setPositionConstructionType(I18nDesc.toOptionalItems(InspectionBusinessTypeEnum.class));
        positionSelectorItemList.setDisapproveReason(I18nDesc.toOptionalItems(DisapproveReasonEnum.class));
        positionSelectorItemList.setStoreLimitedRange(I18nDesc.toOptionalItems(StoreLimitedRangeEnum.class));
        return positionSelectorItemList;
    }

    /**
     * 获取巡检记录汇总数据
     *
     * @param request request
     * @return 巡检记录汇总数据
     */
    @Override
    public InspectionSummaryDTO getInspectionSummary(PositionInspectionRequest request) {
        return inspectionRecordRepository.getInspectionSummary(request);
    }

    /**
     * 判断账号是否有未完成任务
     *
     * @param account 账号
     * @return 有未完成任务返回true，否则false
     */
    public boolean hasUnCompletedTask(String account) {
        return inspectionRecordRepository.existsUnCompletedTaskByOwner(account);
    }

    @SneakyThrows
    @Override
    public void batchUpload(PositionImgBatchUploadRequest request) {
        IntlRetailAssert.nonNull(request, "request can not be null");
        IntlRetailAssert.nonNull(request.getStoreGate(), "storeGate can not be null");
        IntlRetailAssert.nonNull(request.getPositionDisplay(), "positionDisplay can not be null");
        IntlRetailAssert.nonNull(request.getPositionLandingPhoto(), "positionLandingPhoto can not be null");
        IntlRetailAssert.nonNull(request.getFurniturePicture(), "furniturePicture can not be null");
        // 创建临时文件夹
        File tmpDir = FileUtil.getTmpDir();
        String uuid = IdUtil.simpleUUID();
        File targetDir = FileUtil.file(tmpDir, uuid);
        FileUtil.mkdir(targetDir);
        // 解压
        Map<String, PositionImageInfo> positionImageInfoMap = new HashMap<>();
        downloadAndUnZip(request.getStoreGate(), targetDir, positionImageInfoMap, PositionImageInfo::getStoreGate,
                false);
        downloadAndUnZip(request.getPositionDisplay(), targetDir, positionImageInfoMap,
                PositionImageInfo::getPositionDisplay, false);
        downloadAndUnZip(request.getPositionLandingPhoto(), targetDir, positionImageInfoMap,
                PositionImageInfo::getPositionLandingPhoto, false);
        downloadAndUnZip(request.getFurniturePicture(), targetDir, positionImageInfoMap,
                PositionImageInfo::getFurniturePicture, true);
        // 获取操作人姓名
        String operatorName = null;
        if (RpcContext.getContext().getAttachments().get("$upc_userName") != null) {
            operatorName = RpcContext.getContext().getAttachments().get("$upc_userName");
        }

        // 发送上传事件
        PositionImageBatchUploadEvent event = new PositionImageBatchUploadEvent(request.getAccount(),
                request.getEmail(),
                positionImageInfoMap.values(), targetDir, operatorName);
        IntlRetailAssert.notEmpty(event.getSource(), "The compressed package content does not exist");
        eventPublisher.publishEvent(event);
    }

    /**
     * 标记任务为无需完成
     *
     * @param req 任务中心完成任务请求
     * @return 操作结果信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String noNeedCompleteTask(TaskCenterFinishTaskReq req) {
        log.info("领域服务处理无需完成任务请求: {}", req);

        if (req == null || req.getTaskBatchId() == null) {
            log.warn("无需完成任务请求参数不完整");
            return "请求参数不完整";
        }

        try {
            //根据miid查询对应的人
            String operator = req.getMiId() == null ? "" : req.getMiId().toString();
            Optional<IntlRmsUserDto> intlRmsUserDomain = intlRmsUserService.getIntlRmsUserByMiId(req.getMiId());
            if (intlRmsUserDomain.isPresent()) {
                operator = intlRmsUserDomain.get().getDomainName();
            }
            // 1. 查询规则配置
            RuleConfigDomain ruleConfig = ruleConfigRepository.getByTaskBatchId(req.getTaskBatchId());
            if (ruleConfig == null) {
                log.info("未找到对应的阵地巡检规则配置，taskBatchId: {},", req.getTaskBatchId());
                return "非阵地巡检任务，无需处理";
            }

            // 获取阵地信息
            PositionDomain positionDomain = positionRepository.getByCode(req.getOrgId());
            if (positionDomain == null) {
                log.error("阵地信息不存在: positionCode={}", req.getOrgId());
                return "阵地信息不存在";
            }
            // 获取阵地编码 SELECT crpscode FROM `intl_rms_position` where code='MIIDD02054'

            // 2. 查询巡检记录
            InspectionRecordDomain inspectionRecord = inspectionRecordRepository.getByRuleCodeAndBusinessCode(
                    ruleConfig.getRuleCode(),
                    positionDomain.getCrpsCode());
            if (inspectionRecord == null) {
                log.info("找不到巡检记录，ruleCode: {}, orgId: {}", ruleConfig.getRuleCode(), positionDomain.getCrpsCode());
                return "找不到巡检记录，无需处理";
            }
            inspectionRecord.markAsNoNeedToComplete(req.getMiId() == null ? "" : req.getMiId().toString());
            Instant utcInstant = Instant.now().truncatedTo(ChronoUnit.MILLIS);
            boolean result = inspectionRecordRepository.update(inspectionRecord);
            log.info("更新巡检记录状态为无需完成: id={}, result={}", inspectionRecord.getId(), result);
            if (!result) {
                log.error("更新巡检记录状态失败");
                throw new RuntimeException("更新巡检记录状态失败"); // 抛异常让事务回滚
            }

            // 4. 保存历史记录
            InspectionHistoryDomain history = new InspectionHistoryDomain();
            history.setInspectionRecordId(inspectionRecord.getId());
            history.setOperationType(OperationType.SUBMIT);
            history.setRemark("标记为无需完成");
            history.setOperator(operator);
            //intlRmsUserDomain不为空 则用englishName
            if (intlRmsUserDomain.isPresent()) {
                history.setOperatorName(intlRmsUserDomain.get().getEnglishName());
            } else {
                history.setOperatorName(operator); // 使用operator作为operatorName
            }
            history.setOperationTime(utcInstant.toEpochMilli());
            history.setCreateTime(utcInstant.toEpochMilli());
            boolean historyResult = inspectionHistoryRepository.save(history);
            if (!historyResult) {
                log.error("保存巡检历史记录失败");
                throw new RuntimeException("保存巡检历史记录失败"); // 抛异常让事务回滚
            }

            return "成功标记任务为无需完成";
        } catch (Exception e) {
            log.error("处理无需完成任务异常", e);
            throw e; // 让Spring事务感知异常并回滚
        }
    }

    /**
     * 完成用户当前任务动作
     *
     * @param req 任务中心完成任务请求
     * @return 操作结果信息
     */
    @Override
    public String outerTaskFinish(TaskCenterFinishTaskReq req) {
        return "成功完成用户当前任务动作";
    }

    @Override
    public List<PositionInspectionHistoryItem> operationHistory(Long positionInspectionId) {

        InspectionRecordDomain record = inspectionRecordRepository.getById(positionInspectionId);
        if (record == null) {
            log.warn("position inspection record not found. id: {}", positionInspectionId);
            return Collections.emptyList();
        }

        List<InspectionHistoryDomain> historyList = inspectionHistoryRepository
                .getByInspectionRecordId(positionInspectionId);
        return historyList.stream().map(BeanConverter.INSTANCE::map2InspectionHistoryItem).collect(Collectors.toList());
    }

    /**
     * 根据阵地编码创建巡检记录
     *
     * @param positionCode 阵地编码
     * @param operatorId   操作人ID
     * @return 创建结果
     */
    @Override
    public String createInspectionByPositionCode(String areaId, String positionCode, String operatorId) {
        log.info("根据阵地编码创建巡检记录: areaId={}, positionCode={}, operatorId={}", areaId, positionCode, operatorId);

        try {
            // dubbo服务
            ListPositionInfoRequest listPositionInfoRequest = new ListPositionInfoRequest();
            listPositionInfoRequest.setPositionCodeList(Collections.singletonList(positionCode));
            Result<ListPositionInfoResponse> positionInfoResult = positionProvider
                    .listPositionInfo(listPositionInfoRequest);
            ListPositionInfoResponse listPositionInfoResponse = positionInfoResult.getData();
            // 验证是否为空
            if (listPositionInfoResponse == null || listPositionInfoResponse.getList() == null
                    || listPositionInfoResponse.getList().isEmpty()) {
                log.warn("阵地信息在主数据系统不存在: positionCode={}", positionCode);
                return "阵地信息不存在";
            }
            // 查找对应的rule
            List<RuleConfigDomain> ruleConfigList = ruleConfigRepository.findValidRuleConfigsByCountry(areaId, 301);
            if (ruleConfigList == null || ruleConfigList.isEmpty()) {
                log.warn("未找到对应的规则配置: country={}, taskType=301", areaId);
                return "未找到对应的规则配置: " + areaId;
            }
            ListPositionInfoResponse.PositionInfo positionInfo = listPositionInfoResponse.getList().get(0);
            ListPositionInfoResponse.PositionInfo.PositionExtension positionExtension = positionInfo.getPositionExtension();
            // 获取constructionPhase 从枚举PositionStageEnum获取对应的枚举，如果为空则直接返回
            PositionStageEnum positionStageEnum = PositionStageEnum.getByCode(positionInfo.getConstructionPhase());
            if (positionStageEnum == null) {
                log.warn("阵地阶段不存在: positionCode={}", positionCode);
                return "阵地阶段不存在";
            }
            // POS类型无需处理
            if (PositionTypeEnum.POS.getCode().equals(positionInfo.getPositionType())) {
                log.warn("阵地为POS类型，无需下发：positionCode={}", positionCode);
                return "阵地为POS，无需处理";
            }
            // 新建类型的没有旧阵地类型
            if (positionExtension != null && positionExtension.getOldPositionType() != null) {
                // 如果旧类型是Pos，新类型不为Pos
                if (PositionTypeEnum.POS.getCode().equals(positionExtension.getOldPositionType())) {
                    // POS升级为非POS视为新建
                    positionStageEnum = PositionStageEnum.ACCEPTANCE_APPROVED;
                }
            }


            // 获取巡检负责人
            PositionInspectionResponsiblePersonDTO responsiblePerson = this
                    .getResponsiblePersonByPositionCode(positionCode);

            // 循环调用验证 直到validatePositionStage返回true才会往下走
            RuleConfigDomain validRuleConfig = null;
            for (RuleConfigDomain ruleConfig : ruleConfigList) {
                // 验证阵地阶段是否满足规则配置要求
                if (ruleConfig.validatePositionStage(positionStageEnum)) {
                    validRuleConfig = ruleConfig;
                    log.info("找到符合条件的规则配置: ruleCode={}", ruleConfig.getRuleCode());
                    break;
                }
            }

            // 如果没有找到有效的规则配置，则返回错误信息
            if (validRuleConfig == null) {
                log.warn("未找到符合当前阵地阶段的规则配置: positionCode={}, positionStage={}", positionCode,
                        positionStageEnum.getCode());
                return "未找到符合当前阵地阶段的规则配置";
            }

            // 调用春光接口获取建设编码
            // 构造com.xiaomi.cnzone.storeapi.model.channelbuild.business.req.PositionDataReq
            PositionDataReq positionDataReq = new PositionDataReq();
            positionDataReq.setPositionCode(positionCode);
            Result<ChannelBaseV1Resp> constructionActionCodeResult = this.buildChannelPositionProvider
                    .getLatestByPositionCode(positionDataReq);
            IntlRetailAssert.nonNull(constructionActionCodeResult, "constructionActionCodeResult is null");
            // 验证code部位空
            IntlRetailAssert.nonNull(constructionActionCodeResult.getData(),
                    "constructionActionCodeResult.getData() is null");
            IntlRetailAssert.nonNull(constructionActionCodeResult.getData().getReportCode(),
                    "constructionActionCodeResult.getData().getReportCode() is null");
            String constructionActionCode = String.valueOf(constructionActionCodeResult.getData().getReportCode());
            // String constructionActionCode = "demo1";
            InspectionRecordDomain existsInspectionRecordDomain = inspectionRecordRepository
                    .getByBusinessCodeAndConstructionActionCode(positionCode, constructionActionCode);
            // 如果existsInspectionRecordDomain不为null则抛异常
            IntlRetailAssert.isNull(existsInspectionRecordDomain,
                    "positionCode" + positionCode + " constructionActionCode" + constructionActionCode + " 已经存在巡检记录");

            // 4. 组装InspectionRecordDomain
            InspectionRecordDomain inspectionRecordDomain = new InspectionRecordDomain();
            inspectionRecordDomain.setPositionCode(positionCode);
            inspectionRecordDomain.setPositionConstructionType(positionStageEnum.getConstructionType());
            Instant utcInstant = Instant.now().truncatedTo(ChronoUnit.MILLIS);
            inspectionRecordDomain.setPositionCreationTime(utcInstant.toEpochMilli());
            inspectionRecordDomain.setInspectionOwner(operatorId);
            // setPositionCreationTime 为当前的时间戳
            inspectionRecordDomain.setInspectionOwnerMiId(0L);
            inspectionRecordDomain.setCreatedBy(operatorId);
            inspectionRecordDomain.setCreatedOn(utcInstant.toEpochMilli());
            inspectionRecordDomain.setModifiedBy(operatorId);
            inspectionRecordDomain.setModifiedOn(utcInstant.toEpochMilli());
            inspectionRecordDomain.setTaskCompletionTime(0L);
            inspectionRecordDomain.setUploadData("{}");
            inspectionRecordDomain.setCreatedOn(utcInstant.toEpochMilli());
            inspectionRecordDomain.setActionCode(constructionActionCode);

            // 根据巡检负责人是否为空设置不同的状态
            if (responsiblePerson == null) {
                // 巡检负责人为空，设置巡检状态为未下发，任务状态为未完成
                inspectionRecordDomain.setInspectionStatus(InspectionStatusEnum.NOT_ISSUED);
                inspectionRecordDomain.setTaskStatus(TaskStatusEnum.DEFAULT);
            } else {
                // 巡检负责人不为空，设置巡检状态为未完成，并设置巡检负责人信息
                inspectionRecordDomain.setInspectionStatus(InspectionStatusEnum.NOT_COMPLETED);
                inspectionRecordDomain.setTaskStatus(TaskStatusEnum.NOT_COMPLETED);
                inspectionRecordDomain.setReminderTime(
                        validRuleConfig.getStartTime() + validRuleConfig.getReminderDays() * 24 * 60 * 60 * 1000L);
                inspectionRecordDomain.setInspectionOwner(responsiblePerson.getUserName());
                inspectionRecordDomain.setInspectionOwnerMiId(
                        Optional.ofNullable(responsiblePerson.getMiId()).map(Long::valueOf).orElse(0L));
                // 下发任务
                // 创建并设置OrgAndMid对象
                CreateInstanceReq.OrgAndMid orgAndMid = new CreateInstanceReq.OrgAndMid();
                orgAndMid.setOrgId(positionInfo.getRmsPositionCode());
                orgAndMid.setMid(responsiblePerson.getMiId());

                // 设置时间字段
                orgAndMid.setSugStartTimeStamp(utcInstant.toEpochMilli());
                orgAndMid.setSugEndTimeStamp(validRuleConfig.getEndTime());
                orgAndMid.setDeadlineStamp(validRuleConfig.getEndTime());

                // 设置自定义参数
                Map<String, String> customParams = new HashMap<>();
                customParams.put("languageCode", responsiblePerson.getLanguageCode());
                orgAndMid.setCustomParams(customParams);
                Long taskBatchId = taskCenterServiceRpc.pushPositionInspectionTask(Lists.newArrayList(orgAndMid),
                        validRuleConfig);
                inspectionRecordDomain.setCreateInstanceTime(utcInstant.toEpochMilli());
                // 处理返回taskBatchId 不为null 且跟validRuleConfig.getTaskBatchId() 不相等 则更新validRuleConfig.getTaskBatchId()
                if (taskBatchId != null && !taskBatchId.equals(validRuleConfig.getTaskBatchId())) {
                    if (validRuleConfig.getTaskBatchId() != null && !Long.valueOf("0").equals(validRuleConfig.getTaskBatchId())) {
                        log.warn(
                                "push position inspection task batchId is not null, taskBatchId: {}, validRuleConfig.taskBatchId: {}",
                                taskBatchId, validRuleConfig.getTaskBatchId());
                    }
                    validRuleConfig.setTaskBatchId(taskBatchId);
                    ruleConfigRepository.update(validRuleConfig);
                }
            }

            // 设置规则编码
            inspectionRecordDomain.setRuleCode(validRuleConfig.getRuleCode());

            // 7. 如果返回true则将调用InspectionRecordRepository#save
            boolean saveResult = inspectionRecordRepository.save(inspectionRecordDomain);

            if (saveResult) {
                return "成功创建阵地巡检记录";
            } else {
                log.error("保存阵地巡检记录失败: positionCode={}", positionCode);
                return "保存阵地巡检记录失败";
            }
        } catch (Exception e) {
            log.error("创建阵地巡检记录异常", e);
            return "创建阵地巡检记录异常: " + e.getMessage() + " " + positionCode + " " + areaId;
        }
    }
    /**
     * 初始化巡检记录
     *
     * @param request
     * @return 创建结果
     */
    @Override
    public String initInspectionByPositionCode(InitInspectionByPositionCodeRequest request) {
        log.info("根据阵地编码创建巡检记录: countryId={}, positionCode={}, actionType={}, actionTime={}",
                request.getCountryId(), request.getPositionCode(), request.getActionType(), request.getActionTime());

        try {
            // dubbo服务
            ListPositionInfoRequest listPositionInfoRequest = new ListPositionInfoRequest();
            listPositionInfoRequest.setPositionCodeList(Collections.singletonList(request.getPositionCode()));
            Result<ListPositionInfoResponse> positionInfoResult = positionProvider
                    .listPositionInfo(listPositionInfoRequest);
            ListPositionInfoResponse listPositionInfoResponse = positionInfoResult.getData();
            // 验证是否为空
            if (listPositionInfoResponse == null || listPositionInfoResponse.getList() == null
                    || listPositionInfoResponse.getList().isEmpty()) {
                log.warn("阵地信息在主数据系统不存在: positionCode={}", request.getPositionCode());
                return "阵地信息不存在";
            }
            // 查找对应的rule
            List<RuleConfigDomain> ruleConfigList = ruleConfigRepository.findValidRuleConfigsByCountry(request.getCountryId(), 301);
            if (ruleConfigList == null || ruleConfigList.isEmpty()) {
                log.warn("未找到对应的规则配置: country={}, taskType=301", request.getCountryId());
                return "未找到对应的规则配置: " + request.getCountryId();
            }
            // 循环调用验证 直到validatePositionStage返回true才会往下走
            RuleConfigDomain validRuleConfig = null;
            for (RuleConfigDomain ruleConfig : ruleConfigList) {
                // 验证阵地阶段是否满足规则配置要求
                validRuleConfig = ruleConfig;
                log.info("找到符合条件的规则配置: ruleCode={}", ruleConfig.getRuleCode());
            }
            // 如果没有找到有效的规则配置，则返回错误信息
            if (validRuleConfig == null) {
                log.warn("未找到符合当前阵地阶段的规则配置: positionCode={}", request.getPositionCode());
                return "未找到符合当前阵地阶段的规则配置";
            }
            // 调用春光接口获取建设编码
            // 构造com.xiaomi.cnzone.storeapi.model.channelbuild.business.req.PositionDataReq
            PositionDataReq positionDataReq = new PositionDataReq();
            positionDataReq.setPositionCode(request.getPositionCode());
            Result<ChannelBaseV1Resp> constructionActionCodeResult = this.buildChannelPositionProvider
                    .getLatestByPositionCode(positionDataReq);
            String constructionActionCode = "";
            if (constructionActionCodeResult != null
                    && constructionActionCodeResult.getData() != null
                    && constructionActionCodeResult.getData().getReportCode() != null) {
                constructionActionCode = String.valueOf(constructionActionCodeResult.getData().getReportCode());
            }
            InspectionRecordDomain existsInspectionRecordDomain = inspectionRecordRepository
                    .getByBusinessCodeAndConstructionActionCode(request.getPositionCode(), constructionActionCode);
            // 如果existsInspectionRecordDomain不为null则抛异常
            IntlRetailAssert.isNull(existsInspectionRecordDomain,
                    "positionCode" + request.getPositionCode() + " constructionActionCode" + constructionActionCode + " 已经存在巡检记录");
            // 获取巡检负责人
            PositionInspectionResponsiblePersonDTO responsiblePerson = this
                    .getResponsiblePersonByPositionCode(request.getPositionCode());
            ListPositionInfoResponse.PositionInfo positionInfo = listPositionInfoResponse.getList().get(0);

            InspectionRecordDomain inspectionRecordDomain = new InspectionRecordDomain();
            inspectionRecordDomain.setPositionCode(request.getPositionCode());
            inspectionRecordDomain.setPositionConstructionType(InspectionBusinessTypeEnum.getByCode(request.getActionType()));
            Instant utcInstant = Instant.now().truncatedTo(ChronoUnit.MILLIS);
            inspectionRecordDomain.setPositionCreationTime(utcInstant.toEpochMilli());
            //inspectionRecordDomain.setInspectionOwner(operatorId);
            // setPositionCreationTime 为当前的时间戳
            inspectionRecordDomain.setInspectionOwnerMiId(0L);
            //inspectionRecordDomain.setCreatedBy(operatorId);
            inspectionRecordDomain.setCreatedOn(utcInstant.toEpochMilli());
            //inspectionRecordDomain.setModifiedBy(operatorId);
            inspectionRecordDomain.setModifiedOn(utcInstant.toEpochMilli());
            inspectionRecordDomain.setTaskCompletionTime(0L);
            inspectionRecordDomain.setUploadData("{}");
            inspectionRecordDomain.setCreatedOn(utcInstant.toEpochMilli());
            // 只有建店系统的才会有actionCode，RMS的没有
            if (!constructionActionCode.isEmpty()) {
                inspectionRecordDomain.setActionCode(constructionActionCode);
            }
            // 根据巡检负责人是否为空设置不同的状态
            if (responsiblePerson == null) {
                // 巡检负责人为空，设置巡检状态为未下发，任务状态为未完成
                inspectionRecordDomain.setInspectionStatus(InspectionStatusEnum.NOT_ISSUED);
                inspectionRecordDomain.setTaskStatus(TaskStatusEnum.DEFAULT);
            } else {
                // 巡检负责人不为空，设置巡检状态为未完成，并设置巡检负责人信息
                inspectionRecordDomain.setInspectionStatus(InspectionStatusEnum.NOT_COMPLETED);
                inspectionRecordDomain.setTaskStatus(TaskStatusEnum.NOT_COMPLETED);
                inspectionRecordDomain.setReminderTime(
                        utcInstant.toEpochMilli() + validRuleConfig.getReminderDays() * 24 * 60 * 60 * 1000L);
                inspectionRecordDomain.setInspectionOwner(responsiblePerson.getUserName());
                inspectionRecordDomain.setInspectionOwnerMiId(
                        Optional.ofNullable(responsiblePerson.getMiId()).map(Long::valueOf).orElse(0L));
                // 下发任务
                // 创建并设置OrgAndMid对象
                CreateInstanceReq.OrgAndMid orgAndMid = new CreateInstanceReq.OrgAndMid();
                orgAndMid.setOrgId(positionInfo.getRmsPositionCode());
                orgAndMid.setMid(responsiblePerson.getMiId());

                // 设置时间字段
                orgAndMid.setSugStartTimeStamp(utcInstant.toEpochMilli());
                orgAndMid.setSugEndTimeStamp(validRuleConfig.getEndTime());
                orgAndMid.setDeadlineStamp(validRuleConfig.getEndTime());

                // 设置自定义参数
                Map<String, String> customParams = new HashMap<>();
                customParams.put("languageCode", responsiblePerson.getLanguageCode());
                orgAndMid.setCustomParams(customParams);
                Long taskBatchId = taskCenterServiceRpc.pushPositionInspectionTask(Lists.newArrayList(orgAndMid),
                        validRuleConfig);
                inspectionRecordDomain.setCreateInstanceTime(utcInstant.toEpochMilli());
                // 处理返回taskBatchId 不为null 且跟validRuleConfig.getTaskBatchId() 不相等 则更新validRuleConfig.getTaskBatchId()
                if (taskBatchId != null && !taskBatchId.equals(validRuleConfig.getTaskBatchId())) {
                    if (validRuleConfig.getTaskBatchId() != null && !Long.valueOf("0").equals(validRuleConfig.getTaskBatchId())) {
                        log.warn(
                                "push position inspection task batchId is not null, taskBatchId: {}, validRuleConfig.taskBatchId: {}",
                                taskBatchId, validRuleConfig.getTaskBatchId());
                    }
                    validRuleConfig.setTaskBatchId(taskBatchId);
                    ruleConfigRepository.update(validRuleConfig);
                }
            }
            // 设置规则编码
            inspectionRecordDomain.setRuleCode(validRuleConfig.getRuleCode());

            // 7. 如果返回true则将调用InspectionRecordRepository#save
            boolean saveResult = inspectionRecordRepository.save(inspectionRecordDomain);

            if (saveResult) {
                return request.getPositionCode() + "成功创建阵地巡检记录";
            } else {
                log.error("保存阵地巡检记录失败: positionCode={}", request.getPositionCode());
                return request.getPositionCode() + "保存阵地巡检记录失败";
            }
        } catch (Exception e) {
            log.error("创建阵地巡检记录异常", e);
            return "创建阵地巡检记录异常: " + e.getMessage() + " " + request.getPositionCode() + " " + request.getCountryId();
        }
    }

    /**
     * 根据业务代码获取巡检记录列表
     *
     * @param businessCode 业务代码
     * @return 巡检记录领域对象列表
     */
    @Override
    public List<InspectionRecordDomain> getInspectionRecordsByBusinessCode(String businessCode) {
        if (businessCode == null || businessCode.trim().isEmpty()) {
            return new ArrayList<>();
        }

        // 调用仓储层获取数据
        return inspectionRecordRepository.getByBusinessCode(businessCode);
    }

    @Override
    public List<ImageCenterDto> getImageCenterData(List<InspectionRecordDomain> inspectionRecords) {
        List<ImageCenterDto> result = new ArrayList<>();
        if (inspectionRecords == null || inspectionRecords.isEmpty()) {
            return result;
        }
        for (InspectionRecordDomain record : inspectionRecords) {
            // 只返回巡检校验通过后的记录的照片
            if (InspectionStatusEnum.VERIFICATION_PASSED.equals(record.getInspectionStatus())) {
                // 创建图片中心DTO
                ImageCenterDto imageCenterDto = new ImageCenterDto();
                if (record.getUploadData() == null || record.getUploadData().trim().isEmpty()) {
                    continue;
                }

                // 设置阵地建设类型（从第一条有效记录中获取）
                if (record.getPositionConstructionType() != null) {
                    imageCenterDto.setPositionConstructionType(record.getPositionConstructionType().getStatus());
                }

                // 设置创建时间和修改时间
                if (record.getCreatedOn() != null) {
                    imageCenterDto.setCreatedOn(record.getCreatedOn());
                }

                if (record.getModifiedOn() != null) {
                    imageCenterDto.setModifiedOn(record.getModifiedOn());
                }

                // 将JSON字符串转换为UploadDate对象
                UploadData uploadData = JsonUtil.json2bean(record.getUploadData(), UploadData.class);
                if (uploadData == null) {
                    continue;
                }

                // 处理UploadData中的图片数据，通过guid获取对应的图片URL
                uploadData = processUploadDataImages(uploadData);
                // 处理各种类型的照片
                addPhotoGroupToList(uploadData.getStoreGate(), imageCenterDto::getStoreGate, imageCenterDto::setStoreGate);
                addPhotoGroupToList(uploadData.getPositionLandingPhoto(), imageCenterDto ::getPositionLandingPhoto,
                        imageCenterDto::setPositionLandingPhoto);
                addPhotoGroupToList(uploadData.getPositionDisplay(), imageCenterDto::getPositionDisplay,
                        imageCenterDto::setPositionDisplay);

                // 处理家具照片列表 - 合并所有家具照片
                addFurniturePhotos(uploadData.getFurniturePictures(), imageCenterDto);
                result.add(imageCenterDto);
            }
        }
        return result;
    }

    /**
     * 处理家具照片列表，将所有家具的照片添加到DTO中
     *
     * @param furniturePictures 家具照片列表
     * @param imageCenterDto    图片中心DTO
     */
    private void addFurniturePhotos(List<FurniturePhotoGroup> furniturePictures, ImageCenterDto imageCenterDto) {
        if (CollectionUtils.isEmpty(furniturePictures)) {
            return;
        }

        // 确保家具照片列表已初始化
        if (imageCenterDto.getFurniturePictures() == null) {
            imageCenterDto.setFurniturePictures(new ArrayList<>());
        }

        // 将所有家具的照片链接添加到同一个列表中
        furniturePictures
                .forEach(furniture -> addPhotoGroupToList(furniture, imageCenterDto::getFurniturePictures, null));
    }

    /**
     * 将照片组中的图片添加到目标列表中
     *
     * @param photoGroup 照片组
     * @param getter     获取目标列表的方法
     * @param setter     设置目标列表的方法
     */
    private void addPhotoGroupToList(PhotoGroup photoGroup, java.util.function.Supplier<List<String>> getter,
                                     java.util.function.Consumer<List<String>> setter) {
        if (photoGroup == null || CollectionUtils.isEmpty(photoGroup.getImages())) {
            return;
        }

        List<String> targetList = getter.get();
        if (targetList == null && setter != null) {
            targetList = new ArrayList<>();
            setter.accept(targetList);
        }

        if (targetList != null) {
            targetList.addAll(photoGroup.getImages());
        }
    }

    /**
     * 查询阵地家具列表
     *
     * @param request 阵地家具查询请求参数
     * @return 阵地家具列表
     */
    @Override
    public List<OptionalItem<Integer>> getPositionFurnitureList(PositionFurnitureRequest request) {
        // 远程调用获取家具数据
        PositionListRequest positionListRequest = new PositionListRequest();
        positionListRequest.setPositionCode(request.getPositionCode());
        PositionListResponse positionListResponse = RpcUtil
                .getRpcResult(positionProvider.listStorePosition(positionListRequest));
        // 解析返回值中的家具对象
        List<PositionListResponse.PositionExtra> extraList = Optional.ofNullable(positionListResponse)
                .map(PositionListResponse::getList)
                .orElse(Collections.emptyList())
                .stream().map(PositionListResponse.StorePositionInfo::getPositionExtra)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        // 新家具编码
        List<String> newFurnitureCodeList = extraList.stream().map(PositionListResponse.PositionExtra::getFurnitureList)
                .filter(Objects::nonNull).flatMap(List::stream)
                .filter(f -> f.getFurnitureCount() != null && f.getFurnitureCount() > 0)
                .flatMap(f -> Stream.generate(f::getFurnitureCode).limit(f.getFurnitureCount()))
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(newFurnitureCodeList)) {
            List<ConfigFurnitureResp> configFurnitureRespList = RpcUtil
                    .getRpcResult(channelFurnitureProvider.queryFurnitureList(new FurnitureDetailReq()));
            Map<String, String> furnitureId2NameMap =
                    configFurnitureRespList.stream().collect(Collectors.toMap(ConfigFurnitureResp::getFurnitureId,
                            ConfigFurnitureResp::getUniqueFurnitureName));
            MutableInt mutableInt = new MutableInt(0);
            return newFurnitureCodeList.stream().filter(furnitureId2NameMap::containsKey).map(furnitureId2NameMap::get)
                    .map(f -> new OptionalItem<>(mutableInt.getAndIncrement(), f))
                    .collect(Collectors.toList());
        }

        // 旧家具列表
        return extraList.stream()
                .map(PositionListResponse.PositionExtra::getFurnitureDisplay)
                .filter(Objects::nonNull)
                .map(this::parseToOptionalItemList)
                .flatMap(List::stream).collect(Collectors.toList());
    }

    /**
     * 解析出有库存的家具列表
     *
     * @param display 家具显示对象
     * @return 可选的家具列表
     */
    private List<OptionalItem<Integer>> parseToOptionalItemList(PositionListResponse.FurnitureDisplay display) {
        List<OptionalItem<Integer>> items = new ArrayList<>();
        MutableInt mutableInt = new MutableInt(0);
        addItemIfInStock(items, "1 Slot-End Cap Table", display.getSlotEndCapTable1(), mutableInt);
        addItemIfInStock(items, "2 Slots-End Cap Table", display.getSlotEndCapTable2(), mutableInt);
        addItemIfInStock(items, "3 Slots-End Cap Table", display.getSlotEndCapTable3(), mutableInt);
        addItemIfInStock(items, "4 Slots-End Cap Table", display.getSlotEndCapTable4(), mutableInt);
        addItemIfInStock(items, "4 Slots-Experience Table", display.getSlotExperienceTable4(), mutableInt);
        addItemIfInStock(items, "6 Slots-Experience Table", display.getSlotExperienceTable6(), mutableInt);
        addItemIfInStock(items, "8 Slots-Experience Table", display.getSlotExperienceTable8(), mutableInt);
        addItemIfInStock(items, "10 Slots-Experience Table", display.getSlotExperienceTable10(), mutableInt);
        addItemIfInStock(items, "12 Slots-Experience Table", display.getSlotExperienceTable12(), mutableInt);
        addItemIfInStock(items, "Channel-Exclusive Furniture (Xiaomi products only)",
                display.getChannelExclusiveFurnitureXiaomi(), mutableInt);
        addItemIfInStock(items, "Channel-Exclusive Furniture (multi-brand products)",
                display.getChannelExclusiveFurnitureMultiBrand(), mutableInt);
        addItemIfInStock(items, "Display Area", display.getDisplayArea(), mutableInt);
        addItemIfInStock(items, "Display Shelf", display.getDisplayShelf(), mutableInt);
        addItemIfInStock(items, "Accessories Island", display.getAccessoriesIsland(), mutableInt);
        addItemIfInStock(items, "Display Platform", display.getDisplayPlatform(), mutableInt);
        addItemIfInStock(items, "Glass Counter", display.getGlassCounter(), mutableInt);
        addItemIfInStock(items, "Kiosk", display.getKiosk(), mutableInt);
        addItemIfInStock(items, "Other Shaped Furniture", display.getOtherShapedFurniture(), mutableInt);
        return items;
    }

    /**
     * 如果有库存,则加入到返回值家具列表中
     *
     * @param items 家具列表
     * @param key 列表Key
     * @param quantity 库存数量
     * @param mutableInt
     */
    private void addItemIfInStock(List<OptionalItem<Integer>> items, String key, Integer quantity,
                                  MutableInt mutableInt) {
        if (quantity != null && quantity > 0) {
            for (Integer i = 0; i < quantity; i++) {
                items.add(new OptionalItem<>(mutableInt.getAndIncrement(), key));
            }
        }
    }

    private String getLanguageKeyFromHeader() {
        // 从 request 中获取 headers 整个 map
        final HttpServletRequest request = CommonUtils.getCurrentRequest();
        if (request == null) {
            return StringUtils.EMPTY;
        }
        return StringUtils.isEmpty(request.getHeader("X-Retail-Language")) ? StringUtils.EMPTY
                : request.getHeader("X-Retail-Language");
    }

    /**
     * 阵地巡检任务下发
     * 流程：1.调用getCountriesAtMidnight获取国家列表
     * 2.通过getPendingInspectionsByCountries获取未下发的巡检任务
     * 3.通过巡检任务的门店code获取门店负责人，对比巡检任务现有负责人并更新
     * 4.下发任务调用接口createInstance
     *
     * @return 处理结果
     */
    @Override
    public CommonApiResponse<String> dispatchInspectionTasks(Integer regularTime) {
        log.info("开始执行阵地巡检任务下发流程");
        try {
            // 1. 获取当前时间为凌晨0点的国家列表
            List<String> countriesAtMidnight = countryTimeUtil.getCountriesAtMidnight(regularTime);
            log.info("当前时间为凌晨0点的国家列表: {}", countriesAtMidnight);

            if (countriesAtMidnight.isEmpty()) {
                log.info("当前没有处于凌晨0点的国家，不需要下发任务");
                return new CommonApiResponse<>("当前没有处于凌晨0点的国家，不需要下发任务");
            }

            // 2. 获取这些国家未下发的巡检任务
            List<InspectionRecordDTO> pendingInspections = inspectionRecordReadMapper
                    .selectPendingInspectionsByCountries(countriesAtMidnight,
                            InspectionBusinessTypeEnum.getPostionInspectionCodeList());
            log.info("获取到{}个未下发的巡检任务", pendingInspections.size());

            if (pendingInspections.isEmpty()) {
                log.info("没有需要下发的巡检任务");
                return new CommonApiResponse<>("没有需要下发的巡检任务");
            }

            int successCount = 0;
            int failCount = 0;

            // 3. 处理每个未下发的巡检任务
            for (InspectionRecordDTO inspection : pendingInspections) {
                try {
                    // 获取巡检任务的阵地编码
                    String positionCode = inspection.getBusinessCode();
                    if (positionCode == null || positionCode.isEmpty()) {
                        log.warn("巡检任务的阵地编码为空，跳过处理: inspectionId={}", inspection.getId());
                        failCount++;
                        continue;
                    }

                    // 3. 获取门店负责人并更新巡检任务
                    PositionInspectionResponsiblePersonDTO responsiblePerson = getResponsiblePersonByPositionCode(
                            positionCode);
                    if (responsiblePerson == null || responsiblePerson.getMiId() == null) {
                        log.warn("无法获取阵地负责人信息，跳过处理: positionCode={}", positionCode);
                        failCount++;
                        continue;
                    }

                    // 对比并更新巡检任务负责人
                    boolean needsUpdate = false;
                    if (!Objects.equals(String.valueOf(responsiblePerson.getMiId()),
                            String.valueOf(inspection.getInspectionOwnerMiId()))) {
                        inspection.setInspectionOwnerMiId(responsiblePerson.getMiId());
                        needsUpdate = true;
                    }

                    if (!Objects.equals(responsiblePerson.getUserName(), inspection.getInspectionOwner())) {
                        inspection.setInspectionOwner(responsiblePerson.getUserName());
                        needsUpdate = true;
                    }

                    if (needsUpdate) {
                        InspectionRecord inspectionRecord = new InspectionRecord();
                        inspectionRecord.setId(inspection.getId());
                        inspectionRecord.setInspectionOwnerMiId(responsiblePerson.getMiId());
                        inspectionRecord.setInspectionOwner(responsiblePerson.getUserName());
                        inspectionRecordReadMapper.updateById(inspectionRecord);
                        log.info("更新巡检任务负责人: inspectionId={}, newOwner={}", inspection.getId(),
                                responsiblePerson.getUserId());
                    }
                    // 4. 下发任务调用接口createInstance 和转发接口
                    if (needsUpdate && inspection.getInspectionStatus().equals(InspectionStatusEnum.NOT_COMPLETED.getCode())) {
                        // 更新任务负责人
                        TaskCenterChangeExecutorReq taskReq = new TaskCenterChangeExecutorReq();
                        taskReq.setTaskBatchId(inspection.getTaskBatchId());
                        taskReq.setNewMid(responsiblePerson.getMiId());
                        taskReq.setMid(inspection.getInspectionOwnerMiId());
                        taskReq.setPositionCode(positionCode);
                        taskCenterServiceRpc.changeExecutor(taskReq);
                    } else if (inspection.getInspectionStatus().equals(InspectionStatusEnum.NOT_ISSUED.getCode())) {
                        //下发任务调用接口
                        boolean dispatched = dispatchTaskToTaskCenter(inspection, responsiblePerson);
                        if (dispatched) {

                            log.info("成功下发巡检任务: inspectionId={}", inspection.getId());
                            successCount++;
                        } else {
                            log.warn("下发巡检任务失败: inspectionId={}", inspection.getId());
                            failCount++;
                        }
                    }

                } catch (Exception e) {
                    log.error("处理巡检任务异常: inspectionId={}", inspection.getId(), e);
                    failCount++;
                }
            }

            String resultMessage = String.format("阵地巡检任务下发完成: 成功=%d, 失败=%d", successCount, failCount);
            log.info(resultMessage);
            return new CommonApiResponse<>(resultMessage);

        } catch (Exception e) {
            log.error("阵地巡检任务下发异常", e);
            return new CommonApiResponse<>(500, "阵地巡检任务下发异常: " + e.getMessage(), null);
        }
    }

    /**
     * 下发任务到任务中心
     *
     * @param inspection        巡检记录
     * @param responsiblePerson 负责人信息
     * @return 是否成功下发
     */
    private boolean dispatchTaskToTaskCenter(InspectionRecordDTO inspection,
                                             PositionInspectionResponsiblePersonDTO responsiblePerson) {
        try {

            // 创建下发任务请求
            CreateInstanceReq req = new CreateInstanceReq();
            req.setTaskDefinitionId(Long.valueOf(inspection.getTaskDefId()));
            req.setBusinessTypeName("PositionInspection");
            // 开始时间 = 配置的开始时间 + 3小时(3 * 60 * 60 * 1000毫秒)
            req.setSugStartTimeStamp(inspection.getStartTime());
            req.setSugEndTimeStamp(inspection.getStartTime() + inspection.getReminderDays() * 24 * 60 * 60 * 1000L);
            req.setDeadlineStamp(inspection.getEndTime());
            req.setBusinessId(inspection.getRuleCode());
            req.setCreateType(2);
            req.setTitle("阵地巡检");
            req.setRetailTenantId("2");
            req.setRetailAppSign("CHANNEL_RETAIL");

            // 其他设置...
            // 创建并设置OrgAndMid对象
            CreateInstanceReq.OrgAndMid orgAndMid = new CreateInstanceReq.OrgAndMid();
            orgAndMid.setOrgId(responsiblePerson.getPositionCode());
            orgAndMid.setMid(responsiblePerson.getMiId());

            // 设置时间字段
            orgAndMid.setSugStartTimeStamp(inspection.getStartTime());
            orgAndMid.setSugEndTimeStamp(
                    inspection.getStartTime() + inspection.getReminderDays() * 24 * 60 * 60 * 1000L);
            orgAndMid.setDeadlineStamp(inspection.getEndTime());

            // 设置自定义参数
            Map<String, String> customParams = new HashMap<>();
            customParams.put("languageCode", responsiblePerson.getLanguageCode());
            orgAndMid.setCustomParams(customParams);
            List<CreateInstanceReq.OrgAndMid> getOrgAndMidLists = new ArrayList<>();
            // 添加到列表
            getOrgAndMidLists.add(orgAndMid);
            req.setOrgAndMidLists(getOrgAndMidLists);
            // 调用任务中心接口下发任务
            CreateInstanceResp resp = taskCenterServiceRpc.createInstance(req);
            // 处理返回结果
            if (resp != null && resp.getTaskBatchId() != null) {
                log.info("{},{}, taskBatchId={}", inspection.getId(), resp.getTaskDefinitionId(),
                        resp.getTaskBatchId());
                // 先查一次数据库
                RuleConfigDomain rule = ruleConfigRepository.getByRuleCode(inspection.getRuleCode());
                if (inspection != null && inspection.getTaskBatchId() == 0L) {
                    // 只有 task_batch_id 为空或为0时才更新
                    rule.setId(inspection.getRuleId());
                    rule.setTaskBatchId(resp.getTaskBatchId());
                    ruleConfigRepository.update(rule);

                }

                InspectionRecord inspectionRecord = inspectionRecordReadMapper.selectById(inspection.getId());
                // 更新巡检状态为已下发（未完成）
                inspectionRecord.setInspectionStatus(InspectionStatusEnum.NOT_COMPLETED.getCode());
                inspectionRecord.setTaskStatus(TaskStatusEnum.NOT_COMPLETED.getCode());
                // 获取当前时间的 Instant 对象
                Instant now = Instant.now();
                // 获取当前时间的时间戳（以毫秒为单位）
                long timestamp = now.toEpochMilli();

                inspectionRecord.setTaskCreateInstanceTime(timestamp);
                inspectionRecord.setReminderTime(
                        inspection.getStartTime() + inspection.getReminderDays() * 24 * 60 * 60 * 1000L);
                inspectionRecordReadMapper.updateById(inspectionRecord);

                return true;

            } else {
                log.warn("下发任务到任务中心失败或返回结果为空: inspectionId={}", inspection.getId());
                return false;

            }

        } catch (Exception e) {
            log.error("下发任务到任务中心异常", e);
            return false;
        }
    }

    /**
     * 将PositionImageCenterResp列表转换为UploadData
     *
     * @param positionImageCenterRespList PositionImageCenterResp列表
     * @return UploadData对象
     */
    private UploadData convertPositionImageCenterRespToUploadData(
            List<PositionImageCenterResp> positionImageCenterRespList) {
        UploadData uploadData = new UploadData();

        // 初始化家具图片列表
        List<FurniturePhotoGroup> furniturePictures = new ArrayList<>();

        for (PositionImageCenterResp resp : positionImageCenterRespList) {
            // 处理门店大门图片
            if (!CollectionUtils.isEmpty(resp.getStoreGate())) {
                List<String> urls = ((List<?>) resp.getStoreGate()).stream().map(obj -> {
                    if (obj instanceof Multimedia) {
                        return ((Multimedia) obj).getUrl();
                    } else if (obj instanceof Map) {
                        // 用objectMapper转换
                        Multimedia m = objectMapper.convertValue(obj, Multimedia.class);
                        return m.getUrl();
                    } else {
                        return null;
                    }
                }).filter(StringUtils::isNotEmpty).collect(Collectors.toList());
                PhotoGroup storeGate = new PhotoGroup();
                storeGate.setImages(urls);
                uploadData.setStoreGate(storeGate);
            }
            // 处理阵地展示图片
            if (!CollectionUtils.isEmpty(resp.getPositionDisplay())) {
                List<String> urls = ((List<?>) resp.getPositionDisplay()).stream().map(obj -> {
                    if (obj instanceof Multimedia) {
                        return ((Multimedia) obj).getUrl();
                    } else if (obj instanceof Map) {
                        Multimedia m = objectMapper.convertValue(obj, Multimedia.class);
                        return m.getUrl();
                    } else {
                        return null;
                    }
                }).filter(StringUtils::isNotEmpty).collect(Collectors.toList());
                PhotoGroup positionDisplay = new PhotoGroup();
                positionDisplay.setImages(urls);
                uploadData.setPositionDisplay(positionDisplay);
            }
            // 处理家具图片
            if (!CollectionUtils.isEmpty(resp.getFurniturePicture())) {
                List<String> urls = ((List<?>) resp.getFurniturePicture()).stream().map(obj -> {
                    if (obj instanceof Multimedia) {
                        return ((Multimedia) obj).getUrl();
                    } else if (obj instanceof Map) {
                        Multimedia m = objectMapper.convertValue(obj, Multimedia.class);
                        return m.getUrl();
                    } else {
                        return null;
                    }
                }).filter(StringUtils::isNotEmpty).collect(Collectors.toList());
                FurniturePhotoGroup furniturePicture = new FurniturePhotoGroup();
                furniturePicture.setImages(urls);
                furniturePictures.add(furniturePicture);
            }
            // 处理阵地概览图片（映射到阵地落位）
            if (!CollectionUtils.isEmpty(resp.getPositionOverview())) {
                List<String> urls = ((List<?>) resp.getPositionOverview()).stream().map(obj -> {
                    if (obj instanceof Multimedia) {
                        return ((Multimedia) obj).getUrl();
                    } else if (obj instanceof Map) {
                        Multimedia m = objectMapper.convertValue(obj, Multimedia.class);
                        return m.getUrl();
                    } else {
                        return null;
                    }
                }).filter(StringUtils::isNotEmpty).collect(Collectors.toList());
                PhotoGroup positionLandingPhoto = new PhotoGroup();
                positionLandingPhoto.setImages(urls);
                uploadData.setPositionLandingPhoto(positionLandingPhoto);
            }
        }

        // 设置家具图片列表
        if (!furniturePictures.isEmpty()) {
            uploadData.setFurniturePictures(furniturePictures);
        }

        return uploadData;
    }

    @Transactional
    public void updateInspectionRecordAndFinishTask(PositionImageInfo img, String operatorName) {
        InspectionRecordDomain inspectionRecord = img.getInspectionRecord();
        PositionDomain positionDomain = img.getPositionDomain();
        IntlRetailAssert.nonNull(positionDomain, "PositionDomain not found.");
        fileUploadService.save(img.getFileUploadInfo());
        inspectionRecordRepository.update(inspectionRecord);
        InspectionHistoryDomain history = new InspectionHistoryDomain();
        history.setInspectionRecordId(inspectionRecord.getId());
        history.setOperationType(OperationType.SUBMIT);
        history.setOperator(inspectionRecord.getModifiedBy());
        // 使用传递的operatorName，如果为空则使用operator
        history.setOperatorName(operatorName != null ? operatorName : inspectionRecord.getModifiedBy());
        history.setOperationTime(inspectionRecord.getModifiedOn());
        history.setCreateTime(inspectionRecord.getModifiedOn());
        inspectionHistoryRepository.save(history);
        taskCenterServiceRpc.outerTaskFinish(toTaskCenterFinishReq(img, positionDomain.getPositionCode()));
    }

    private TaskCenterFinishReq toTaskCenterFinishReq(PositionImageInfo img, String rmsPositionCode) {
        TaskCenterFinishReq taskCenterFinishReq = new TaskCenterFinishReq();
        taskCenterFinishReq.setTaskBatchId(img.getRuleConfig().getTaskBatchId());
        taskCenterFinishReq.setMid(img.getInspectionRecord().getInspectionOwnerMiId());
        taskCenterFinishReq.setOperatorMid(img.getInspectionRecord().getInspectionOwnerMiId());
        taskCenterFinishReq.setOrgId(rmsPositionCode);
        return taskCenterFinishReq;
    }

    /**
     * 处理UploadData中的图片数据，通过guid获取对应的图片URL
     *
     * @param uploadData 上传数据
     * @return 处理后的UploadData
     */
    private UploadData processUploadDataImages(UploadData uploadData) {
        if (uploadData == null) {
            return null;
        }

        try {
            // 提取所有PhotoGroup中的guid
            List<String> allGuids = new ArrayList<>();

            // 收集单个PhotoGroup的guid
            if (uploadData.getStoreGate() != null && StringUtils.isNotEmpty(uploadData.getStoreGate().getGuid())) {
                allGuids.add(uploadData.getStoreGate().getGuid());
            }
            if (uploadData.getPositionLandingPhoto() != null && StringUtils.isNotEmpty(uploadData.getPositionLandingPhoto().getGuid())) {
                allGuids.add(uploadData.getPositionLandingPhoto().getGuid());
            }
            if (uploadData.getPositionDisplay() != null && StringUtils.isNotEmpty(uploadData.getPositionDisplay().getGuid())) {
                allGuids.add(uploadData.getPositionDisplay().getGuid());
            }

            // 收集家具图片列表中的guid
            if (uploadData.getFurniturePictures() != null) {
                uploadData.getFurniturePictures().stream()
                        .filter(Objects::nonNull)
                        .filter(furniturePhotoGroup -> StringUtils.isNotEmpty(furniturePhotoGroup.getGuid()))
                        .forEach(furniturePhotoGroup -> allGuids.add(furniturePhotoGroup.getGuid()));
            }

            // 如果没有guid，直接返回原始数据
            if (allGuids.isEmpty()) {
                return uploadData;
            }

            // 调用文件上传服务获取图片URL
            Map<String, List<String>> guidToUrlsMap = fileUploadService.getUrlsByModuleAndGuids(
                    FileUploadEnum.POSITION_INSPECTION, allGuids);

            // 更新各个PhotoGroup中的images字段
            if (uploadData.getStoreGate() != null && StringUtils.isNotEmpty(uploadData.getStoreGate().getGuid())) {
                List<String> urls = guidToUrlsMap.get(uploadData.getStoreGate().getGuid());
                if (urls != null && !urls.isEmpty()) {
                    uploadData.getStoreGate().setImages(urls);
                }
            }

            if (uploadData.getPositionLandingPhoto() != null && StringUtils.isNotEmpty(uploadData.getPositionLandingPhoto().getGuid())) {
                List<String> urls = guidToUrlsMap.get(uploadData.getPositionLandingPhoto().getGuid());
                if (urls != null && !urls.isEmpty()) {
                    uploadData.getPositionLandingPhoto().setImages(urls);
                }
            }

            if (uploadData.getPositionDisplay() != null && StringUtils.isNotEmpty(uploadData.getPositionDisplay().getGuid())) {
                List<String> urls = guidToUrlsMap.get(uploadData.getPositionDisplay().getGuid());
                if (urls != null && !urls.isEmpty()) {
                    uploadData.getPositionDisplay().setImages(urls);
                }
            }

            // 更新家具图片列表
            if (uploadData.getFurniturePictures() != null) {
                uploadData.getFurniturePictures().stream()
                        .filter(Objects::nonNull)
                        .filter(furniturePhotoGroup -> StringUtils.isNotEmpty(furniturePhotoGroup.getGuid()))
                        .forEach(furniturePhotoGroup -> {
                            List<String> urls = guidToUrlsMap.get(furniturePhotoGroup.getGuid());
                            if (urls != null && !urls.isEmpty()) {
                                furniturePhotoGroup.setImages(urls);
                            }
                            AbnormalReasonEnum abnormalReasonEnum = AbnormalReasonEnum.getByCode(furniturePhotoGroup.getReason());
                            //判断reason是否为Other，如果不是，则给remark赋值原因desc
                            if (abnormalReasonEnum != null && !AbnormalReasonEnum.OTHER.equals(abnormalReasonEnum)) {
                                furniturePhotoGroup.setRemark(abnormalReasonEnum.getDesc());
                            }
                        });
            }

        } catch (Exception e) {
            log.error("处理UploadData图片数据异常", e);
        }

        return uploadData;
    }
}
