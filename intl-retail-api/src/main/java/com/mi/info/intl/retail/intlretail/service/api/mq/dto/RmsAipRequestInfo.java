package com.mi.info.intl.retail.intlretail.service.api.mq.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class RmsAipRequestInfo {
    private String path;
    private String type;
    private String account;
    private Long mid;
    private String positionId;
    private String countryCode;
    private String positionCode;
    
    public RmsAipRequestInfo(String path, String type, String account, String storeId) {
        this.path = path;
        this.type = type;
        this.account = account;
        this.positionId = storeId;
    }
    
    public RmsAipRequestInfo(String path, String type, String account, String storeId, String positionCode) {
        this.path = path;
        this.type = type;
        this.account = account;
        this.positionId = storeId;
        this.positionCode = positionCode;
    }
}
