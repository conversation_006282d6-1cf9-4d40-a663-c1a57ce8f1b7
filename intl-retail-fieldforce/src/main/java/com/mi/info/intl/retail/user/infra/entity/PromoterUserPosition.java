package com.mi.info.intl.retail.user.infra.entity;

import lombok.Data;

/**
 * 促销员用户阵地信息实体类
 * 用于映射新的促销员查询结果
 *
 * <AUTHOR>
 * @date 2025-01-08
 */
@Data
public class PromoterUserPosition {

    /**
     * 用户ID (user.rms_userid)
     */
    private String userId;

    /**
     * 用户MI ID (user.mi_id)
     */
    private String mid;

    /**
     * 用户名称 (user.english_name)
     */
    private String userName;

    /**
     * 区域代码 (user.country_shortcode)
     */
    private String areaCode;

    /**
     * 语言代码 (user.language_code)
     */
    private String languageCode;

    /**
     * 职位代码 (user.job_id)
     */
    private Integer titleCode;

    /**
     * 职位名称 (user.job_name)
     */
    private String titleName;

    /**
     * 门店ID (p.position_id)
     */
    private String storeId;

    /**
     * 阵地代码 (p.code)
     */
    private String positionCode;

    /**
     * 阵地类型 (p.type)
     */
    private Integer positionType;

    /**
     * 阵地名称 (p.name)
     */
    private String positionName;

    /**
     * 渠道类型ID (p.channel_type)
     */
    private Integer channelTypeId;

    /**
     * 渠道类型名称 (p.channel_type_name)
     */
    private String channelTypeName;
}