env: test
miwork:
  alarm:
    groupId:
      p0: oc_1e6d1e54ac5eae84730fe55e86a013e7
      p1: oc_1e6d1e54ac5eae84730fe55e86a013e7
      p2: oc_1e6d1e54ac5eae84730fe55e86a013e7

# 加密配置
itsm:
  key@kc-sid: india-sales.g
  key: GCC_dXFDqR6UmNulwNx8LZrw1JMK68LlB40ceJKmJ4lBNRgSjrf6mb3rR4WDzaVZn7xc87__GBCdCPLL-apOlYw2Jxx1ShexGBQTxLIzWvxHDHwVl2mGl9xX5JTRogA
  aesGcmKey@kc-sid: india-sales.g
  aesGcmKey: GDCtHFFCXnxDA6frlNh0r98fRUrntsw1EtJeJWkEl2NZ0hMwM0pqK8IU/ioff3qbp1oYEjA1pL7oHEv5jeTSidAun/um/xgQdaL4VXQYSOuyQr+a8zZtgRgUHCiGZMhXytwtIiRb+akikk4IjiQA

oaucf:
  auth:
    appId: dthJf4KwiRL6          # 应用的id
    appSecret: mX0P9kaZqG2yN8Rm  # 应用的secret
  bpm:
    enabled: true
    url: https://bpm-infra.test.mioffice.cn/runtime    # api的url
    connectTimeout: 10          # 连接超时时间 单位秒
    readTimeout: 60

intelTemple-url:
  planUploadUrl: https://qhdx-intl-retail.alsgp0.mi-fds.com/qhdx-intl-retail/新LDU计划维护批量上传模版.xlsx?GalaxyAccessKeyId=5151729087601&Expires=9223372036854775807&Signature=Vp8TqXTJ%2Btt0FKOoDKnCgf2KnxU%3D
  planStopUrl: https://qhdx-intl-retail.alsgp0.mi-fds.com/qhdx-intl-retail/新LDU计划批量停用模版.xlsx?GalaxyAccessKeyId=5151729087601&Expires=9223372036854775807&Signature=GdmHwryYdZTSo1a1AwLbvYXUyz0%3D
  targetUploadUrl: https://qhdx-intl-retail.alsgp0.mi-fds.com/qhdx-intl-retail/新LDU目标维护批量上传模版.xlsx?GalaxyAccessKeyId=5151729087601&Expires=9223372036854775807&Signature=ZW9gWwm%2Bt8KgtYzcx4uLVlfQ%2FA4%3D
  newProductTarget: https://intl-retail.alsgp0.mi-fds.com/intl-retail/pc_new_product_inspection/newProductTargetTemplate.xlsx?GalaxyAccessKeyId=5151729087601&Expires=9223372036854775807&Signature=fSsN9LN4bJU34r5qwEv76wqK0bM%3D
oapi:
  app-id@kc-sid: india-sales.g
  # cli_a60d6cea8478d063
  app-id: GCBHEKHqftUV57dyUC7oqRxSvV08I/GvF4Bck3lpr+BrGxgSH7eGryR4QOWaRDT5OaRCYGn/GBC9257wBHHzeOA9hZl2qsBlGBSfwKJUetylaJNygCCA+/CNZKuFdSUAEgA=
  app-secret@kc-sid: india-sales.g
  # BUIyDUcTVC9Qj69jnoM5ah7XvYzdFgel
  app-secret: GDDX4DMrN8fsx8ptI9op/1nPRufkVZ+mGs/a6DZjQ+pgrOcuxughu9y7ctqT4nB+5FAYEgs7/3oPZkqXmPH1ZfmVE5iW/xgQUFlva10lzS/7NhPk8QUYTRgUkxyHDFQrj/JBYMST+brRJUv/eNglABIA
  missing-rule-alert-template: '{"elements":[{"tag":"column_set","horizontal_spacing":"8px","horizontal_align":"left","columns":[{"tag":"column","width":"weighted","elements":[{"tag":"markdown","content":"{content}","text_align":"left","text_size":"normal_v2"}]}]}],"header":{"title":{"tag":"plain_text","content":"{title}"},"template":"red"}}'
  url: https://open.f.mioffice.cn


proretail:
  project:
    id: 11
    name: intl-retail

intl-retail:
  global-data-access:
    http: true

job:
  admin:
    addresses: http://sg-job.test.be.mi.com
  accessToken@kc-sid: india-sales.g
  accessToken: GCD7+RwBNl3FwKOCDkLYIhtwHjd4SJFfPwHQUVlDUZk92BgSssynWjn6TZ2r7yCeJTpDep//GBAQs0iw3OxDnLlD9P8QarKaGBR0GP4KGjS5ZTo0EI0mFHyo72BZFwA=
  executor:
    appname: intl-retail
    ip: ""
    port: 9991
    logpath: /home/<USER>/log/xmstore-objective/jobhandler
    logretentiondays: 30
dubbo:
  nr-upload-center:
    appId: xm-yp-upc-0048
    appKey: 941bfb141f2141401b2d254d67bc3eed
    url: http://sgp-xmmionegw.test.mi.com/mtop/file


feishu:
  appId: cli_a60d6cea8478d063
  appSecret@kc-sid: india-sales.g
  appSecret: GDDNlEYkzhoTik1dVyPqlcWPXVuogQbNfUiP2XLy9yAcGh5KLYgoMC1c6EzmjJeTYzAYEqJPvtpFFkOqmbN025peIFY0/xgQcfNLEUowQH+GXGG8jS8HjBgUOfevm5PGoheSWZsXTgWMPCbIiUUA
  getGroupListUrl: https://open.f.mioffice.cn/open-apis/im/v1/chats?page_token=
  getUnionidsUrl: https://open.f.mioffice.cn/open-apis/contact/v3/users/batch_get_id
  sendMessageGroupUrl: https://open.f.mioffice.cn/open-apis/message/v4/send/
  batchSendMessageUrl: https://open.f.mioffice.cn/open-apis/message/v4/batch_send/
  tokenUrl: https://open.f.mioffice.cn/open-apis/auth/v3/tenant_access_token/internal
  messageUrl: https://open.f.mioffice.cn/open-apis/im/v1/messages


dubbo:
  nr-upload-center:
    appId: xm-yp-upc-0048
    appKey: 941bfb141f2141401b2d254d67bc3eed
    url: http://sgp-xmmionegw.test.mi.com/mtop/file