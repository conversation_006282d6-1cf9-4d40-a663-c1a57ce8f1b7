<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.info.intl.retail.user.infra.mapper.IntlRmsUserMapper">

    <resultMap id="BaseResultMap" type="com.mi.info.intl.retail.user.infra.entity.IntlRmsUser">
        <id property="id" column="id" />
        <result property="rmsUserid" column="rms_userid" />
        <result property="code" column="code" />
        <result property="domainName" column="domain_name" />
        <result property="englishName" column="english_name" />
        <result property="countryId" column="country_id" />
        <result property="countryName" column="country_name" />
        <result property="jobId" column="job_id" />
        <result property="jobName" column="job_name" />
        <result property="email" column="email" />
        <result property="mobile" column="mobile" />
        <result property="miId" column="mi_id" />
        <result property="managerId" column="manager_id" />
        <result property="managerName" column="manager_name" />
        <result property="virtualMiId" column="virtual_mi_id" />
        <result property="languageId" column="language_id" />
        <result property="languageName" column="language_name" />
        <result property="languageCode" column="language_code" />
        <result property="isDisabled" column="is_disabled" />
        <result property="createdOn" column="created_on" />
        <result property="modifiedOn" column="modified_on" />
        <result property="timezoneCode" column="timezone_code" />
        <result property="timezoneName" column="timezone_name" />
        <result property="createdAt" column="created_at" />
        <result property="updatedAt" column="updated_at" />
        <result property="countryShortcode" column="country_shortcode" />
    </resultMap>


    <sql id="Base_Column_List">
     id, rms_userid, code, domain_name, english_name, country_id, country_name, job_id, job_name, email, mobile, mi_id, manager_id, manager_name,
       virtual_mi_id, language_id, language_name, language_code, is_disabled, created_on, modified_on, timezone_code, timezone_name, created_at, updated_at, country_shortcode
    </sql>

    <select id="selectByEmail" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from intl_rms_user
        where email = #{email}
    </select>
    <select id="selectByDomainName" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from intl_rms_user
        where domain_name = #{domainName}
        and is_disabled = 0 and job_id != 0 limit 1
    </select>

    <select id="selectByMiId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from intl_rms_user
        where mi_id = #{miId}
    </select>
</mapper>
