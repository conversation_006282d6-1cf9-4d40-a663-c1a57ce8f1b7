package com.mi.info.intl.retail.ldu.app.service.impl;

import java.util.List;

import javax.annotation.Resource;

import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import com.mi.info.intl.retail.intlretail.service.api.ldu.SnImeiService;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.SnImeiGoodsInfoDto;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.SnImeiInfoDto;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.SnImeiQueryDto;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.SnImeiValidationDto;
import com.mi.info.intl.retail.intlretail.service.api.retailer.dto.CommonResponse;
import com.mi.info.intl.retail.ldu.service.IntlLduSnImeiService;
import com.xiaomi.mone.docs.annotations.dubbo.ApiDoc;
import com.xiaomi.mone.docs.annotations.dubbo.ApiModule;
import com.xiaomi.mone.docs.annotations.http.MiApiRequestMethod;

/**
 * <AUTHOR>
 * @description sn/imei查询校验服务实现类
 * @date 2025/7/8 11:20
 */
@ApiModule(value = "国际渠道零售服务", apiInterface = SnImeiService.class)
@Service
@DubboService(group = "${dubbo-group.provider.intl-retail:}", interfaceClass = SnImeiService.class)
public class SnImeiServiceImpl implements SnImeiService {

    @Resource
    private IntlLduSnImeiService intlLduSnImeiService;


    @ApiDoc(name = "根据sn/imei查询商品信息", value = "/api/ldureport/querySnImeiInfo", method = MiApiRequestMethod.POST)
    @Override
    public CommonResponse<List<SnImeiInfoDto>> querySnImeiInfo(SnImeiQueryDto snImeiQueryDto) {
        List<SnImeiInfoDto> snImeiInfos = intlLduSnImeiService.querySnImeiInfo(snImeiQueryDto);
        return new CommonResponse<>(snImeiInfos);
    }

    @ApiDoc(name = "根据sn/imei校验商品信息", value = "/api/ldureport/validateSnImeiInfo", method = MiApiRequestMethod.POST)
    @Override
    public CommonResponse<List<SnImeiValidationDto>> validateSnImeiInfo(SnImeiQueryDto snImeiQueryDto) {
        List<SnImeiValidationDto> list = intlLduSnImeiService.validateSnImeiInfo(snImeiQueryDto);
        return new CommonResponse<>(list);
    }

    @ApiDoc(name = "根据sn/imei查询商品信息", value = "/api/ldureport/queryGoodsInfoBySnImei", method = MiApiRequestMethod.POST)
    @Override
    public CommonResponse<List<SnImeiGoodsInfoDto>> queryGoodsInfoBySnImei(SnImeiQueryDto snImeiQueryDto) {
        List<SnImeiGoodsInfoDto> snImeiGoodsInfoDtoList = intlLduSnImeiService.queryGoodsInfoBySnImeis(snImeiQueryDto);
        return new CommonResponse<>(snImeiGoodsInfoDtoList);
    }


}
