package com.mi.info.intl.retail.intlretail.service.api.store.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

@Setter
@Getter
public class RetailerInfoRequest implements Serializable {

    private static final long serialVersionUID = 973432739743971L;

    @JsonProperty("codes")
    private List<String> codes;

    @JsonProperty("region")
    private String region;

}
