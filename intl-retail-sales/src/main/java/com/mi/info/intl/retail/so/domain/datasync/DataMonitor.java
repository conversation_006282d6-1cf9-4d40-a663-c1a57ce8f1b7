package com.mi.info.intl.retail.so.domain.datasync;

import com.mi.info.intl.retail.so.domain.datasync.dto.DataDifferenceDto;
import com.mi.info.intl.retail.so.domain.datasync.enums.DataCompareResult;
import com.mi.info.intl.retail.so.domain.upload.entity.IntlSoImei;
import com.mi.info.intl.retail.so.domain.upload.entity.IntlSoQty;
import com.mi.info.intl.retail.so.domain.upload.service.IntlSoImeiService;
import com.mi.info.intl.retail.so.domain.upload.service.IntlSoQtyService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 数据双向同步对比差异监控服务
 * 支持 IMEI 和 QTY 两种数据类型的对比
 */
@Slf4j
@Service
public class DataMonitor {

    @Autowired
    private IntlSoImeiService intlSoImeiService;

    @Autowired
    private IntlSoQtyService intlSoQtyService;

    @Autowired
    private RmsService rmsService;

    @Autowired
    private RobotNotificationService robotNotificationService;

    @Value("${intl-retail.data-monitor.enabled:false}")
    private boolean monitorEnabled;

    private static final int BATCH_SIZE = 1000;

    /**
     * 定时执行数据差异监控（每天凌晨2点执行）
     */
    public void monitorDataDifference() {
        if (!monitorEnabled) {
            log.debug("Data monitor is disabled, skipping monitoring");
            return;
        }

        try {
            log.info("sync so data monitor start...");

            // 计算前一天的时间范围
            LocalDate yesterday = LocalDate.now().minusDays(1);
            long startTime = yesterday.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
            long endTime = yesterday.plusDays(1).atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();

            log.info("monitor date range：{} - {} ({} - {})", startTime, endTime,
                    new java.util.Date(startTime), new java.util.Date(endTime));

            // 执行 IMEI 数据对比
            List<DataDifferenceDto> imeiDifferences = compareImeiData(startTime, endTime);

            // 执行 QTY 数据对比
            List<DataDifferenceDto> qtyDifferences = compareQtyData(startTime, endTime);

            // 发送对比报告
            robotNotificationService.sendDataDifferenceReport(imeiDifferences, qtyDifferences);

            log.info("sync so data monitor run successful.");

        } catch (Exception e) {
            log.error("sync so data monitor run  error", e);
            robotNotificationService.sendSimpleNotification("数据监控执行失败",
                    "执行数据差异监控时发生错误: " + e.getMessage());
        }
    }

    /**
     * 对比 IMEI 数据
     */
    private List<DataDifferenceDto> compareImeiData(long startTime, long endTime) {
        log.info("compare IMEI data...");

        // 创建统计变量
        Map<DataCompareResult, Long> categoryCount = new HashMap<>();
        Map<DataCompareResult, List<String>> categoryIds = new HashMap<>();

        // 初始化统计
        for (DataCompareResult result : DataCompareResult.values()) {
            categoryCount.put(result, 0L);
            categoryIds.put(result, new ArrayList<>());
        }

        // 1. 从零售系统角度对比（零售系统有，RMS系统没有或状态不一致）
        compareRetailToRmsImei(startTime, endTime, categoryCount, categoryIds);

        // 2. 从RMS系统角度对比（RMS系统有，零售系统没有）
        compareRmsToRetailImei(startTime, endTime, categoryCount, categoryIds);

        // 构建返回结果
        return buildDifferenceDtoList(categoryCount, categoryIds);
    }

    /**
     * 对比 QTY 数据
     */
    private List<DataDifferenceDto> compareQtyData(long startTime, long endTime) {
        log.info("compare QTY data...");

        // 创建统计变量
        Map<DataCompareResult, Long> categoryCount = new HashMap<>();
        Map<DataCompareResult, List<String>> categoryIds = new HashMap<>();

        // 初始化统计
        for (DataCompareResult result : DataCompareResult.values()) {
            categoryCount.put(result, 0L);
            categoryIds.put(result, new ArrayList<>());
        }

        // 1. 从零售系统角度对比（零售系统有，RMS系统没有或状态不一致）
        compareRetailToRmsQty(startTime, endTime, categoryCount, categoryIds);

        // 2. 从RMS系统角度对比（RMS系统有，零售系统没有）
        compareRmsToRetailQty(startTime, endTime, categoryCount, categoryIds);

        // 构建返回结果
        return buildDifferenceDtoList(categoryCount, categoryIds);
    }

    /**
     * 从零售系统角度对比 IMEI 数据
     */
    private void compareRetailToRmsImei(long startTime, long endTime,
                                        Map<DataCompareResult, Long> categoryCount,
                                        Map<DataCompareResult, List<String>> categoryIds) {
        long afterId = 0;
        boolean hasMore = true;

        while (hasMore) {
            // 获取一批本地IMEI变更数据
            List<IntlSoImei> retailList = intlSoImeiService.lambdaQuery()
                    .ge(IntlSoImei::getModifiedOn, startTime)
                    .lt(IntlSoImei::getModifiedOn, endTime)
                    .gt(IntlSoImei::getId, afterId)
                    .orderByAsc(IntlSoImei::getId)
                    .last("LIMIT " + BATCH_SIZE)
                    .list();

            if (retailList.isEmpty()) {
                break;
            }
            afterId = retailList.get(retailList.size() - 1).getId();
            hasMore = retailList.size() == BATCH_SIZE;
            // 分离有RMS ID和没有RMS ID的数据
            List<String> rmsIds = new ArrayList<>();
            List<Long> retailIds = new ArrayList<>();

            for (IntlSoImei imei : retailList) {
                if (StringUtils.isNotBlank(imei.getRmsId())) {
                    rmsIds.add(imei.getRmsId());
                } else {
                    retailIds.add(imei.getId());
                }
            }

            // 批量查询RMS系统数据
            Map<String, IntlSoImei> rmsByRmsIdMap = new HashMap<>();
            Map<Long, IntlSoImei> rmsByRetailIdMap = new HashMap<>();

            if (!rmsIds.isEmpty()) {
                List<IntlSoImei> rmsList1 = rmsService.batchGetImeiByRmsIds(rmsIds);
                for (IntlSoImei imei : rmsList1) {
                    rmsByRmsIdMap.put(imei.getRmsId(), imei);
                }
            }

            if (!retailIds.isEmpty()) {
                List<IntlSoImei> rmsList2 = rmsService.batchGetImeiByRetailIds(retailIds);
                for (IntlSoImei imei : rmsList2) {
                    rmsByRetailIdMap.put(imei.getId(), imei);
                }
            }

            // 对比数据
            for (IntlSoImei retailImei : retailList) {
                IntlSoImei rmsImei = null;

                if (StringUtils.isNotBlank(retailImei.getRmsId())) {
                    rmsImei = rmsByRmsIdMap.get(retailImei.getRmsId());
                } else {
                    rmsImei = rmsByRetailIdMap.get(retailImei.getId());
                }

                if (rmsImei == null) {
                    // 零售系统有，RMS系统没有
                    recordDifference(DataCompareResult.RETAIL_ONLY, String.valueOf(retailImei.getId()),
                            categoryCount, categoryIds);
                } else if (!compareImeiStatus(retailImei, rmsImei)) {
                    // 状态不一致
                    recordDifference(DataCompareResult.STATUS_INCONSISTENT, String.valueOf(retailImei.getId()),
                            categoryCount, categoryIds);
                }
            }
        }
    }

    /**
     * 从RMS系统角度对比 IMEI 数据
     */
    private void compareRmsToRetailImei(long startTime, long endTime,
                                        Map<DataCompareResult, Long> categoryCount,
                                        Map<DataCompareResult, List<String>> categoryIds) {
        String afterRmsId = null;
        boolean hasMore = true;

        while (hasMore) {
            List<IntlSoImei> rmsList = rmsService.getImeiFormRmsByPage(startTime, endTime, afterRmsId);
            if (rmsList.isEmpty()) {
                break;
            }
            hasMore = rmsList.size() == BATCH_SIZE;
            afterRmsId = rmsList.get(rmsList.size() - 1).getRmsId();

            // 收集RMS ID列表
            List<String> rmsIds = new ArrayList<>();
            for (IntlSoImei imei : rmsList) {
                if (StringUtils.isNotBlank(imei.getRmsId())) {
                    rmsIds.add(imei.getRmsId());
                }
            }

            if (rmsIds.isEmpty()) {
                return;
            }
            // 查询零售系统中对应的数据
            List<IntlSoImei> retailList = intlSoImeiService.lambdaQuery()
                    .in(IntlSoImei::getRmsId, rmsIds)
                    .list();

            Map<String, IntlSoImei> retailByRmsIdMap = new HashMap<>();
            for (IntlSoImei imei : retailList) {
                retailByRmsIdMap.put(imei.getRmsId(), imei);
            }

            // 对比数据
            for (IntlSoImei rmsImei : rmsList) {
                if (StringUtils.isNotBlank(rmsImei.getRmsId())) {
                    continue;
                }
                IntlSoImei retailImei = retailByRmsIdMap.get(rmsImei.getRmsId());
                if (retailImei == null) {
                    // RMS系统有，零售系统没有
                    recordDifference(DataCompareResult.RMS_ONLY, rmsImei.getRmsId(),
                            categoryCount, categoryIds);
                } else if (!compareImeiStatus(retailImei, rmsImei)) {
                    // 状态不一致（从RMS角度检查）
                    recordDifference(DataCompareResult.STATUS_INCONSISTENT, rmsImei.getRmsId(),
                            categoryCount, categoryIds);
                }
            }
        }

    }

    /**
     * 从零售系统角度对比 QTY 数据
     */
    private void compareRetailToRmsQty(long startTime, long endTime,
                                       Map<DataCompareResult, Long> categoryCount,
                                       Map<DataCompareResult, List<String>> categoryIds) {
        long afterId = 0;
        boolean hasMore = true;

        while (hasMore) {
            // 获取一批本地QTY变更数据
            List<IntlSoQty> retailList = intlSoQtyService.lambdaQuery()
                    .ge(IntlSoQty::getModifiedon, startTime)
                    .lt(IntlSoQty::getModifiedon, endTime)
                    .gt(IntlSoQty::getId, afterId)
                    .orderByAsc(IntlSoQty::getId)
                    .last("LIMIT " + BATCH_SIZE)
                    .list();

            if (retailList.isEmpty()) {
                break;
            }

            hasMore = retailList.size() == BATCH_SIZE;
            afterId = retailList.get(retailList.size() - 1).getId();

            // 分离有RMS ID和没有RMS ID的数据
            List<String> rmsIds = new ArrayList<>();
            List<Long> retailIds = new ArrayList<>();

            for (IntlSoQty qty : retailList) {
                if (StringUtils.isNotBlank(qty.getRmsId())) {
                    rmsIds.add(qty.getRmsId());
                } else {
                    retailIds.add(qty.getId());
                }
            }

            // 批量查询RMS系统数据
            Map<String, IntlSoQty> rmsByRmsIdMap = new HashMap<>();
            Map<Long, IntlSoQty> rmsByRetailIdMap = new HashMap<>();

            if (!rmsIds.isEmpty()) {
                List<IntlSoQty> rmsList1 = rmsService.batchGetQtyByRmsIds(rmsIds);
                for (IntlSoQty qty : rmsList1) {
                    rmsByRmsIdMap.put(qty.getRmsId(), qty);
                }
            }

            if (!retailIds.isEmpty()) {
                List<IntlSoQty> rmsList2 = rmsService.batchGetQtyByRetailIds(retailIds);
                for (IntlSoQty qty : rmsList2) {
                    rmsByRetailIdMap.put(qty.getId(), qty);
                }
            }

            // 对比数据
            for (IntlSoQty retailQty : retailList) {
                IntlSoQty rmsQty = null;

                if (StringUtils.isNotBlank(retailQty.getRmsId())) {
                    rmsQty = rmsByRmsIdMap.get(retailQty.getRmsId());
                } else {
                    rmsQty = rmsByRetailIdMap.get(retailQty.getId());
                }

                if (rmsQty == null) {
                    // 零售系统有，RMS系统没有
                    recordDifference(DataCompareResult.RETAIL_ONLY, String.valueOf(retailQty.getId()),
                            categoryCount, categoryIds);
                } else if (!compareQtyStatus(retailQty, rmsQty)) {
                    // 状态不一致
                    recordDifference(DataCompareResult.STATUS_INCONSISTENT, String.valueOf(retailQty.getId()),
                            categoryCount, categoryIds);
                }
            }
        }
    }

    /**
     * 从RMS系统角度对比 QTY 数据
     */
    private void compareRmsToRetailQty(long startTime, long endTime,
                                       Map<DataCompareResult, Long> categoryCount,
                                       Map<DataCompareResult, List<String>> categoryIds) {
        String afterRmsId = null;
        boolean hasMore = true;

        while (hasMore) {
            List<IntlSoQty> rmsList = rmsService.getQtyFormRmsByPage(startTime, endTime, afterRmsId);

            if (rmsList.isEmpty()) {
                break;
            }

            hasMore = rmsList.size() == BATCH_SIZE;
            afterRmsId = rmsList.get(rmsList.size() - 1).getRmsId();

            // 收集RMS ID列表
            List<String> rmsIds = new ArrayList<>();
            for (IntlSoQty qty : rmsList) {
                if (StringUtils.isNotBlank(qty.getRmsId())) {
                    rmsIds.add(qty.getRmsId());
                }
            }

            if (!rmsIds.isEmpty()) {
                // 查询零售系统中对应的数据
                List<IntlSoQty> retailList = intlSoQtyService.lambdaQuery()
                        .in(IntlSoQty::getRmsId, rmsIds)
                        .list();

                Map<String, IntlSoQty> retailByRmsIdMap = new HashMap<>();
                for (IntlSoQty qty : retailList) {
                    retailByRmsIdMap.put(qty.getRmsId(), qty);
                }

                // 对比数据
                for (IntlSoQty rmsQty : rmsList) {
                    if (StringUtils.isNotBlank(rmsQty.getRmsId())) {
                        IntlSoQty retailQty = retailByRmsIdMap.get(rmsQty.getRmsId());
                        if (retailQty == null) {
                            // RMS系统有，零售系统没有
                            recordDifference(DataCompareResult.RMS_ONLY, rmsQty.getRmsId(),
                                    categoryCount, categoryIds);
                        } else if (!compareQtyStatus(retailQty, rmsQty)) {
                            // 状态不一致（从RMS角度检查）
                            recordDifference(DataCompareResult.STATUS_INCONSISTENT, rmsQty.getRmsId(),
                                    categoryCount, categoryIds);
                        }
                    }
                }
            }
        }
    }

    /**
     * 记录差异到统计中
     */
    private void recordDifference(DataCompareResult category, String id,
                                  Map<DataCompareResult, Long> categoryCount,
                                  Map<DataCompareResult, List<String>> categoryIds) {
        categoryCount.put(category, categoryCount.getOrDefault(category, 0L) + 1L);
        List<String> ids = categoryIds.get(category);
        if (ids.size() < 10) {
            ids.add(id);
        }
    }

    /**
     * 构建差异DTO列表
     */
    private List<DataDifferenceDto> buildDifferenceDtoList(Map<DataCompareResult, Long> categoryCount,
                                                           Map<DataCompareResult, List<String>> categoryIds) {
        List<DataDifferenceDto> result = new ArrayList<>();
        for (DataCompareResult category : DataCompareResult.values()) {
            Long count = categoryCount.getOrDefault(category, 0L);
            List<String> ids = categoryIds.getOrDefault(category, new ArrayList<>());
            result.add(new DataDifferenceDto(category, count, ids));
        }
        return result;
    }

    /**
     * 比较 IMEI 状态是否一致
     */
    private boolean compareImeiStatus(IntlSoImei imei1, IntlSoImei imei2) {
        // 这里需要根据实际业务逻辑来判断状态是否一致
        // 示例：比较状态字段
        Integer status1 = imei1.getStatus();
        Integer status2 = imei2.getStatus();

        if (status1 == null && status2 == null) {
            return true;
        }
        if (status1 == null || status2 == null) {
            return false;
        }

        return status1.equals(status2);
    }

    /**
     * 比较 QTY 状态是否一致
     */
    private boolean compareQtyStatus(IntlSoQty qty1, IntlSoQty qty2) {
        // 这里需要根据实际业务逻辑来判断状态是否一致
        // 示例：比较状态字段
        Integer status1 = qty1.getStatus();
        Integer status2 = qty2.getStatus();

        if (status1 == null && status2 == null) {
            return true;
        }
        if (status1 == null || status2 == null) {
            return false;
        }

        return status1.equals(status2);
    }
}
