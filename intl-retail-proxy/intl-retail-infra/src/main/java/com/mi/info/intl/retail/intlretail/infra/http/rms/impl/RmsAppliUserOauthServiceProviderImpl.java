package com.mi.info.intl.retail.intlretail.infra.http.rms.impl;

import com.mi.info.intl.retail.exception.RmsApiException;
import com.mi.info.intl.retail.intlretail.domain.common.utils.JsonUtil;
import com.mi.info.intl.retail.intlretail.domain.http.rms.RmsAppliUserOauthServiceProvider;
import com.mi.info.intl.retail.intlretail.domain.http.rms.RmsStoreTokenPrivider;
import com.mi.info.intl.retail.intlretail.domain.http.rms.dto.RmsResponseBody;
import com.mi.info.intl.retail.intlretail.infra.config.AppliUserTokenConfig;
import com.mi.info.intl.retail.intlretail.infra.config.RmsRegionConfig;
import com.mi.info.intl.retail.intlretail.infra.http.rms.RmsErrorCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.NullArgumentException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Slf4j
@Service
public class RmsAppliUserOauthServiceProviderImpl implements RmsAppliUserOauthServiceProvider {


    @Autowired
    private RmsRegionConfig rmsRegionConfig;

    @Autowired
    private AppliUserTokenConfig appliUserTokenConfig;

    @Autowired
    private RmsStoreTokenPrivider rmsStoreTokenPrivider;

    @Autowired
    private RestTemplate restTemplate;

    private static final String ERROR_HTTP_METHOD_MESSAGE = "error:method is error,httpMethod:";
    static final String BEARER = "Bearer ";
    static final String SPECIAL_AREAID = "GLOBAL";

    @Override
    public <T> List<T> httpForList(String areaId, String path, Object requestBody, String httpMethod, Class<T> responseType) {
        HttpMethod method = HttpMethod.resolve(httpMethod);
        Assert.notNull(method, ERROR_HTTP_METHOD_MESSAGE + httpMethod);
        long start = System.currentTimeMillis();

        // 特殊处理GLOBAL视角的请求
        if (areaId.equalsIgnoreCase(SPECIAL_AREAID)) {
            return processGlobalRequest(path, requestBody, method, responseType);
        }

        Optional<String> envNameOptional = Optional.ofNullable(rmsRegionConfig.getRegions().get(areaId));
        String envName = envNameOptional.orElseThrow(() -> new NullArgumentException("Regions not found for areaId: " + areaId));

        Optional<String> hostOptional = Optional.ofNullable(appliUserTokenConfig.getResources().get(envName));
        String host = hostOptional.orElseThrow(() -> new NullArgumentException("Resources not found for envName: " + envName));

        String token = rmsStoreTokenPrivider.getToken(envName);
        String res = "";
        log.info("start to request areaId: {},  url: {}, method: {}, body: {}, ", areaId, host + path, httpMethod, JsonUtil.bean2json(requestBody));
        HttpHeaders headers = new HttpHeaders();
        headers.add("authorization", BEARER + token);
        headers.add("Content-Type", "application/json");

        RmsResponseBody rmsResponseBody;
        try {
            rmsResponseBody = this.restTemplate.exchange(host + path, method, new HttpEntity<>(requestBody, headers), RmsResponseBody.class).getBody();
            // 根据返回的编码进行逻辑处理
            if (rmsResponseBody == null) {
                throw new RmsApiException(0, "Rms error: rmsResponseBody is null");
            }
            if (!RmsErrorCode.SUCCESS.getCode().equals(rmsResponseBody.getCode())) {
                throw new RmsApiException(rmsResponseBody.getCode(), "Rms error: " + rmsResponseBody.getMessage());
            }
        } catch (RestClientException e) {
            throw new RmsApiException(400, e.getMessage(), e);
        }
        return JsonUtil.jsonArr2beanList(rmsResponseBody.getResult(), responseType);
    }

    @Override
    public <T> T httpForObject(String areaId, String path, Object requestBody, String httpMethod, Class<T> responseType) {
        Optional<String> envNameOptional = Optional.ofNullable(rmsRegionConfig.getRegions().get(areaId));
        String envName = envNameOptional.orElseThrow(() -> new NullArgumentException("Regions not found for areaId: " + areaId));

        Optional<String> hostOptional = Optional.ofNullable(appliUserTokenConfig.getResources().get(envName));
        String host = hostOptional.orElseThrow(() -> new NullArgumentException("Resources not found for envName: " + envName));

        HttpMethod method = HttpMethod.resolve(httpMethod);
        Assert.notNull(method, ERROR_HTTP_METHOD_MESSAGE + httpMethod);

        long start = System.currentTimeMillis();
        String token = rmsStoreTokenPrivider.getToken(envName);
        String res = "";
        log.info("start to request areaId: {},  url: {}, method: {}, body: {}, ", areaId, host + path, httpMethod, JsonUtil.bean2json(requestBody));

        HttpHeaders headers = new HttpHeaders();
        headers.add("authorization", BEARER + token);
        headers.add("Content-Type", "application/json");

        RmsResponseBody rmsResponseBody;
        try {
            rmsResponseBody = this.restTemplate.exchange(host + path, method, new HttpEntity<>(requestBody, headers), RmsResponseBody.class).getBody();
            // 根据返回的编码进行逻辑处理
            if (rmsResponseBody == null) {
                throw new RmsApiException(0, "Rms error: rmsResponseBody is null");
            }
            if (!RmsErrorCode.SUCCESS.getCode().equals(rmsResponseBody.getCode())) {
                throw new RmsApiException(rmsResponseBody.getCode(), "Rms error: " + rmsResponseBody.getMessage());
            }
        } catch (RestClientException e) {
            throw new RmsApiException(400, e.getMessage(), e);
        }
        return JsonUtil.json2bean(rmsResponseBody.getResult(), responseType);
    }

    @Override
    public RmsResponseBody httpForRmsResponseBody(String areaId, String path, Object requestBody, String httpMethod) {
        Optional<String> envNameOptional = Optional.ofNullable(rmsRegionConfig.getRegions().get(areaId));
        String envName = envNameOptional.orElseThrow(() -> new NullArgumentException("Regions not found for areaId: " + areaId));

        Optional<String> hostOptional = Optional.ofNullable(appliUserTokenConfig.getResources().get(envName));
        String host = hostOptional.orElseThrow(() -> new NullArgumentException("Resources not found for envName: " + envName));

        HttpMethod method = HttpMethod.resolve(httpMethod);
        Assert.notNull(method, ERROR_HTTP_METHOD_MESSAGE + httpMethod);

        long start = System.currentTimeMillis();
        String token = rmsStoreTokenPrivider.getToken(envName);
        String res = "";
        log.info("start to request areaId: {},  url: {}, method: {}, body: {}, ", areaId, host + path, httpMethod, JsonUtil.bean2json(requestBody));

        HttpHeaders headers = new HttpHeaders();
        headers.add("authorization", BEARER + token);
        headers.add("Content-Type", "application/json");

        RmsResponseBody rmsResponseBody;
        try {
            rmsResponseBody = this.restTemplate.exchange(host + path, method, new HttpEntity<>(requestBody, headers), RmsResponseBody.class).getBody();

        } catch (RestClientException e) {
            throw new RmsApiException(400, e.getMessage(), e);
        }
        return rmsResponseBody;
    }

    /*
     * 并行请求RMS多个环境的数据
     */
    private <T> List<T> processGlobalRequest(String path, Object requestBody, HttpMethod method, Class<T> responseType) {
        List<CompletableFuture<List<T>>> futures = appliUserTokenConfig.getResources().entrySet().stream()
                .map(entry -> CompletableFuture.supplyAsync(() -> {
                    String token = rmsStoreTokenPrivider.getToken(entry.getKey());
                    log.info("start to request areaId: {},  url: {}, method: {}, body: {}, ",
                            entry.getKey(), entry.getValue() + path, method.toString(), JsonUtil.bean2json(requestBody));
                    HttpHeaders headers = new HttpHeaders();
                    headers.add("authorization", BEARER + token);
                    headers.add("Content-Type", "application/json");
                    RmsResponseBody rmsResponseBody = this.restTemplate.exchange(entry.getValue() + path, method,
                            new HttpEntity<>(requestBody, headers), RmsResponseBody.class).getBody();
                    return JsonUtil.jsonArr2beanList(rmsResponseBody.getResult(), responseType);
                }))
                .collect(Collectors.toList());

        CompletableFuture<Void> allFutures =
                CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
        return allFutures.thenApply(v ->
                futures.stream()
                        .map(CompletableFuture::join)
                        .flatMap(List::stream)
                        .collect(Collectors.toList())
        ).join();
    }

    @Override
    public RmsResponseBody httpForRmsResponseBodyByJwtToken(String areaId, String path, Object requestBody, String httpMethod, String token) {
        Optional<String> envNameOptional = Optional.ofNullable(rmsRegionConfig.getRegions().get(areaId));
        String envName = envNameOptional.orElseThrow(() -> new NullArgumentException("Regions not found for areaId: " + areaId));

        Optional<String> hostOptional = Optional.ofNullable(appliUserTokenConfig.getResources().get(envName));
        String host = hostOptional.orElseThrow(() -> new NullArgumentException("Resources not found for envName: " + envName));

        HttpMethod method = HttpMethod.resolve(httpMethod);
        Assert.notNull(method, ERROR_HTTP_METHOD_MESSAGE + httpMethod);

        long start = System.currentTimeMillis();
        String res = "";
        log.info("start to request areaId: {},  url: {}, method: {}, body: {}, ", areaId, host + path, httpMethod, JsonUtil.bean2json(requestBody));

        HttpHeaders headers = new HttpHeaders();
        headers.add("authorization", BEARER + token);
        headers.add("Content-Type", "application/json");

        RmsResponseBody rmsResponseBody;
        try {
            rmsResponseBody = this.restTemplate.exchange(host + path, method, new HttpEntity<>(requestBody, headers), RmsResponseBody.class).getBody();

        } catch (RestClientException e) {
            throw new RmsApiException(400, e.getMessage(), e);
        }
        return rmsResponseBody;
    }
}
