# 数据库设计文档

## 概述
本文档包含国际零售系统的数据库表结构设计。

## DDL 文件列表

### 门店等级相关表
- [门店等级规则表](./ddl/2025-08-05-store_grade_rule.sql) - 存储门店等级规则配置信息
- [门店等级关系表](./ddl/2025-08-05-store_grade_relation.sql) - 存储门店与等级规则的关联关系

### 其他表
- [其他表结构](./ddl/2025-08-04.sql) - 其他数据库表结构

## 表结构说明

### store_grade_rule (门店等级规则表)
该表用于存储门店等级规则的配置信息，支持多国家、多渠道的等级规则管理。

**主要字段：**
- `id`: 主键ID
- `country_code`: 国家代码，用于多国家数据隔离
- `channel_type`: 渠道类型，如 RETAILER、WHOLESALE 等
- `method`: 计算方法，如 CURRENT、RELATION 等
- `retailer_name`: 零售商名称
- `retailer_code`: 零售商代码
- `s_min_count` ~ `d_min_count`: 各等级的最小数量阈值
- `rules_status`: 规则状态
- `approve_status`: 审批状态
- `application_time`: 申请时间
- `approved_time`: 审批时间
- `last_calculation_time`: 最后计算时间
- `create_time`: 创建时间
- `update_time`: 更新时间
- `create_by`: 创建人
- `update_by`: 更新人
- `is_deleted`: 软删除标记

**索引设计：**
- 主键索引：`id`
- 复合索引：`idx_country_channel` (`country_code`, `channel_type`, `is_deleted`) - 用于按国家代码和渠道类型查询，同时过滤已删除数据

### store_grade_relation (门店等级关系表)
该表用于存储门店与等级规则的关联关系，支持多国家、多规则的门店等级管理。

**主要字段：**
- `id`: 主键ID
- `country_code`: 国家代码，用于多国家数据隔离
- `store_grade_rule_id`: 门店等级规则ID，关联到具体的等级规则
- `store_code`: 门店代码，唯一标识门店
- `store_grade`: 门店等级，如 S、A、B、C、D 等
- `create_time`: 创建时间
- `update_time`: 更新时间
- `create_by`: 创建人
- `update_by`: 更新人
- `is_deleted`: 软删除标记

**索引设计：**
- 主键索引：`id`
- 复合索引：`idx_rule_country` (`store_grade_rule_id`, `country_code`, `is_deleted`) - 用于按规则ID和国家代码查询，同时过滤已删除数据
- 复合索引：`idx_store_code` (`store_code`, `country_code`) - 用于按门店代码和国家代码查询

## 使用场景

1. **门店等级规则管理**: 通过 `store_grade_rule` 表管理不同国家、不同渠道的等级规则
2. **门店等级统计**: 通过 `store_grade_rule_id` 和 `country_code` 查询特定规则下的门店等级分布
3. **门店等级查询**: 通过 `store_code` 查询特定门店的等级信息
4. **等级变更记录**: 记录门店等级的历史变更
5. **多国家支持**: 通过 `country_code` 实现多国家数据隔离

## 注意事项

1. 所有查询都应该包含 `country_code` 条件以确保数据隔离
2. 使用软删除机制，删除操作只是标记 `is_deleted = 1`
3. 国家代码字段用于多国家数据隔离，查询时必须指定
4. 建议定期清理已删除的数据以优化性能
