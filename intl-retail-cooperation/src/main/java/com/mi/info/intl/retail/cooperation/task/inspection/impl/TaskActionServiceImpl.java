package com.mi.info.intl.retail.cooperation.task.inspection.impl;

import com.mi.info.intl.retail.api.task.dto.TaskActionRequest;
import com.mi.info.intl.retail.api.task.enums.TaskActionEnum;
import com.mi.info.intl.retail.cooperation.task.config.InspectionConfig;
import com.mi.info.intl.retail.cooperation.task.infra.entity.IntlBigPromotionConf;
import com.mi.info.intl.retail.cooperation.task.infra.mapper.read.IntlBigPromotionConfReadMapper;
import com.mi.info.intl.retail.api.task.TaskActionService;
import com.mi.info.intl.retail.utils.JsonUtil;
import com.mi.info.intl.retail.utils.intl.IntlTimeUtil;
import com.xiaomi.cnzone.brain.platform.api.model.req.admin.CurrentTaskInstanceReq;
import com.xiaomi.cnzone.brain.platform.api.model.req.admin.FinishUserCurrentEventReq;
import com.xiaomi.cnzone.brain.platform.api.model.req.admin.ProretailOuterEventReq;
import com.xiaomi.cnzone.brain.platform.api.model.req.admin.ResetUserCurrentTaskEventStatusReq;
import com.xiaomi.cnzone.brain.platform.api.model.req.app.TaskInstanceIdAndEventDefinitionIdReq;
import com.xiaomi.cnzone.brain.platform.api.model.req.app.TaskInstanceIdReq;
import com.xiaomi.cnzone.brain.platform.api.model.req.app.TaskInstanceReq;
import com.xiaomi.cnzone.brain.platform.api.model.resp.app.EventInstanceListResp;
import com.xiaomi.cnzone.brain.platform.api.model.resp.app.EventInstanceResp;
import com.xiaomi.cnzone.brain.platform.api.model.resp.app.TaskInstanceByMidResp;
import com.xiaomi.cnzone.brain.platform.api.model.resp.app.TaskInstanceResp;
import com.xiaomi.cnzone.brain.platform.api.provider.BrainPlatformAppProvider;
import com.xiaomi.cnzone.brain.platform.api.provider.BrainPlatformOuterProvider;
import com.xiaomi.cnzone.proretail.newcommon.util.RetailJsonUtil;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.jetbrains.annotations.Nullable;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.mi.info.intl.retail.cooperation.task.app.mq.RmsApiRequestConsumer.getResetUserCurrentTaskEventStatusReq;

/**
 * 任务动作服务类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class TaskActionServiceImpl implements TaskActionService {

    @DubboReference(group = "${center.dubbo.group:}", check = false, timeout = 20000, retries = 0)
    private BrainPlatformOuterProvider brainPlatformOuterProvider;

    @DubboReference(group = "${center.dubbo.group:}", check = false, timeout = 20000, retries = 0)
    private BrainPlatformAppProvider brainPlatformAppProvider;


    @Autowired
    private IntlBigPromotionConfReadMapper intlBigPromotionConfReadMapper;

    @Autowired
    private InspectionConfig inspectionConfig;

    // 定义督导职位数组
    private final List<Long> supervisorTitleCodes = Arrays.asList(500900002L, 100000051L, 100000024L);
    // 定义促销员职位数组
    private final List<Long> promoterTitleCodes = Arrays.asList(500900001L, 100000027L, 100000026L);


    /**
     * 处理任务动作
     */
    @Override
    public void handleTaskAction(TaskActionRequest request) {
        log.info("handleTaskAction request: {}", request);

        Date countryLocalDate = IntlTimeUtil.getCountryLocalDateByCountryCode(request.getCountryCode());
        TaskInstanceResp.TaskInstance taskInstance = queryUserCurrentTaskInstance(request);

        if (taskInstance == null) {
            log.warn("task not exist, ignore this action request: {}", request);
            return;
        }

        IntlBigPromotionConf conf = intlBigPromotionConfReadMapper.queryCurrentBigPromotionTask(
                request.getCountryCode(), countryLocalDate);

        if (conf == null) {
            log.info("do no big promotion task, action name: {}, action code: {} ",
                    request.getTaskActionEnum().getActionName(), request.getTaskActionEnum().getActionCode());
            handleTaskActionNoPromotion(request, taskInstance);
        } else {
            log.info("do big promotion task, action name: {}, action code: {} ",
                    request.getTaskActionEnum().getActionName(), request.getTaskActionEnum().getActionCode());
            handleTaskActionInPromotion(request, taskInstance, conf);
        }
    }

    /**
     * 任务完成统一接口
     *
     * @param request 任务动作请求
     */
    @Override
    public void taskCompleted(TaskActionRequest request) {
        log.info("taskCompleted request: {}", request);
        // 根据职位判断执行逻辑
        if (supervisorTitleCodes.contains(request.getUserTitleCode())) {
            // 执行督导逻辑
            handleTaskAction(request);
        } else if (promoterTitleCodes.contains(request.getUserTitleCode())) {
            // 执行促销员逻辑
            handlePromoterTaskCompleted(request);
        } else {
            log.warn("unknown user title code: {}, ignore this action request: {}", request.getUserTitleCode(), request);
        }

    }

    /**
     * 促销员任务完成
     *
     * @param request 任务动作请求
     */
    private void handlePromoterTaskCompleted(TaskActionRequest request) {
        log.info("handlePromoterTaskCompleted request: {}", request);
        // 查询当前用户任务列表
        List<TaskInstanceByMidResp.TaskInstanceByMid> taskInstanceList = queryUserTaskInstanceList(request);
        if (taskInstanceList == null) {
            log.warn("promoter task not exist, ignore this action request: {}", request);
            return;
        }
        // 根据业务类型和阵地Id筛选第一条任务
        TaskInstanceByMidResp.TaskInstanceByMid taskInstance = taskInstanceList.stream()
                .filter(t -> t.getBusinessTypeId() == request.getTaskActionEnum().getActionCode().intValue()
                        && t.getOrgId().equals(request.getPositionCode()))
                .findFirst().orElse(null);
        if (taskInstance == null) {
            log.warn("promoter task not found, ignore this action request: {}", request);
            return;
        }
        // 完成促销员任务
        finishPromoterTask(request, taskInstance);
    }

    /**
     * 查询当前用户任务列表(促销员）
     */
    private List<TaskInstanceByMidResp.TaskInstanceByMid> queryUserTaskInstanceList(TaskActionRequest request) {
        log.info("queryUserTaskInstanceList request: {}", request);
        TaskInstanceReq taskInstanceReq = new TaskInstanceReq();
        taskInstanceReq.setMid(request.getMid());
        taskInstanceReq.setOrgId(request.getPositionCode());
        taskInstanceReq.setBusinessTypeId(request.getTaskActionEnum().getActionCode().intValue());
        taskInstanceReq.setAreaId(request.getCountryCode());

        TaskInstanceByMidResp result = brainPlatformAppProvider.queryTaskInstanceByMid(taskInstanceReq);
        log.info("queryUserTaskInstanceList result: {}", result);

        return result.getList();
    }

    /**
     * 完成促销员任务
     */
    private void finishPromoterTask(TaskActionRequest request, TaskInstanceByMidResp.TaskInstanceByMid taskInstance) {
        log.info("finishPromoterTask request: {}", request);
        // 完成促销员任务
        ProretailOuterEventReq proretailOuterEventReq = new ProretailOuterEventReq();
        proretailOuterEventReq.setMid(request.getMid());
        proretailOuterEventReq.setTaskBatchId(taskInstance.getTaskBatchId());
        proretailOuterEventReq.setOrgId(taskInstance.getOrgId());
        proretailOuterEventReq.setOperatorMid(request.getMid());
        proretailOuterEventReq.setRetailTenantId("2");
        proretailOuterEventReq.setRetailAppSign("CHANNEL_RETAIL");

        Result<String> result = brainPlatformOuterProvider.outerTaskFinish(proretailOuterEventReq);
        log.info("finishPromoterTask result: {}", result);
        if (result.getCode() != 0) {
            log.error("promoter outerTaskFinish error:{}", result.getMessage());
        }
    }

    /**
     * 无新品任务时的处理
     */
    private void handleTaskActionNoPromotion(TaskActionRequest request, TaskInstanceResp.TaskInstance instance) {
        if (instance.getStatus() == 1) {
            log.info("task has finished, ignore this action request: {}", request);
            return;
        }

        if (request.getTaskActionEnum() == TaskActionEnum.CHECK_OUT_SR) {
            handleSignOut(request, instance);
        } else if (request.getTaskActionEnum() == TaskActionEnum.CHECK_IN_SR) {
            handleSignIn(request, instance);
        } else {
            finishUserCurrentTask(request, null);
        }
    }

    /**
     * 有新品任务时的处理
     */
    private void handleTaskActionInPromotion(TaskActionRequest request, TaskInstanceResp.TaskInstance instance,
                                             IntlBigPromotionConf conf) {
        if (instance.getStatus() == 1 && !inCurrentCountryDate(request.getCountryCode(), instance.getFinishTimeStamp())) {
            log.warn("task has finished(not current day finished), ignore this action request: {}", request);
            return;
        }

        if (instance.getStatus() != 1 && arrivedPromotionCompletedCondition(request.getTaskActionEnum(), instance)) {
            Map<String, String> customRecordMap = new HashMap<>();
            customRecordMap.put("bigPromotionStartTime", IntlTimeUtil.toFormatString(conf.getStartTime()));
            customRecordMap.put("bigPromotionEndTime", IntlTimeUtil.toFormatString(conf.getEndTime()));
            // completeType 2: 完成动作，主动完成子任务
            finishUserCurrentTask(request, customRecordMap, 2);
        } else {
            // completeType 1: 完成动作，子任务非主动完成(默认被动完成逻辑)
            finishUserCurrentTask(request, null, 1);
        }
    }

    /**
     * 检查是否在当前国家日期范围内
     */
    private boolean inCurrentCountryDate(String countryCode, Long finishTime) {
        List<Long> startAndEndOfDayMillis = IntlTimeUtil.getStartAndEndOfDayMillis(countryCode);
        if (startAndEndOfDayMillis == null) {
            log.error("get start and end of day millis error, countryCode:{}", countryCode);
            return false;
        }
        Long startOfDayMillis = startAndEndOfDayMillis.get(0);
        Long endOfDayMillis = startAndEndOfDayMillis.get(1);
        return finishTime >= startOfDayMillis && finishTime <= endOfDayMillis;
    }

    /**
     * 检查是否满足促销完成条件
     */
    private boolean arrivedPromotionCompletedCondition(TaskActionEnum currentActionEnum,
                                                       TaskInstanceResp.TaskInstance instance) {
        List<Long> mustDoEvent = inspectionConfig.getMustDoEventDefinitionIds();
        List<Long> anyOneEvent = inspectionConfig.getAnyOneEventDefinitionIds();
        List<EventInstanceResp> eventInstances = queryEventInstanceList(instance);
        Map<Long, EventInstanceResp> eventInstanceMap = eventInstances.stream()
                .collect(Collectors.toMap(EventInstanceResp::getEventDefinitionId, Function.identity()));

        boolean mustDoEventAllCompleted = false;
        Long currentEventId = currentActionEnum.getActionCode();
        boolean anyOneEventCompleted = anyOneEvent.contains(currentEventId);

        if (anyOneEventCompleted) {
            int mustDoEventCount = mustDoEvent.size();
            for (Long mustDoEventId : mustDoEvent) {
                EventInstanceResp eventInstanceResp = eventInstanceMap.get(mustDoEventId);
                // 不存在视为完成，则减1;存在且完成则减1
                if (eventInstanceResp == null || eventInstanceResp.getStatus() == 1) {
                    mustDoEventCount--;
                }
            }
            mustDoEventAllCompleted = mustDoEventCount == 0;
        } else {
            int mustDoEventCount = mustDoEvent.size();
            for (Long mustDoEventId : mustDoEvent) {
                EventInstanceResp eventInstanceResp = eventInstanceMap.get(mustDoEventId);
                // 为当前事件则减1;不存在视为完成，则减1;存在且完成则减1
                if (currentEventId.equals(mustDoEventId) || eventInstanceResp == null
                        || eventInstanceResp.getStatus() == 1) {
                    mustDoEventCount--;
                }
            }
            mustDoEventAllCompleted = mustDoEventCount == 0;

            int anyOneEventCount = anyOneEvent.size();
            for (Long anyOneEventId : anyOneEvent) {
                EventInstanceResp eventInstanceResp = eventInstanceMap.get(anyOneEventId);
                //存在任意 "任意事件" 完成则 anyOneEventCompleted 为 true
                if (eventInstanceResp != null) {
                    anyOneEventCompleted = eventInstanceResp.getStatus() == 1;
                    if (anyOneEventCompleted) {
                        break;
                    }
                } else {
                    // 不存在任务时，anyOneEventCount 减1
                    anyOneEventCount--;
                }
            }
            if (!anyOneEventCompleted) {
                // 所有任意事件不存在场景： 任意事件不存在，则 anyOneEventCompleted 为 true
                anyOneEventCompleted = anyOneEventCount == 0;
            }
        }
        return anyOneEventCompleted && mustDoEventAllCompleted;
    }

    /**
     * 处理签出任务
     */
    private void handleSignOut(TaskActionRequest request, TaskInstanceResp.TaskInstance instance) {
        EventInstanceResp signInEventInstance = querySignInEventInstance(instance.getId());
        if (signInEventInstance == null) {
            log.error("signInEventInstance is null, check this error, ignore this action request: {}", request);
            return;
        }

        long signInTime = signInEventInstance.getFinishTimeStamp();
        if (signInTime == 0) {
            log.error("signInEventInstance signInTime is 0, check this error, ignore this action request: {}", request);
            return;
        }

        // inspectionTime != 0 时 才管控 动作必须在 check in check out 之间 完成, 且时长要大于inspectionTime，否则重置动作状态
        // inspectionTime == 0 时 或 null 才不管控动作，不会重置动作状态
        Long inspectionTime = getInspectionTime(instance);
        Long costedInspectionTime = (System.currentTimeMillis() - signInTime) / (60 * 1000);

        if ((inspectionTime != null && inspectionTime != 0) && (costedInspectionTime < inspectionTime)) {
            // 在店时长不够
            ResetUserCurrentTaskEventStatusReq resetUserCurrentTaskEventStatusReq = convertToResetUserCurrentTaskEventStatusReq(request);
            // 1任务未完成 2.在店时长 3.任务过期重置
            resetUserCurrentTaskEventStatusReq.setResetUserTaskEventType(2);
            Map<String, Object> resetUserReason = new HashMap<>();
            resetUserReason.put("costedInspectionTime", costedInspectionTime);
            resetUserReason.put("inspectionTime", inspectionTime);
            resetUserCurrentTaskEventStatusReq.setResetUserReason(RetailJsonUtil.toJson(resetUserReason));
            resetUserCurrentTaskEventStatus(resetUserCurrentTaskEventStatusReq);
            return;
        }

        List<EventInstanceResp> unCompletedList = actionUnCompletedList(instance);
        if ((inspectionTime != null && inspectionTime != 0) && CollectionUtils.isNotEmpty(unCompletedList)) {
            // 存在未完成的任务
            ResetUserCurrentTaskEventStatusReq resetUserCurrentTaskEventStatusReq = convertToResetUserCurrentTaskEventStatusReq(request);
            // 1任务未完成 2.在店时长 3.任务过期重置
            resetUserCurrentTaskEventStatusReq.setResetUserTaskEventType(1);
            List<String> unCompletedTitleList = unCompletedList.stream().map(EventInstanceResp::getTitle).collect(Collectors.toList());
            resetUserCurrentTaskEventStatusReq.setResetUserReason(RetailJsonUtil.toJson(unCompletedTitleList));
            resetUserCurrentTaskEventStatus(resetUserCurrentTaskEventStatusReq);
        } else {
            Map<String, String> customRecordMap = new HashMap<>();
            customRecordMap.put("costedInspectionTime", String.valueOf(costedInspectionTime));
            finishUserCurrentTask(request, customRecordMap);
        }
    }

    /**
     * 处理签入任务
     */
    private void handleSignIn(TaskActionRequest request, TaskInstanceResp.TaskInstance instance) {
        Long inspectionTime = getInspectionTime(instance);
        // inspectionTime != 0 时 才管控 动作必须在 check in check out 之间 完成, 且时长要大于inspectionTime，否则重置动作状态
        // inspectionTime == 0 时 或 null 才不管控动作，不会重置动作状态
        if (inspectionTime != null && inspectionTime != 0) {
            ResetUserCurrentTaskEventStatusReq resetUserCurrentTaskEventStatusReq = convertToResetUserCurrentTaskEventStatusReq(request);
            resetUserCurrentTaskEventStatusReq.setResetUserTaskEventType(4);
            resetUserCurrentTaskEventStatusReq.setResetUserReason("sign in reset all current event");
            resetUserCurrentTaskEventStatus(resetUserCurrentTaskEventStatusReq);
        }
        finishUserCurrentTask(request, null);
    }

    /**
     * 查找未完成的动作列表
     */
    private List<EventInstanceResp> actionUnCompletedList(TaskInstanceResp.TaskInstance instance) {
        List<EventInstanceResp> eventInstances = queryEventInstanceList(instance);
        Long checkOutEventDefinitionId = TaskActionEnum.CHECK_OUT_SR.getActionCode();
        List<EventInstanceResp> notActionCompletedList = new ArrayList<>();

        for (EventInstanceResp eventInstance : eventInstances) {
            Long eventDefinitionId = eventInstance.getEventDefinitionId();
            if (Objects.equals(eventDefinitionId, checkOutEventDefinitionId)) {
                continue;
            }
            if (eventInstance.getStatus() != 1) {
                notActionCompletedList.add(eventInstance);
            }
        }
        return notActionCompletedList;
    }

    /**
     * 查询用户当前任务实例
     */
    private TaskInstanceResp.TaskInstance queryUserCurrentTaskInstance(TaskActionRequest request) {
        return getTaskInstance(request.getMid(), request.getPositionCode(), brainPlatformOuterProvider, log);
    }

    @Nullable
    public static TaskInstanceResp.TaskInstance getTaskInstance(Long mid, String positionCode,
                                                                BrainPlatformOuterProvider brainPlatformOuterProvider, Logger log) {
        CurrentTaskInstanceReq currentTaskInstanceReq = new CurrentTaskInstanceReq();
        currentTaskInstanceReq.setMid(mid);
        currentTaskInstanceReq.setOrgId(positionCode);
        currentTaskInstanceReq.setBusinessTypeId(TaskActionEnum.SR_INSPECTION.getActionCode());
        currentTaskInstanceReq.setRetailTenantId("2");
        currentTaskInstanceReq.setRetailAppSign("CHANNEL_RETAIL");

        Result<TaskInstanceResp.TaskInstance> result = brainPlatformOuterProvider.queryUserCurrentTaskInstance(
                currentTaskInstanceReq);
        if (result.getCode() != GeneralCodes.OK.getCode()) {
            log.error("queryUserCurrentTaskInstance error, result: {}", result);
            return null;
        }
        if (result.getData().getId() == null) {
            log.warn("task instance not exist, req: {}, result: {}", currentTaskInstanceReq, result);
            return null;
        }
        return result.getData();
    }

    /**
     * 重置用户当前任务事件状态
     */
    private void resetUserCurrentTaskEventStatus(ResetUserCurrentTaskEventStatusReq resetUserCurrentTaskEventStatusReq) {
        brainPlatformOuterProvider.resetUserCurrentTaskEventStatus(resetUserCurrentTaskEventStatusReq);
    }

    /**
     * 转换为重置用户当前任务事件状态请求
     */
    private ResetUserCurrentTaskEventStatusReq convertToResetUserCurrentTaskEventStatusReq(TaskActionRequest request) {
        return getResetUserCurrentTaskEventStatusReq(request.getMid(), request.getPositionCode());
    }

    /**
     * 查询签入事件实例
     */
    private EventInstanceResp querySignInEventInstance(Long taskInstanceId) {
        return getEventInstanceResp(taskInstanceId, brainPlatformOuterProvider, log);
    }

    @Nullable
    public static EventInstanceResp getEventInstanceResp(Long taskInstanceId, BrainPlatformOuterProvider brainPlatformOuterProvider, Logger log) {
        TaskInstanceIdAndEventDefinitionIdReq taskInstanceIdAndEventDefinitionIdReq = new TaskInstanceIdAndEventDefinitionIdReq();
        taskInstanceIdAndEventDefinitionIdReq.setTaskInstanceId(taskInstanceId);
        taskInstanceIdAndEventDefinitionIdReq.setEventDefinitionId(TaskActionEnum.CHECK_IN_SR.getActionCode());

        Result<EventInstanceResp> result = brainPlatformOuterProvider.queryEventInstanceByTaskInstanceIdAndDefinitionId(
                taskInstanceIdAndEventDefinitionIdReq);
        if (result.getCode() != GeneralCodes.OK.getCode()) {
            log.error("querySignInEventInstance error, result: {}", result);
            return null;
        }
        if (result.getData().getId() == null) {
            log.warn("event instance not exist, req: {}, result: {}", taskInstanceIdAndEventDefinitionIdReq, result);
            return null;
        }
        return result.getData();
    }

    /**
     * 完成用户当前任务
     */
    private boolean finishUserCurrentTask(TaskActionRequest request, Map<String, String> customRecordMap) {
        return finishUserCurrentTask(request, customRecordMap, 1);
    }

    /**
     * 完成用户当前任务
     */
    private boolean finishUserCurrentTask(TaskActionRequest request, Map<String, String> customRecordMap,
                                          Integer completeType) {
        FinishUserCurrentEventReq finishUserCurrentEventReq = new FinishUserCurrentEventReq();
        finishUserCurrentEventReq.setMid(request.getMid());
        finishUserCurrentEventReq.setOrgId(request.getPositionCode());
        finishUserCurrentEventReq.setBusinessTypeId(request.getTaskActionEnum().getActionCode());
        finishUserCurrentEventReq.setRetailTenantId("2");
        finishUserCurrentEventReq.setRetailAppSign("CHANNEL_RETAIL");
        finishUserCurrentEventReq.setOperateTimeStamp(System.currentTimeMillis());
        finishUserCurrentEventReq.setCompletedType(completeType);
        if (customRecordMap != null) {
            finishUserCurrentEventReq.setCustomRecordStr(JsonUtil.bean2json(customRecordMap));
        }

        Result<String> result = brainPlatformOuterProvider.finishUserCurrentEvent(finishUserCurrentEventReq);
        if (result.getCode() != 0) {
            log.error("finishUserCurrentTask error:{}", result.getMessage());
            return false;
        }
        return true;
    }

    /**
     * 查询事件实例列表
     */
    private List<EventInstanceResp> queryEventInstanceList(TaskInstanceResp.TaskInstance instance) {
        TaskInstanceIdReq taskInstanceIdReq = new TaskInstanceIdReq();
        taskInstanceIdReq.setTaskInstanceId(instance.getId());

        Result<EventInstanceListResp> result = brainPlatformOuterProvider.queryEventInstanceByTaskInstanceId(
                taskInstanceIdReq);
        if (result.getCode() != GeneralCodes.OK.getCode()) {
            log.error("queryEventInstanceList error, result: {}", result);
            return new ArrayList<>();
        }
        return result.getData().getEventInstances();
    }

    /**
     * 获取检查时间
     */
    private Long getInspectionTime(TaskInstanceResp.TaskInstance instance) {
        try {
            String customParamsStr = instance.getCustomParamsStr();
            Map<String, Object> customParamsMap = JsonUtil.json2map(customParamsStr);
            Integer inspectionTime = (Integer) customParamsMap.get("inspectionTime");
            if (inspectionTime == null) {
                return null;
            }
            return Long.valueOf(inspectionTime);
        } catch (Exception e) {
            log.error("getInspectionTime error", e);
            return null;
        }
    }
}
