<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.info.intl.retail.user.infra.mapper.read.UserPositionStoreReadMapper">
    <resultMap id="userPositionStoreMap" type="com.mi.info.intl.retail.user.infra.entity.UserPositionStore">
        <result column="rms_userid" property="userId"/>
        <result column="domain_name" property="userName"/>
        <result column="mi_id" property="mid"/>
        <result column="area_code" property="areaId"/>
        <result column="job_id" property="userTitle"/>
        <result column="job_name" property="userTitleName"/>
        <result column="timezone_code" property="timezoneCode"/>
        <result column="language_code" property="languageCode"/>
        <result column="country_code" property="countryCode"/>
        <result column="position_code" property="positionCode"/>
        <result column="position_name" property="positionName"/>
        <result column="position_type" property="positionType"/>
        <result column="position_type_name" property="positionTypeName"/>
        <result column="is_promotion_store" property="isPromotion"/>
        <result column="store_id" property="storeId"/>
        <result column="store_name" property="storeName"/>
        <result column="grade" property="storeGrade"/>
        <result column="grade_name" property="storeGradeName"/>
        <result column="store_code" property="storeCode"/>
    </resultMap>

    <select id="getUserPositionStore" resultMap="userPositionStoreMap">
        SELECT distinct
        u.rms_userid, u.domain_name, u.job_id, u.job_name, u.mi_id, u.timezone_code, u.language_code,
        ct.country_code, ct.area_code,
        p.code as position_code, p.name as position_name, p.type as position_type, p.type_name as position_type_name,
        p.is_promotion_store,
        s.store_id, s.name as store_name, s.grade, s.grade_name, s.code as store_code
        FROM intl_rms_user u
        JOIN intl_rms_personnel_position up ON u.rms_userid = up.user_id
        JOIN intl_rms_position p ON up.position_id = p.position_id
        JOIN intl_rms_store s ON p.store_id = s.store_id
        JOIN intl_rms_country_timezone ct ON u.country_id = ct.country_id
        <where>
            <!-- 用户必须是启用状态 -->
            AND u.is_disabled = 0

            <!-- 关联的岗位和店铺必须是有效状态 -->
            AND up.state_code = 0
            AND p.state_code = 0
            AND s.state_code = 0

            <!-- 限定岗位类型范围 -->
            AND p.type IN (100000002, 100000006, 100000003, 100000004, 100000005)

            <!-- 用户必须关联了有效的MI账号 -->
            AND u.mi_id != 0
            AND u.mi_id is not null

            <!-- 按国家筛选 -->
            <if test="country != null">
                AND ct.country_code = #{country}
            </if>

            <!-- 按职位ID列表筛选 -->
            <if test="titles != null and titles.size() > 0">
                AND u.job_id IN
                <foreach item="title" collection="titles" open="(" separator="," close=")">
                    #{title}
                </foreach>
            </if>

            <!-- 按店铺等级名称列表筛选 -->
            <if test="storeGrades != null and storeGrades.size() > 0">
                AND s.grade_name IN
                <foreach item="grade" collection="storeGrades" open="(" separator="," close=")">
                    #{grade}
                </foreach>
            </if>

            <!-- 按mid列表筛选 -->
            <if test="midList != null and midList.size() > 0">
                AND u.mi_id IN
                <foreach item="mid" collection="midList" open="(" separator="," close=")">
                    #{mid}
                </foreach>
            </if>

        </where>
    </select>

    <!-- 促销员查询结果映射 -->
    <resultMap id="promoterUserPositionMap" type="com.mi.info.intl.retail.user.infra.entity.UserPositionStore">
        <result column="user_id" property="userId"/>
        <result column="mid" property="mid"/>
        <result column="user_name" property="userName"/>
        <result column="area_code" property="areaId"/>
        <result column="language_code" property="languageCode"/>
        <result column="title_code" property="userTitle"/>
        <result column="title_name" property="userTitleName"/>
        <result column="store_id" property="storeId"/>
        <result column="position_code" property="positionCode"/>
        <result column="position_type" property="positionType"/>
        <result column="position_name" property="positionName"/>
        <result column="channel_type_id" property="channelTypeId"/>
        <result column="channel_type_name" property="channelTypeName"/>
    </resultMap>

    <!-- 查询促销员用户阵地信息 -->
    <select id="getPromoterUserPositions" resultMap="promoterUserPositionMap">
        SELECT DISTINCT
            user.rms_userid AS user_id,
            user.mi_id AS mid,
            user.english_name AS user_name,
            user.country_shortcode AS area_code,
            user.language_code AS language_code,
            user.job_id AS title_code,
            user.job_name AS title_name,
            p.position_id AS store_id,
            p.code AS position_code,
            p.type AS position_type,
            p.name AS position_name,
            p.channel_type AS channel_type_id,
            p.channel_type_name AS channel_type_name
        FROM intl_rms_user user
        JOIN intl_rms_personnel_position pp ON user.rms_userid = pp.user_id
            AND pp.state_code = 0
        JOIN intl_rms_position p ON pp.position_id = p.position_id
        <where>
            <!-- 基础条件 -->
            user.is_disabled = 0
            AND user.mi_id IS NOT NULL
            AND user.mi_id != 0
            AND p.type IN (100000002,100000005,100000004)
            AND p.channel_type IN (100000000,100000002)

            <!-- 国家筛选（必填） -->
            AND user.country_shortcode = #{region}

            <!-- 按职位代码列表筛选 -->
            <if test="titleCodeList != null and titleCodeList.size() > 0">
                AND user.job_id IN
                <foreach item="titleCode" collection="titleCodeList" open="(" separator="," close=")">
                    #{titleCode}
                </foreach>
            </if>

            <!-- 按零售商代码列表筛选 -->
            <if test="retailerCodeList != null and retailerCodeList.size() > 0">
                AND p.retailer_name IN
                <foreach item="retailerCode" collection="retailerCodeList" open="(" separator="," close=")">
                    #{retailerCode}
                </foreach>
            </if>

            <!-- 按阵地类型列表筛选 -->
            <if test="positionTypeList != null and positionTypeList.size() > 0">
                AND p.type IN
                <foreach item="positionType" collection="positionTypeList" open="(" separator="," close=")">
                    #{positionType}
                </foreach>
            </if>

            <!-- 按阵地代码列表筛选 -->
            <if test="positionCodeList != null and positionCodeList.size() > 0">
                AND p.code IN
                <foreach item="positionCode" collection="positionCodeList" open="(" separator="," close=")">
                    #{positionCode}
                </foreach>
            </if>

            <!-- 按渠道类型列表筛选 -->
            <if test="channelTypeList != null and channelTypeList.size() > 0">
                AND p.channel_type IN
                <foreach item="channelType" collection="channelTypeList" open="(" separator="," close=")">
                    #{channelType}
                </foreach>
            </if>
        </where>
        ORDER BY
            user.mi_id ASC,
            p.code ASC
        <if test="pageNum != null and pageSize != null and pageSize > 0">
            LIMIT #{pageSize} OFFSET #{pageSize} * (#{pageNum} - 1)
        </if>
    </select>

    <!-- 统计促销员用户阵地信息总数 -->
    <select id="countPromoterUserPositions" resultType="java.lang.Integer">
        SELECT COUNT(DISTINCT CONCAT(user.mi_id, '_', p.position_id))
        FROM intl_rms_user user
        JOIN intl_rms_personnel_position pp ON user.rms_userid = pp.user_id
            AND pp.state_code = 0
        JOIN intl_rms_position p ON pp.position_id = p.position_id
        <where>
            <!-- 基础条件 -->
            user.is_disabled = 0
            AND user.mi_id IS NOT NULL
            AND user.mi_id != 0
            AND p.type IN (100000002,100000005,100000004)
            AND p.channel_type IN (100000000,100000002)

            <!-- 国家筛选（必填） -->
            AND user.country_shortcode = #{region}

            <!-- 按职位代码列表筛选 -->
            <if test="titleCodeList != null and titleCodeList.size() > 0">
                AND user.job_id IN
                <foreach item="titleCode" collection="titleCodeList" open="(" separator="," close=")">
                    #{titleCode}
                </foreach>
            </if>

            <!-- 按零售商代码列表筛选 -->
            <if test="retailerCodeList != null and retailerCodeList.size() > 0">
                AND p.retailer_name IN
                <foreach item="retailerCode" collection="retailerCodeList" open="(" separator="," close=")">
                    #{retailerCode}
                </foreach>
            </if>

            <!-- 按阵地类型列表筛选 -->
            <if test="positionTypeList != null and positionTypeList.size() > 0">
                AND p.type IN
                <foreach item="positionType" collection="positionTypeList" open="(" separator="," close=")">
                    #{positionType}
                </foreach>
            </if>

            <!-- 按阵地代码列表筛选 -->
            <if test="positionCodeList != null and positionCodeList.size() > 0">
                AND p.code IN
                <foreach item="positionCode" collection="positionCodeList" open="(" separator="," close=")">
                    #{positionCode}
                </foreach>
            </if>

            <!-- 按渠道类型列表筛选 -->
            <if test="channelTypeList != null and channelTypeList.size() > 0">
                AND p.channel_type IN
                <foreach item="channelType" collection="channelTypeList" open="(" separator="," close=")">
                    #{channelType}
                </foreach>
            </if>
        </where>
    </select>

    <!-- 根据midList查询促销员用户阵地信息 -->
    <select id="getPromoterUserPositionsByMidList" resultMap="promoterUserPositionMap">
        SELECT DISTINCT
            user.rms_userid AS user_id,
            user.mi_id AS mid,
            user.english_name AS user_name,
            user.country_shortcode AS area_code,
            user.language_code AS language_code,
            user.job_id AS title_code,
            user.job_name AS title_name,
            p.position_id AS store_id,
            p.code AS position_code,
            p.type AS position_type,
            p.name AS position_name,
            p.channel_type AS channel_type_id,
            p.channel_type_name AS channel_type_name
        FROM intl_rms_user user
        JOIN intl_rms_personnel_position pp ON user.rms_userid = pp.user_id
            AND pp.state_code = 0
        JOIN intl_rms_position p ON pp.position_id = p.position_id
        <where>
            <!-- 基础条件 -->
            user.is_disabled = 0
            AND user.mi_id IS NOT NULL
            AND user.mi_id != 0
            AND p.type IN (100000002,100000005,100000004)
            AND p.channel_type IN (100000000,100000002)

            <!-- 国家筛选（必填） -->
            AND user.country_shortcode = #{region}

            <!-- 按MI ID列表筛选 -->
            <if test="midList != null and midList.size() > 0">
                AND user.mi_id IN
                <foreach item="mid" collection="midList" open="(" separator="," close=")">
                    #{mid}
                </foreach>
            </if>
        </where>
        ORDER BY
            user.mi_id ASC,
            p.code ASC
        <if test="pageNum != null and pageSize != null and pageSize > 0">
            LIMIT #{pageSize} OFFSET #{pageSize} * (#{pageNum} - 1)
        </if>
    </select>

    <!-- 根据midList统计促销员用户阵地信息总数 -->
    <select id="countPromoterUserPositionsByMidList" resultType="java.lang.Integer">
        SELECT COUNT(DISTINCT CONCAT(user.mi_id, '_', p.position_id))
        FROM intl_rms_user user
        JOIN intl_rms_personnel_position pp ON user.rms_userid = pp.user_id
            AND pp.state_code = 0
        JOIN intl_rms_position p ON pp.position_id = p.position_id
        <where>
            <!-- 基础条件 -->
            user.is_disabled = 0
            AND user.mi_id IS NOT NULL
            AND user.mi_id != 0
            AND p.type IN (100000002,100000005,100000004)
            AND p.channel_type IN (100000000,100000002)

            <!-- 国家筛选（必填） -->
            AND user.country_shortcode = #{region}

            <!-- 按MI ID列表筛选 -->
            <if test="midList != null and midList.size() > 0">
                AND user.mi_id IN
                <foreach item="mid" collection="midList" open="(" separator="," close=")">
                    #{mid}
                </foreach>
            </if>
        </where>
    </select>
</mapper>
