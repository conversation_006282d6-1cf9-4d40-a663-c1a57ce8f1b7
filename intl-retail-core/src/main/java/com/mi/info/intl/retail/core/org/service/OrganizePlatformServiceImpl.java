package com.mi.info.intl.retail.core.org.service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import cn.hutool.json.JSONUtil;
import com.xiaomi.nr.eiam.api.dto.userinfo.GetUserInfoReq;
import com.xiaomi.nr.eiam.api.dto.userinfo.GetUserInfoResp;
import com.xiaomi.nr.eiam.api.dto.userinfo.ListUserInfoReq;
import com.xiaomi.nr.eiam.common.enums.SceneEnum;
import com.xiaomi.youpin.infra.rpc.Result;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.mi.info.intl.retail.core.org.configuration.JobInfo;
import com.mi.info.intl.retail.exception.BizException;
import com.mi.info.intl.retail.exception.ErrorCodes;
import com.mi.info.intl.retail.utils.RpcResultUtils;
import com.xiaomi.nr.eiam.admin.dto.provider.user.SearchUserSensitiveInfoRequest;
import com.xiaomi.nr.eiam.admin.provider.UserAdminProvider;
import com.xiaomi.nr.eiam.admin.vo.provider.user.UserSensitiveInfoResp;
import com.xiaomi.nr.eiam.api.dto.userinfo.GetParentOrganPositionUserReq;
import com.xiaomi.nr.eiam.api.dto.userinfo.UserPosition;
import com.xiaomi.nr.eiam.api.provider.UserProvider;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2025/08/05
 */
@Slf4j
@Service
public class OrganizePlatformServiceImpl implements OrganizePlatformService {

    @DubboReference(group = "${organization.dubbo-group}", interfaceClass = UserProvider.class, check = false,
        timeout = 5000)
    private UserProvider userProvider;

    @Value("${organization.scene:new_retail}")
    private String defaultScene;

    @DubboReference(group = "${organization.dubbo-group}", interfaceClass = UserAdminProvider.class, check = false,
        timeout = 5000)
    private UserAdminProvider userAdminProvider;

    @Override
    public List<UserPosition> getOrganizePlatform(JobInfo jobInfo) {
        List<UserPosition> userPositions = null;
        try {
            GetParentOrganPositionUserReq userInfoRequest = buildGetParentOrganPositionUserReq(jobInfo);
            userPositions = RpcResultUtils.handleOrgRpc(userProvider::getParentOrganPositionUser, userInfoRequest,
                "UserProvider.getParentOrganPositionUser", true).orElse(Collections.emptyList());
            if (CollectionUtils.isEmpty(userPositions)) {
                throw new BizException(ErrorCodes.CAN_NOT_OBTAIN_USER_INFO, jobInfo.getPositionName());
            }
            // 查询用户隐私信息
            SearchUserSensitiveInfoRequest request = new SearchUserSensitiveInfoRequest();
            request.setMiIdList(userPositions.stream().map(UserPosition::getMiId).collect(Collectors.toList()));
            List<UserSensitiveInfoResp> userInfoRespList =
                RpcResultUtils.handleOrgRpc(userAdminProvider::searchUserSensitiveInfo, request,
                    "UserAdminProvider.searchUserSensitiveInfo", true).orElse(Collections.emptyList());
            if (CollectionUtils.isEmpty(userInfoRespList)) {
                throw new BizException(ErrorCodes.CAN_NOT_OBTAIN_USER_INFO, jobInfo.getPositionName());
            }
            Map<Long, UserSensitiveInfoResp> map = userInfoRespList.stream()
                .collect(Collectors.toMap(UserSensitiveInfoResp::getMiId, item -> item, (o1, o2) -> o1));
            userPositions.forEach(userPosition -> {
                UserSensitiveInfoResp userInfoResp = map.get(userPosition.getMiId());
                if (userInfoResp != null) {
                    userPosition.setName(userInfoResp.getName());
                }
            });
        } catch (Exception e) {
            throw new BizException(ErrorCodes.CAN_NOT_OBTAIN_USER_INFO, jobInfo.getPositionName());
        }
        return userPositions;
    }

    private GetParentOrganPositionUserReq buildGetParentOrganPositionUserReq(JobInfo jobInfo) {
        GetParentOrganPositionUserReq userInfoRequest = new GetParentOrganPositionUserReq();
        userInfoRequest.setScene(getScene(jobInfo));
        userInfoRequest.setOrganCode(jobInfo.getOrganCode());
        userInfoRequest.setPositionId(jobInfo.getPositionId());
        userInfoRequest.setManageChannelList(jobInfo.getManageChannelList());
        return userInfoRequest;
    }

    private String getScene(JobInfo jobInfo) {
        return StringUtils.isBlank(jobInfo.getScene()) ? defaultScene : jobInfo.getScene();
    }

    /**
     * 批量获取组织平台岗位信息
     *
     * @param jobInfoList 工作信息清单
     * @return {@link Map }<{@link String }, {@link List }<{@link UserPosition }>>
     */
    @Override
    public Map<Integer, List<UserPosition>> getBatchOrganizePlatform(List<JobInfo> jobInfoList) {
        if (CollectionUtils.isEmpty(jobInfoList) || jobInfoList.size() > 5) {
            throw new IllegalArgumentException("jobInfoList is empty or size > 5");
        }
        return jobInfoList.stream().collect(Collectors.toMap(JobInfo::getPositionId, jobInfo -> {
            try {
                return CompletableFuture.supplyAsync(() -> getOrganizePlatform(jobInfo)).join();
            } catch (Exception e) {
                throw new BizException(ErrorCodes.CAN_NOT_OBTAIN_USER_INFO, jobInfo.getPositionName());
            }
        }));

    }

    @Override
    public List<UserSensitiveInfoResp> getBatchUserInfo(List<Long> userIds, String scene) {
        if (CollectionUtils.isEmpty(userIds) || userIds.size() > 5) {
            throw new IllegalArgumentException("userIds is empty or size > 5");
        }
        // 查询用户隐私信息
        SearchUserSensitiveInfoRequest request = new SearchUserSensitiveInfoRequest();
        request.setMiIdList(userIds);
        return RpcResultUtils.handleOrgRpc(userAdminProvider::searchUserSensitiveInfo, request,
            "UserAdminProvider.searchUserSensitiveInfo", true).orElse(Collections.emptyList());

    }

    @Override
    public GetUserInfoResp getUserInfo(Long miId) {
        GetUserInfoReq req = new GetUserInfoReq();
        req.setScene(SceneEnum.NEW_RETAIL.getScene());
        req.setMiId(miId);
        log.info("getUserInfo req:{}", JSONUtil.toJsonStr(req));
        return RpcResultUtils.handleOrgRpc(userProvider::getUserInfo, req,
                "UserProvider.getUserInfo", true)
                .orElse(null);
    }


    @Override
    public List<GetUserInfoResp> getUserInfoList(List<Long> miIdList, String scene) {
        ListUserInfoReq req = new ListUserInfoReq();
        req.setScene(scene);
        req.setMiIdList(miIdList);
        log.info("getUserInfoList req:{}", JSONUtil.toJsonStr(req));
        return RpcResultUtils.handleOrgRpc(userProvider::listUserInfo, req,
                        "UserProvider.listUserInfo", true)
                .orElse(Collections.emptyList());
    }

}
