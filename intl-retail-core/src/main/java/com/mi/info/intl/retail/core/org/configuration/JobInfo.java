package com.mi.info.intl.retail.core.org.configuration;

import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
public class JobInfo {
    /**
     * 组织场景
     */
    private String scene;
    /**
     * 组织编码
     */
    private String organCode;
    /**
     * 岗位id
     */
    private Integer positionId;

    /**
     * 岗位名称
     */
    private String positionName;
    /**
     * 岗位类型列表
     */
    private List<Integer> manageChannelList;

}
