package com.mi.info.intl.retail.ldu.app.service.impl;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import com.mi.info.intl.retail.intlretail.service.api.ldu.LduFilterService;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.ProductLineDto;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.ProductSimpleInfoDto;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.RetailerQueryDto;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.SearchProductInfoDto;
import com.mi.info.intl.retail.intlretail.service.api.retailer.dto.CommonResponse;
import com.mi.info.intl.retail.intlretail.service.api.store.dto.ChannelInfoRequest;
import com.mi.info.intl.retail.intlretail.service.api.store.dto.ChannelInfoResponse;
import com.mi.info.intl.retail.intlretail.service.api.store.dto.RetailerInfoResponse;
import com.mi.info.intl.retail.intlretail.service.api.store.gateway.IGateWayChannelInfoService;
import com.mi.info.intl.retail.intlretail.service.api.store.gateway.dto.GateWayChannelInfoResponse;
import com.mi.info.intl.retail.ldu.infra.repository.IProductQueryService;
import com.xiaomi.mone.docs.annotations.dubbo.ApiDoc;
import com.xiaomi.mone.docs.annotations.dubbo.ApiModule;
import com.xiaomi.mone.docs.annotations.http.MiApiRequestMethod;

/**
 * 提供LDU列表过滤条件数据查询服务
 *
 * <AUTHOR>
 * @date 2025/7/8 17:58
 */
@ApiModule(value = "国际渠道零售服务", apiInterface = LduFilterService.class)
@Service
@DubboService(group = "${dubbo-group.provider.intl-retail:}", interfaceClass = LduFilterService.class)
public class LduFilterServiceImpl implements LduFilterService {

    @Autowired
    private IProductQueryService productLineQueryService;

    @Resource
    private IGateWayChannelInfoService gateWayChannelInfoService;

    @ApiDoc(name = "查询产品线列表", value = "/api/ldureport/productLines", method = MiApiRequestMethod.POST)
    @Override
    public CommonResponse<List<ProductLineDto>> queryProductLines() {
        List<ProductLineDto> productLineDtos = productLineQueryService.queryProductLines();
        return new CommonResponse<>(productLineDtos);
    }

    @ApiDoc(name = "查询零售商列表", value = "/api/ldureport/retailers", method = MiApiRequestMethod.POST)
    @Override
    public CommonResponse<List<RetailerInfoResponse>> queryRetailers(RetailerQueryDto request) {
        Assert.notNull(request, "参数不能为空");
        CommonResponse<List<RetailerInfoResponse>> emptyResp = new CommonResponse<>(Collections.emptyList());
        // 校验参数
        if (StringUtils.isBlank(request.getRegion())) {
            return emptyResp;
        }
        if (StringUtils.isBlank(request.getKeyword()) && CollectionUtils.isEmpty(request.getCodes())) {
            return emptyResp;
        }

        ChannelInfoRequest channelInfoRequest = new ChannelInfoRequest();
        channelInfoRequest.setType(20);
        channelInfoRequest.setAreaId(request.getRegion());
        channelInfoRequest.setCountryCode(request.getRegion());
        channelInfoRequest.setSearch(request.getKeyword());
        channelInfoRequest.setCodes(request.getCodes());

        GateWayChannelInfoResponse result = gateWayChannelInfoService.queryChannelInfo(channelInfoRequest);
        if (Objects.isNull(result) || CollectionUtils.isEmpty(result.getData())) {
            return emptyResp;
        }

        List<ChannelInfoResponse> data = result.getData();

        List<RetailerInfoResponse> resultList = new ArrayList<>();
        for (ChannelInfoResponse datum : data) {
            if (Objects.isNull(datum.getBasic())) {
                continue;
            }
            RetailerInfoResponse item = new RetailerInfoResponse();
            item.setCode(datum.getBasic().getCode());
            item.setName(datum.getBasic().getName());
            resultList.add(item);
        }
        return new CommonResponse<>(resultList);
    }

    @ApiDoc(name = "根据商品id或名称搜索商品信息", value = "/api/ldureport/searchProductInfoByIdOrName", method = MiApiRequestMethod.POST)
    @Override
    public CommonResponse<List<ProductSimpleInfoDto>> searchProductInfoByIdOrName(SearchProductInfoDto searchProductInfoDto) {
        Assert.notNull(searchProductInfoDto.getQueryKey(), "商品id或商品名称参数不能为空");
        CommonResponse<List<ProductSimpleInfoDto>> emptyResp = new CommonResponse<>(Collections.emptyList());
        if (StringUtils.isBlank(searchProductInfoDto.getQueryKey())) {
            return emptyResp;
        }
        List<ProductSimpleInfoDto> result = productLineQueryService.searchProductInfoByIdOrName(searchProductInfoDto);

        return new CommonResponse<>(result);
    }
}
