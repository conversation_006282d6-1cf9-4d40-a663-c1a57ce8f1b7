<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.info.intl.retail.so.infra.database.mapper.sales.IntlSoSalesQtyMapper">

    <select id="queryTotalCountForSalesQty" resultType="java.lang.Integer">
        select count(*)
        from intl_so_qty isq
        left join intl_so_user_info isui on isui.id = isq.user_info_id
        left join intl_so_org_info isoi on isoi.id = isq.org_info_id
        left join intl_rms_store irs on irs.code = isq.store_rms_code
        left join intl_rms_position irpo on irpo.code = isq.position_rms_code
        left join intl_rms_retailer irr on irr.name = isoi.retailer_code
        left join intl_rms_product irpr on irpr.goods_id = isq.product_code
        <include refid="selectSalesQtyWhere"/>
    </select>

    <select id="querySalesQtyPage" resultType="com.mi.info.intl.retail.intlretail.service.api.so.sales.dto.SalesQtyRespDto">
        select
        isq.id as id,
        isq.salesman_mid as salesmanMid,
        isui.salesman_rmsaccount as	salesmanAccount,
        isui.createdby_jobtitle	as	salesmanTitle,
        isoi.store_code	as storeCode,
        irs.name as	storeName,
        isoi.store_grade as	storeGrade,
        isoi.store_type as storeType,
        isoi.store_channel_type as channelType,
        isoi.store_hasSR as hasSr,
        isoi.store_hasPC as hasPc,
        isoi.position_rms_code as positionCode,
        irpo.name as positionName,
        isoi.position_type as positionType,
        isoi.retailer_code as retailerCode,
        irr.name as retailerName,
        irs.country_id_name as country,
        irs.province_label as province,
        irs.city_id_name as	city,
        irpr.code69 as code69,
        isq.product_code as productId,
        isq.sales_time as SalesTime,
        irpr.sku_name as skuName,
        irpr.spu_name_en as spuEn,
        irpr.product_line_en as productLineEn,
        isq.rrp ,
        isq.currency,
        isq.reporting_type as reportingType,
        isq.is_photo_exist as isPhotoExist,
        isq.note as remark,
        isq.createdby as createdBy,
        isq.createdon as createdOn
        from intl_so_qty isq
        left join intl_so_user_info isui on isui.id = isq.user_info_id
        left join intl_so_org_info isoi on isoi.id = isq.org_info_id
        left join intl_rms_store irs on irs.code = isq.store_rms_code
        left join intl_rms_position irpo on irpo.code = isq.position_rms_code
        left join intl_rms_retailer irr on irr.name = isoi.retailer_code
        left join intl_rms_product irpr on irpr.goods_id = isq.product_code
        <include refid="selectSalesQtyWhere"/>
        order by isq.id
        limit #{reqDto.pageSize} offset #{reqDto.offset}
    </select>

    <select id="getQtyReportingRoleData" resultType="com.mi.info.intl.retail.intlretail.service.api.so.sales.dto.StatisticsRespDto">
        select
        isq.reporting_type as `key`,count(*) as `count`
        from intl_so_qty isq
        left join intl_so_user_info isui on isui.id = isq.user_info_id
        left join intl_so_org_info isoi on isoi.id = isq.org_info_id
        left join intl_rms_store irs on irs.code = isq.store_rms_code
        left join intl_rms_product irpr on irpr.goods_id = isq.product_code
        <include refid="selectSalesQtyWhere"/>
        group by isq.reporting_type
    </select>

    <select id="getQtyReportingTypeData" resultType="com.mi.info.intl.retail.intlretail.service.api.so.sales.dto.StatisticsRespDto">
        select
        isui.createdby_jobtitle	as `key`,count(*) as `count`
        from intl_so_qty isq
        left join intl_so_user_info isui on isui.id = isq.user_info_id
        left join intl_so_org_info isoi on isoi.id = isq.org_info_id
        left join intl_rms_store irs on irs.code = isq.store_rms_code
        left join intl_rms_product irpr on irpr.goods_id = isq.product_code
        <include refid="selectSalesQtyWhere"/>
        group by isui.createdby_jobtitle
    </select>

    <sql id="selectSalesQtyWhere">
        <where>
            <if test="reqDto.id != null"> and isq.id = #{reqDto.id}</if>
            <if test="reqDto.salesmanMid != null"> and isq.salesman_mid = #{reqDto.salesmanMid}</if>
            <if test="reqDto.salesmanJobtitleList != null and reqDto.salesmanJobtitleList.size()>0">
                and isui.salesman_jobtitle in
                <foreach collection="reqDto.salesmanJobtitleList" item="item" open="(" close=")" separator="," >
                    #{item}
                </foreach>
            </if>
            <if test="reqDto.storeRmsCode != null and reqDto.storeRmsCode!= ''">
                and isq.store_rms_code = #{reqDto.storeRmsCode}
            </if>
            <if test="reqDto.storeGradeList != null and reqDto.storeGradeList.size()>0">
                and isoi.store_grade in
                <foreach collection="reqDto.storeGradeList" item="item" open="(" close=")" separator="," >
                    #{item}
                </foreach>
            </if>
            <if test="reqDto.storeTypeList != null and reqDto.storeTypeList.size()>0">
                and isoi.store_type in
                <foreach collection="reqDto.storeTypeList" item="item" open="(" close=")" separator="," >
                    #{item}
                </foreach>
            </if>
            <if test="reqDto.storeChannelTypeList != null and reqDto.storeChannelTypeList.size()>0">
                and isoi.store_channel_type in
                <foreach collection="reqDto.storeChannelTypeList" item="item" open="(" close=")" separator="," >
                    #{item}
                </foreach>
            </if>
            <if test="reqDto.positionRmsCode != null and reqDto.positionRmsCode!= ''">
                and isq.position_rms_code = #{reqDto.positionRmsCode}
            </if>
            <if test="reqDto.positionTypeList != null and reqDto.positionTypeList.size()>0">
                and isoi.position_type in
                <foreach collection="reqDto.positionTypeList" item="item" open="(" close=")" separator="," >
                    #{item}
                </foreach>
            </if>
            <if test="reqDto.retailerCode != null and reqDto.retailerCode!= ''">
                and isoi.retailer_code = #{reqDto.retailerCode}
            </if>
            <if test="reqDto.countryShortcode != null and reqDto.countryShortcode!= ''">
                and irs.country_shortcode = #{reqDto.countryShortcode}
            </if>
            <if test="reqDto.code69 != null and reqDto.code69!= ''">
                and irpr.code69 = #{reqDto.code69}
            </if>
            <if test="reqDto.cityCode != null and reqDto.cityCode!= ''">
                and irs.city_code = #{reqDto.cityCode}
            </if>
            <if test="reqDto.productCode != null"> and isq.product_code = #{reqDto.productCode}</if>
            <if test="reqDto.salesStartTime != null"> and isq.sales_time <![CDATA[>=]]> #{reqDto.salesStartTime}</if>
            <if test="reqDto.salesEndTime != null"> and isq.sales_time <![CDATA[<=]]> #{reqDto.salesEndTime}</if>
            <if test="reqDto.spuNameEn != null and reqDto.spuNameEn!= ''">
                and irpr.spu_name_en = #{reqDto.spuNameEn}
            </if>
            <if test="reqDto.productLineEnList != null and reqDto.productLineEnList.size()>0">
                and irpr.product_line_en in
                <foreach collection="reqDto.productLineEnList" item="item" open="(" close=")" separator="," >
                    #{item}
                </foreach>
            </if>
            <if test="reqDto.reportingTypeList != null and reqDto.reportingTypeList.size()>0">
                and isq.reporting_type in
                <foreach collection="reqDto.reportingTypeList" item="item" open="(" close=")" separator="," >
                    #{item}
                </foreach>
            </if>
            <if test="reqDto.createdBy != null"> and isq.createdby = #{reqDto.createdBy}</if>
            <if test="reqDto.createStartTime != null"> and isq.createdon <![CDATA[>=]]> #{reqDto.createStartTime}</if>
            <if test="reqDto.createEndTime != null"> and isq.createdon <![CDATA[<=]]> #{reqDto.createEndTime}</if>
            <if test="reqDto.hasSrList != null and reqDto.hasSrList.size()>0">
                and isoi.store_hasSR in
                <foreach collection="reqDto.hasSrList" item="item" open="(" close=")" separator="," >
                    #{item}
                </foreach>
            </if>
            <if test="reqDto.hasPcList != null and reqDto.hasPcList.size()>0">
                and isoi.store_hasPC in
                <foreach collection="reqDto.hasPcList" item="item" open="(" close=")" separator="," >
                    #{item}
                </foreach>
            </if>
        </where>
    </sql>

</mapper>
