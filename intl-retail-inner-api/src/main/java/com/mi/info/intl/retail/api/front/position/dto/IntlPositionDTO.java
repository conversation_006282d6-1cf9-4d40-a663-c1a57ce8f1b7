package com.mi.info.intl.retail.api.front.position.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 零售商DTO职位
 *
 * <AUTHOR>
 * @date 2025/07/28
 */
@Accessors(chain = true)
@Setter
@Getter
public class IntlPositionDTO {

    /**
     * 阵地ID
     */
    private String positionId;

    /**
     * 职位代码
     */
    private String positionCode;

    /**
     * 零售商ID
     */
    private String retailerId;

    /**
     * 零售商代码
     */
    private String retailerCode;
    /**
     * 门店code
     */
    private String storeCode;

    /**
     * 门店ID
     */
    private String storeId;
    /**
     * rms-门店编码
     */
    private String storeCodeRMS;

    /**
     * 零售后台的门店编码
     */
    private String storeCodeNew;
}
