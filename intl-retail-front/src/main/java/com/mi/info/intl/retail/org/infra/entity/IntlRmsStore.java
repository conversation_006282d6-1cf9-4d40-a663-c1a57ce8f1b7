package com.mi.info.intl.retail.org.infra.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * RMS门店表
 *
 * @TableName intl_rms_store
 */
@TableName(value = "intl_rms_store")
@Data
public class IntlRmsStore implements Serializable {
    private static final long serialVersionUID = 834580072005292664L;

    /**
     *
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 唯一标识
     */
    @TableField(value = "store_id")
    private String storeId;

    /**
     * 门店代码
     */
    @TableField(value = "name")
    private String name;

    /**
     * 门店名称
     */
    @TableField(value = "type")
    private Integer type;

    /**
     * 门店类型
     */
    @TableField(value = "type_name")
    private String typeName;

    /**
     * 门店代码
     */
    @TableField(value = "code")
    private String code;

    /**
     * 门店主数据代码
     */
    @TableField(value = "crss_code")
    private String crssCode;

    /**
     * 零售商名称
     */
    @TableField(value = "retailer_name")
    private String retailerName;

    /**
     * 零售商代码
     */
    @TableField(value = "retailer_id")
    private String retailerId;

    /**
     * 零售商代码标签
     */
    @TableField(value = "retailer_id_name")
    private String retailerIdName;

    /**
     * 国家/地区
     */
    @TableField(value = "country_id")
    private String countryId;

    /**
     * 国家/地区标签
     */
    @TableField(value = "country_id_name")
    private String countryIdName;

    /**
     * 分销商
     */
    @TableField(value = "account_id")
    private String accountId;

    /**
     * 分销商标签
     */
    @TableField(value = "account_id_name")
    private String accountIdName;

    /**
     * 供应商
     */
    @TableField(value = "distributor_id")
    private String distributorId;

    /**
     * 供应商标签
     */
    @TableField(value = "distributor_id_name")
    private String distributorIdName;

    /**
     * 城市
     */
    @TableField(value = "city_id")
    private String cityId;

    /**
     * 城市编码
     */
    @TableField(value = "city_code")
    private String cityCode;

    /**
     * 城市标签
     */
    @TableField(value = "city_id_name")
    private String cityIdName;

    /**
     * 地址
     */
    @TableField(value = "address")
    private String address;

    /**
     * 渠道类型
     */
    @TableField(value = "channel_type")
    private Integer channelType;

    /**
     * 渠道类型标签
     */
    @TableField(value = "channel_type_name")
    private String channelTypeName;

    /**
     * 门店等级
     */
    @TableField(value = "grade")
    private Integer grade;

    /**
     * 门店等级标签
     */
    @TableField(value = "grade_name")
    private String gradeName;

    /**
     * 运营状态
     */
    @TableField(value = "operation_status")
    private Integer operationStatus;

    /**
     * 运营状态标签
     */
    @TableField(value = "operation_status_name")
    private String operationStatusName;

    /**
     * 是否有促
     */
    @TableField(value = "has_pc")
    private Integer hasPc;

    /**
     * 负责人
     */
    @TableField(value = "owner_id")
    private String ownerId;

    /**
     * 负责人标签
     */
    @TableField(value = "owner_id_name")
    private String ownerIdName;

    /**
     * 创建时间
     */
    @TableField(value = "created_on")
    private Date createdOn;

    /**
     * 修改时间
     */
    @TableField(value = "modified_on")
    private Date modifiedOn;

    /**
     * 是否可用
     */
    @TableField(value = "state_code")
    private Integer stateCode;

    /**
     * 写入时间
     */
    @TableField(value = "created_at")
    private Long createdAt;
    /**
     * 更新时间
     */
    @TableField(value = "updated_at")
    private Long updatedAt;

    /**
     * 省
     */
    @TableField(value = "province_code")
    private String provinceCode;

    /**
     * 省标签
     */
    @TableField(value = "province_label")
    private String provinceLabel;

    /**
     * 县
     */
    @TableField(value = "county_code")
    private String countyCode;

    /**
     * 县标签
     */
    @TableField(value = "county_label")
    private String countyLabel;

    /**
     * 国家短码
     */
    @TableField(value = "country_shortcode")
    private String countryShortcode;

    /**
     * grade的计算标识
     */
    @TableField(value = "grade_cal_flag")
    private Integer gradeCalFlag;
    /**
     * 网格组织编码
     */
    @TableField(value = "district_org_code")
    private String districtOrgCode;
    /**
     * 城市组织编码
     */
    @TableField(value = "division_org_code")
    private String divisionOrgCode;
    /**
     * 大区组织编码
     */
    @TableField(value = "area_org_code")
    private String areaOrgCode;
    /**
     * 国际区域组织编码
     */
    @TableField(value = "region_org_code")
    private String regionOrgCode;
    /**
     * 国家组织编码
     */
    @TableField(value = "country_org_code")
    private String countryOrgCode;

    /**
     * 是否有做功
     */
    @TableField(value = "store_class")
    private Integer storeClass;

    /**
     * 是否有督导
     */
    @TableField(value = "has_sr")
    private Integer hasSr;


}