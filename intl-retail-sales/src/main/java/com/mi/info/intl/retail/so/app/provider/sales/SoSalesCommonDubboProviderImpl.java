package com.mi.info.intl.retail.so.app.provider.sales;

import java.util.List;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import com.mi.info.intl.retail.exception.ErrorCodes;
import com.mi.info.intl.retail.intlretail.service.api.so.sales.dto.FileUploadReqDto;
import com.mi.info.intl.retail.intlretail.service.api.so.sales.dto.SearchReferenceReqDto;
import com.mi.info.intl.retail.intlretail.service.api.so.sales.dto.SearchReferenceRespDto;
import com.mi.info.intl.retail.intlretail.service.api.so.sales.provider.SoSalesCommonDubboProvider;
import com.mi.info.intl.retail.intlretail.util.AppProviderUtil;
import com.mi.info.intl.retail.model.CommonApiResponse;
import com.mi.info.intl.retail.so.domain.sales.enums.SearchReferenceTableEnum;
import com.mi.info.intl.retail.so.domain.sales.service.IntlSoCommonDomainService;
import com.xiaomi.mone.docs.annotations.dubbo.ApiDoc;
import com.xiaomi.mone.docs.annotations.dubbo.ApiModule;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@DubboService(group = "${center.dubbo.group:}", interfaceClass = SoSalesCommonDubboProvider.class)
@ApiModule(value = "国际渠道零售服务", apiInterface = SoSalesCommonDubboProvider.class)
public class SoSalesCommonDubboProviderImpl implements SoSalesCommonDubboProvider {

    @Resource
    private IntlSoCommonDomainService intlSoCommonDomainService;

    @ApiDoc(value = "/api/so/v1/salesCommon/getSearchReferencesData", description = "获取search reference列表数据")
    @Override
    public CommonApiResponse<List<SearchReferenceRespDto>> getSearchReferencesData(SearchReferenceReqDto searchReferenceReqDto) {
        if (null == searchReferenceReqDto) {
            log.error("[method]getSearchReferencesData:searchReferenceReqDto is null......");
            return CommonApiResponse.failure(ErrorCodes.PARAM_IS_EMPTY.getCode(), "The parameter 'searchReferenceReqDto' is empty");
        }
        String table = searchReferenceReqDto.getTable();
        String keyWord = searchReferenceReqDto.getKeyWord();
        String parentKey = searchReferenceReqDto.getParentKey();
        String countryCode = searchReferenceReqDto.getCountryCode();
        Integer type = searchReferenceReqDto.getType();
        log.info("enter getSearchReferencesData method::params:[table:{}, keyWord:{}, parentKey:{}, countryCode:{}, type:{}]",
                table, keyWord, parentKey, countryCode, type);
        if (StringUtils.isBlank(table)) {
            return CommonApiResponse.failure(ErrorCodes.PARAM_IS_EMPTY.getCode(), "The parameter 'table' is empty");
        }
        if (StringUtils.isBlank(keyWord)) {
            return CommonApiResponse.failure(ErrorCodes.PARAM_IS_EMPTY.getCode(), "The parameter 'keyWord' is empty");
        }
        SearchReferenceTableEnum tableEnum = SearchReferenceTableEnum.fromTableName(table);
        if (null == tableEnum) {
            return CommonApiResponse.failure(ErrorCodes.PARAM_IS_EMPTY.getCode(), "The parameter 'table' is incorrect");
        }
        if (SearchReferenceTableEnum.INTL_RMS_CITY.tableName().equals(tableEnum.tableName()) && StringUtils.isBlank(parentKey)) {
            return CommonApiResponse.failure(ErrorCodes.PARAM_IS_EMPTY.getCode(), "The parameter 'parentKey' is empty");
        }
        if (SearchReferenceTableEnum.INTL_SO_RULE_RETAILERS.tableName().equals(tableEnum.tableName())) {
            if (StringUtils.isBlank(countryCode)) {
                return CommonApiResponse.failure(ErrorCodes.PARAM_IS_EMPTY.getCode(), "The parameter 'countryCode' is empty");
            }
            if (null == type) {
                return CommonApiResponse.failure(ErrorCodes.PARAM_IS_EMPTY.getCode(), "The parameter 'type' is empty");
            }
        }
        return AppProviderUtil.wrap(log, "SoSalesCommonDubboProvider::getSearchReferencesData", searchReferenceReqDto,
                intlSoCommonDomainService::getSearchReferencesData, false);
    }

    @ApiDoc(value = "/api/so/v1/salesCommon/getPicturesByParams", description = "根据参数获取图片列表数据")
    @Override
    public CommonApiResponse<List<String>> getPicturesByParams(FileUploadReqDto fileUploadReqDto) {
        if (null == fileUploadReqDto) {
            log.error("[method]getPicturesByParams:fileUploadReqDto is null......");
            return CommonApiResponse.failure(ErrorCodes.PARAM_IS_EMPTY.getCode(), "The parameter 'fileUploadReqDto' is empty");
        }
        return AppProviderUtil.wrap(log, "SoSalesCommonDubboProvider::getPicturesByParams", fileUploadReqDto,
                intlSoCommonDomainService::getPicturesByParams, false);
    }


}
