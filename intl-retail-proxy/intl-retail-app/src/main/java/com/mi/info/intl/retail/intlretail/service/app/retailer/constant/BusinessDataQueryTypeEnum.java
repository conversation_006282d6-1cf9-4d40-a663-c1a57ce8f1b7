package com.mi.info.intl.retail.intlretail.service.app.retailer.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 业务数据查询类型枚举
 * 用于区分不同的查询逻辑
 *
 * <AUTHOR>
 * @date 2025-01-08
 */
@Getter
@AllArgsConstructor
public enum BusinessDataQueryTypeEnum {

    /**
     * 原促销员查询逻辑（调用外部接口）
     */
    ORIGINAL_PROMOTER(null, "原促销员查询"),

    /**
     * 督导查询逻辑（MySQL数据库）
     */
    SUPERVISOR(2, "督导查询"),

    /**
     * 新促销员查询逻辑（MySQL数据库）
     */
    NEW_PROMOTER(11, "新促销员查询");

    private final Integer code;
    private final String description;

    /**
     * 根据code获取枚举
     *
     * @param code 查询类型代码
     * @return 对应的枚举值
     */
    public static BusinessDataQueryTypeEnum getByCode(Integer code) {
        if (code == null) {
            return ORIGINAL_PROMOTER;
        }

        for (BusinessDataQueryTypeEnum type : values()) {
            if (code.equals(type.getCode())) {
                return type;
            }
        }

        return ORIGINAL_PROMOTER;
    }

    /**
     * 判断是否为MySQL查询类型
     *
     * @param code 查询类型代码
     * @return true表示使用MySQL查询，false表示使用外部接口
     */
    public static boolean isMysqlQuery(Integer code) {
        BusinessDataQueryTypeEnum type = getByCode(code);
        return type == SUPERVISOR || type == NEW_PROMOTER;
    }
}