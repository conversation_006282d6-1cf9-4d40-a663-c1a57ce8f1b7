package com.mi.info.intl.retail.intlretail.service.app.retailer.impl;

import com.mi.info.intl.retail.intlretail.service.api.retailer.dto.ChannelDto;
import com.mi.info.intl.retail.intlretail.service.api.retailer.dto.PositionDto;
import com.mi.info.intl.retail.intlretail.service.api.retailer.dto.RetailerAreaResponse;
import com.mi.info.intl.retail.intlretail.service.api.store.dto.BusinessDataInputRequest;
import com.mi.info.intl.retail.intlretail.service.api.store.dto.BusinessDataResponse;
import com.mi.info.intl.retail.intlretail.service.api.store.dto.ChannelInfoRequest;
import com.mi.info.intl.retail.intlretail.service.api.store.dto.ChannelInfoResponse;
import com.mi.info.intl.retail.intlretail.service.api.store.dto.RetailerInfoRequest;
import com.mi.info.intl.retail.intlretail.service.api.store.dto.RetailerInfoResponse;
import com.mi.info.intl.retail.intlretail.service.api.store.dto.RetailerListRequest;
import com.mi.info.intl.retail.intlretail.service.api.store.dto.RetailerListResponse;
import com.mi.info.intl.retail.intlretail.service.api.store.gateway.IGateWayChannelInfoService;
import com.mi.info.intl.retail.intlretail.service.api.store.gateway.dto.GateWayChannelInfoResponse;
import com.mi.info.intl.retail.user.app.UserService;
import com.mi.info.intl.retail.user.domain.UserInfoManager;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * RetailerServiceImpl 单元测试类
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("零售商服务实现类测试")
class RetailerServiceImplTest {

    @InjectMocks
    private RetailerServiceImpl retailerService;

    @Mock
    private IGateWayChannelInfoService gateWayChannelInfoService;

    @Mock
    private UserService userService;

    @Mock
    private UserInfoManager userInfoManager;

    private RetailerListRequest testRetailerListRequest;
    private BusinessDataInputRequest testBusinessDataRequest;
    private ChannelInfoRequest testChannelInfoRequest;
    private RetailerInfoRequest testRetailerInfoRequest;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        testRetailerListRequest = new RetailerListRequest();

        testBusinessDataRequest = new BusinessDataInputRequest();
        testBusinessDataRequest.setType(1); // 促销员查询
        testChannelInfoRequest = new ChannelInfoRequest();

        testRetailerInfoRequest = new RetailerInfoRequest();
    }

    @Test
    @DisplayName("获取区域信息 - 成功场景")
    void testGetArea_Success() {
        // Given
        RetailerAreaResponse expectedResponse = new RetailerAreaResponse();

        // When
        RetailerAreaResponse result = retailerService.getArea();

        // Then
        assertNotNull(result);
        assertEquals(expectedResponse, result);
    }

    @Test
    @DisplayName("获取区域信息 - 返回空")
    void testGetArea_Empty() {
        // Given

        // When
        RetailerAreaResponse result = retailerService.getArea();

        // Then
        assertNull(result);
    }

    @Test
    @DisplayName("获取渠道信息 - 成功场景")
    void testGetChannelInfo_Success() {
        // When
        RetailerAreaResponse result = retailerService.getArea();

        // Then
        assertNotNull(result);
    }

    @Test
    @DisplayName("获取渠道信息 - 返回空")
    void testGetChannelInfo_Empty() {
        // When
        RetailerAreaResponse result = retailerService.getArea();

        // Then
        assertNotNull(result);
    }

    @Test
    @DisplayName("获取零售商信息 - 成功场景")
    void testGetRetailerInfo_Success() {
        // Given
        RetailerInfoResponse expectedResponse = new RetailerInfoResponse();

        // Then
        assertNotNull(expectedResponse);
    }

    @Test
    @DisplayName("获取零售商信息 - 返回空")
    void testGetRetailerInfo_Empty() {
        // Given
        RetailerInfoResponse expectedResponse = new RetailerInfoResponse();

        // Then
        assertNotNull(expectedResponse);
    }

    @Test
    @DisplayName("获取零售商列表 - 成功场景")
    void testGetRetailerList_Success() {
        // Given
        List<RetailerListResponse> expectedList = Arrays.asList(
                createRetailerListResponse("RETAILER001", "零售商1"),
                createRetailerListResponse("RETAILER002", "零售商2")
        );

        when(gateWayChannelInfoService.queryRetailerList(any(RetailerListRequest.class)))
                .thenReturn(expectedList);

        // When
        List<RetailerListResponse> result = retailerService.getRetailerList(testRetailerListRequest);

        // Then
        assertNotNull(result);
        assertEquals(expectedList, result);
        verify(gateWayChannelInfoService, times(1)).queryRetailerList(testRetailerListRequest);
    }

    @Test
    @DisplayName("获取零售商列表 - 请求为空")
    void testGetRetailerList_NullRequest() {
        // When
        List<RetailerListResponse> result = retailerService.getRetailerList(null);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(gateWayChannelInfoService, never()).queryRetailerList(any());
    }

    @Test
    @DisplayName("获取零售商列表 - 服务为空")
    void testGetRetailerList_NullService() {
        // Given
        ReflectionTestUtils.setField(retailerService, "gateWayChannelInfoService", null);

        // When
        List<RetailerListResponse> result = retailerService.getRetailerList(testRetailerListRequest);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    @DisplayName("获取零售商列表 - 异常处理")
    void testGetRetailerList_Exception() {
        // Given
        when(gateWayChannelInfoService.queryRetailerList(any(RetailerListRequest.class)))
                .thenThrow(new RuntimeException("Service error"));

        // When
        List<RetailerListResponse> result = retailerService.getRetailerList(testRetailerListRequest);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(gateWayChannelInfoService, times(1)).queryRetailerList(testRetailerListRequest);
    }

    @Test
    @DisplayName("获取组织人员 - 促销员查询")
    void testGetOrgPerson_PromoterQuery() {
        // Given
        testBusinessDataRequest.setType(1); // 促销员查询
        List<BusinessDataResponse> expectedList = Arrays.asList(
                createBusinessDataResponse("USER001", "用户1"),
                createBusinessDataResponse("USER002", "用户2")
        );

        when(gateWayChannelInfoService.queryBusinessData(any(BusinessDataInputRequest.class)))
                .thenReturn(expectedList);

        // When
        List<BusinessDataResponse> result = retailerService.getOrgPerson(testBusinessDataRequest);

        // Then
        assertNotNull(result);
        assertEquals(expectedList, result);
        verify(gateWayChannelInfoService, times(1)).queryBusinessData(testBusinessDataRequest);
        verify(userService, never()).getUserPositions(any());
    }

    @Test
    @DisplayName("获取组织人员 - 督导查询")
    void testGetOrgPerson_SupervisorQuery() {
        // Given
        testBusinessDataRequest.setType(2); // 督导查询
        List<BusinessDataResponse> expectedList = Arrays.asList(
                createBusinessDataResponse("USER001", "督导1"),
                createBusinessDataResponse("USER002", "督导2")
        );

        when(userService.getUserPositions(any(BusinessDataInputRequest.class)))
                .thenReturn(expectedList);

        // When
        List<BusinessDataResponse> result = retailerService.getOrgPerson(testBusinessDataRequest);

        // Then
        assertNotNull(result);
        assertEquals(expectedList, result);
        verify(userService, times(1)).getUserPositions(testBusinessDataRequest);
        verify(gateWayChannelInfoService, never()).queryBusinessData(any());
    }

    @Test
    @DisplayName("获取组织人员 - 类型为空")
    void testGetOrgPerson_NullType() {
        // Given
        testBusinessDataRequest.setType(null);
        List<BusinessDataResponse> expectedList = Arrays.asList(
                createBusinessDataResponse("USER001", "用户1")
        );

        when(gateWayChannelInfoService.queryBusinessData(any(BusinessDataInputRequest.class)))
                .thenReturn(expectedList);

        // When
        List<BusinessDataResponse> result = retailerService.getOrgPerson(testBusinessDataRequest);

        // Then
        assertNotNull(result);
        assertEquals(expectedList, result);
        verify(gateWayChannelInfoService, times(1)).queryBusinessData(testBusinessDataRequest);
        verify(userService, never()).getUserPositions(any());
    }

    @Test
    @DisplayName("获取组织人员 - 类型不为2")
    void testGetOrgPerson_TypeNotTwo() {
        // Given
        testBusinessDataRequest.setType(3); // 其他类型
        List<BusinessDataResponse> expectedList = Arrays.asList(
                createBusinessDataResponse("USER001", "用户1")
        );

        when(gateWayChannelInfoService.queryBusinessData(any(BusinessDataInputRequest.class)))
                .thenReturn(expectedList);

        // When
        List<BusinessDataResponse> result = retailerService.getOrgPerson(testBusinessDataRequest);

        // Then
        assertNotNull(result);
        assertEquals(expectedList, result);
        verify(gateWayChannelInfoService, times(1)).queryBusinessData(testBusinessDataRequest);
        verify(userService, never()).getUserPositions(any());
    }

    @Test
    @DisplayName("获取组织人员 - 新促销员查询(type=11)带分页参数")
    void testGetOrgPerson_NewPromoterQuery() {
        // Given
        testBusinessDataRequest.setType(11); // 新促销员查询
        testBusinessDataRequest.setRegion("ID");
        testBusinessDataRequest.setPageNum(1);
        testBusinessDataRequest.setPageSize(50);
        List<BusinessDataResponse> expectedList = Arrays.asList(
                createBusinessDataResponse("USER001", "新促销员1"),
                createBusinessDataResponse("USER002", "新促销员2")
        );

        when(userInfoManager.getPromoterUserPositions(any(BusinessDataInputRequest.class)))
                .thenReturn(expectedList);

        // When
        List<BusinessDataResponse> result = retailerService.getOrgPerson(testBusinessDataRequest);

        // Then
        assertNotNull(result);
        assertEquals(expectedList, result);
        verify(userInfoManager, times(1)).getPromoterUserPositions(testBusinessDataRequest);
        verify(userService, never()).getUserPositions(any());
        verify(gateWayChannelInfoService, never()).queryBusinessData(any());
    }

    @Test
    @DisplayName("获取组织人员 - 新促销员查询使用midList")
    void testGetOrgPerson_NewPromoterQueryWithMidList() {
        // Given
        testBusinessDataRequest.setType(11);
        testBusinessDataRequest.setRegion("ID");
        testBusinessDataRequest.setMidList(Arrays.asList("12345", "67890"));
        testBusinessDataRequest.setPageNum(1);
        testBusinessDataRequest.setPageSize(100);

        List<BusinessDataResponse> expectedList = Arrays.asList(
                createBusinessDataResponse("12345", "促销员1"),
                createBusinessDataResponse("67890", "促销员2")
        );

        when(userInfoManager.getPromoterUserPositions(any(BusinessDataInputRequest.class)))
                .thenReturn(expectedList);

        // When
        List<BusinessDataResponse> result = retailerService.getOrgPerson(testBusinessDataRequest);

        // Then
        assertNotNull(result);
        assertEquals(expectedList, result);
        verify(userInfoManager, times(1)).getPromoterUserPositions(testBusinessDataRequest);
    }

    @Test
    @DisplayName("获取组织人员 - 新促销员查询带titleCode映射")
    void testGetOrgPerson_NewPromoterQueryWithTitleCodeMapping() {
        // Given
        testBusinessDataRequest.setType(11);
        testBusinessDataRequest.setRegion("ID");
        testBusinessDataRequest.setTitleCodeList(Arrays.asList(246, 247)); // 组织中台的titleCode
        testBusinessDataRequest.setPageNum(1);
        testBusinessDataRequest.setPageSize(50);

        List<BusinessDataResponse> expectedList = Arrays.asList(
                createBusinessDataResponse("USER001", "促销员1")
        );

        when(userInfoManager.getPromoterUserPositions(any(BusinessDataInputRequest.class)))
                .thenReturn(expectedList);

        // When
        List<BusinessDataResponse> result = retailerService.getOrgPerson(testBusinessDataRequest);

        // Then
        assertNotNull(result);
        assertEquals(expectedList, result);
        verify(userInfoManager, times(1)).getPromoterUserPositions(testBusinessDataRequest);
        // 验证titleCode会在UserInfoManager中进行映射转换
    }

    @Test
    @DisplayName("获取组织人员 - 验证查询类型枚举逻辑")
    void testGetOrgPerson_QueryTypeEnum() {
        // 测试督导查询 (type=2)
        testBusinessDataRequest.setType(2);
        when(userService.getUserPositions(any())).thenReturn(Collections.emptyList());

        retailerService.getOrgPerson(testBusinessDataRequest);
        verify(userService, times(1)).getUserPositions(any());

        // 重置mock
        reset(userService, userInfoManager, gateWayChannelInfoService);

        // 测试新促销员查询 (type=11)
        testBusinessDataRequest.setType(11);
        when(userInfoManager.getPromoterUserPositions(any())).thenReturn(Collections.emptyList());

        retailerService.getOrgPerson(testBusinessDataRequest);
        verify(userInfoManager, times(1)).getPromoterUserPositions(any());

        // 重置mock
        reset(userService, userInfoManager, gateWayChannelInfoService);

        // 测试原促销员查询 (type=null或其他值)
        testBusinessDataRequest.setType(null);
        when(gateWayChannelInfoService.queryBusinessData(any())).thenReturn(Collections.emptyList());

        retailerService.getOrgPerson(testBusinessDataRequest);
        verify(gateWayChannelInfoService, times(1)).queryBusinessData(any());
    }

    // 辅助方法
    private Object createAreaDto(String code, String name) {
        // 这里需要根据实际的AreaDto类来创建对象
        // 由于没有看到具体的类定义，这里返回一个模拟对象
        return new Object(); // 实际应该返回AreaDto对象
    }

    private ChannelDto createChannelDto(String code, String name) {
        ChannelDto dto = new ChannelDto();
        dto.setChannelName(name);
        return dto;
    }

    private Object createRetailerDto(String code, String name) {
        // 这里需要根据实际的RetailerDto类来创建对象
        return new Object(); // 实际应该返回RetailerDto对象
    }

    private RetailerListResponse createRetailerListResponse(String code, String name) {
        RetailerListResponse response = new RetailerListResponse();
        return response;
    }

    private BusinessDataResponse createBusinessDataResponse(String userId, String userName) {
        BusinessDataResponse response = new BusinessDataResponse();
        return response;
    }
}