package com.mi.info.intl.retail.retailer.entity;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;

/**
 * RMS零售商表
 *
 * @TableName intl_rms_retailer
 */
@TableName(value = "intl_rms_retailer")
@Data
public class IntlRmsRetailer implements Serializable {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 唯一标识
     */
    @TableField(value = "retailer_id")
    private String retailerId;

    /**
     * RMS零售商Code
     */
    @TableField(value = "name")
    private String name;

    /**
     * CRM代码
     */
    @TableField(value = "crm_code")
    private String crmCode;

    /**
     * 零售商名称
     */
    @TableField(value = "retailer_name")
    private String retailerName;

    /**
     * 是否来自CRM
     */
    @TableField(value = "from_crm")
    private Integer fromCrm;

    /**
     * 零售商简称
     */
    @TableField(value = "retailer_for_short")
    private String retailerForShort;

    /**
     * 重点零售商
     */
    @TableField(value = "key_retailer")
    private Integer keyRetailer;

    /**
     * 零售商等级
     */
    @TableField(value = "retailer_grade")
    private Integer retailerGrade;

    /**
     * 国家
     */
    @TableField(value = "country_id")
    private String countryId;

    /**
     * 国家名称
     */
    @TableField(value = "country_name")
    private String countryName;

    /**
     * 国家code
     */
    @TableField(value = "country_code")
    private String countryCode;

    /**
     * 省份code
     */
    @TableField(value = "province_code")
    private String provinceCode;

    /**
     * 省份名称
     */
    @TableField(value = "province_name")
    private String provinceName;

    /**
     * 城市code
     */
    @TableField(value = "city_code")
    private String cityCode;

    /**
     * 城市名称
     */
    @TableField(value = "city_name")
    private String cityName;

    /**
     * 县code
     */
    @TableField(value = "county_code")
    private String countyCode;

    /**
     * 县名称
     */
    @TableField(value = "county_name")
    private String countyName;

    /**
     * 地址
     */
    @TableField(value = "address")
    private String address;

    /**
     * 零售商渠道类型名称
     */
    @TableField(value = "retailer_channel_type_name")
    private String retailerChannelTypeName;

    /**
     * 零售商渠道类型
     */
    @TableField(value = "retailer_channel_type")
    private Integer retailerChannelType;


    /**
     * 创建日期
     */
    @TableField(value = "created_at")
    private Long createdAt;

    /**
     * 更新时间
     */
    @TableField(value = "updated_at")
    private Long updatedAt;

    /**
     * 状态，0表示可用，1表示停用
     */
    @TableField(value = "state_code")
    private Integer stateCode;

    /**
     * 是否是新零售商
     */
    @TableField(value = "is_new")
    private int isNew;

    /**
     * 国家缩写
     */
    @TableField(value = "country_shortcode")
    private String countryShortcode;

}