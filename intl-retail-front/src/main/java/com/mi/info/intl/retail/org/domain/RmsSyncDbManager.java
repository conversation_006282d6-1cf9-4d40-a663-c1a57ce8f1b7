package com.mi.info.intl.retail.org.domain;

import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.mi.info.intl.retail.core.feishu.service.SendMessageService;
import com.mi.info.intl.retail.enums.AlarmKey;
import com.mi.info.intl.retail.so.app.mq.SyncSoToEsProducer;
import com.mi.info.intl.retail.so.domain.datasync.enums.DataSyncDataTypeEnum;
import com.mi.info.intl.retail.so.domain.upload.entity.IntlSoImei;
import com.mi.info.intl.retail.so.domain.upload.entity.IntlSoQty;
import com.mi.info.intl.retail.so.infra.database.mapper.upload.IntlSoImeiMapper;
import com.mi.info.intl.retail.so.infra.database.mapper.upload.IntlSoQtyMapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.mi.info.intl.retail.api.so.rule.blacklist.SoBlacklistApiService;
import com.mi.info.intl.retail.api.so.rule.model.SoRuleRetailerModel;
import com.mi.info.intl.retail.api.so.rule.service.IntlSoRuleRetailerApiService;
import com.mi.info.intl.retail.cooperation.task.infra.entity.IntlRmsSignRule;
import com.mi.info.intl.retail.cooperation.task.infra.mapper.IntlRmsSignRuleMapper;
import com.mi.info.intl.retail.cooperation.task.infra.mapper.read.IntlRmsSignRuleReadMapper;
import com.mi.info.intl.retail.intlretail.service.api.mq.dto.RmsDbContentRequest;
import com.mi.info.intl.retail.intlretail.service.api.mq.dto.RmsDbRequest;
import com.mi.info.intl.retail.ldu.infra.entity.IntlRmsProduct;
import com.mi.info.intl.retail.ldu.infra.mapper.IntlRmsProductMapper;
import com.mi.info.intl.retail.org.infra.entity.IntlRmsCountryTimezone;
import com.mi.info.intl.retail.org.infra.entity.IntlRmsPosition;
import com.mi.info.intl.retail.org.infra.entity.IntlRmsStore;
import com.mi.info.intl.retail.org.infra.mapper.IntlRmsCountryTimezoneMapper;
import com.mi.info.intl.retail.org.infra.mapper.IntlRmsPositionMapper;
import com.mi.info.intl.retail.org.infra.mapper.IntlRmsStoreMapper;
import com.mi.info.intl.retail.org.infra.mapper.read.IntlRmsCountryTimezoneReadMapper;
import com.mi.info.intl.retail.org.infra.mapper.read.IntlRmsStoreReadMapper;
import com.mi.info.intl.retail.retailer.entity.IntlRmsRetailer;
import com.mi.info.intl.retail.retailer.mapper.IntlRmsRetailerMapper;
import com.mi.info.intl.retail.so.domain.upload.entity.*;
import com.mi.info.intl.retail.so.infra.database.mapper.upload.*;
import com.mi.info.intl.retail.user.infra.entity.IntlRmsPersonnelPosition;
import com.mi.info.intl.retail.user.infra.entity.IntlRmsUser;
import com.mi.info.intl.retail.user.infra.mapper.IntlRmsPersonnelPositionMapper;
import com.mi.info.intl.retail.user.infra.mapper.IntlRmsUserMapper;
import com.mi.info.intl.retail.user.infra.mapper.read.IntlRmsPersonnelPositionReadMapper;
import com.mi.info.intl.retail.user.infra.mapper.read.IntlRmsUserReadMapper;
import com.mi.info.intl.retail.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;

@Slf4j
@Service
public class RmsSyncDbManager {

    @Resource
    private IntlRmsStoreMapper intlRmsStoreMapper;
    @Resource
    private IntlRmsStoreReadMapper intlRmsStoreReadMapper;
    @Resource
    private IntlRmsPositionMapper intlRmsPositionMapper;
    @Resource
    private IntlRmsCountryTimezoneMapper intlRmsCountryTimezoneMapper;
    @Resource
    private IntlRmsCountryTimezoneReadMapper intlRmsCountryTimezoneReadMapper;
    @Resource
    private IntlRmsUserMapper intlRmsUserMapper;
    @Resource
    private IntlRmsUserReadMapper intlRmsUserReadMapper;
    @Resource
    private IntlRmsPersonnelPositionMapper intlRmsPersonnelPositionMapper;
    @Resource
    private IntlRmsPersonnelPositionReadMapper intlRmsPersonnelPositionReadMapper;
    @Resource
    private IntlRmsSignRuleMapper intlRmsSignRuleMapper;
    @Resource
    private IntlRmsSignRuleReadMapper intlRmsSignRuleReadMapper;
    @Resource
    private IntlRmsRetailerMapper intlRmsRetailerMapper;
    @Resource
    private SoBlacklistApiService soBlacklistApiService;
    @Resource
    private IntlRmsRrpMapper intlRmsRrpMapper;
    @Resource
    private IntlRmsProvinceMapper intlRmsProvinceMapper;
    @Resource
    private IntlRmsCityMapper intlRmsCityMapper;
    @Resource
    private IntlRmsSecondarychannelMapper intlRmsSecondarychannelMapper;
    @Resource
    private IntlRmsProductMapper intlRmsProductMapper;
    @Resource
    private IntlSoRuleRetailerApiService intlSoRuleRetailerApiService;
    @Resource
    private IntlSoImeiMapper intlSoImeiMapper;
    @Resource
    private IntlSoQtyMapper intlSoQtyMapper;
    @Resource
    private SyncSoToEsProducer syncSoToEsProducer;
    @Resource
    private SendMessageService sendMessageService;

    public void editDb(RmsDbRequest rmsDBRequest) {
        //按表名分组处理
        Map<String, List<RmsDbContentRequest>> tableGroup = rmsDBRequest.getRmsDBContentList().stream()
                .collect(Collectors.groupingBy(RmsDbContentRequest::getTable));
        //分别处理每组数据
        for (Map.Entry<String, List<RmsDbContentRequest>> entry : tableGroup.entrySet()) {
            String tableName = entry.getKey();
            List<RmsDbContentRequest> requests = entry.getValue();
            try {
                switch (tableName) {
                    case "intl_rms_store":
                        batchProcessStores(requests);
                        break;
                    case "intl_rms_user":
                        batchProcessUsers(requests);
                        break;
                    case "intl_rms_position":
                        batchProcessPositions(requests);
                        break;
                    case "intl_rms_country_timezone":
                        batchProcessCountryTimezones(requests);
                        break;
                    case "intl_rms_personnel_position":
                        batchProcessPersonnelPositions(requests);
                        break;
                    case "intl_rms_sign_rule":
                        batchProcessSignRules(requests);
                        break;
                    case "intl_rms_retailer":
                        batchProcessRetailers(requests);
                        break;
                    case "intl_so_sn_blacklist":
                        batchProcessSoSnBlacklists(requests);
                        break;
                    case "intl_rms_rrp":
                        batchProcessRrps(requests);
                        break;
                    case "intl_rms_province":
                        batchProcessProvinces(requests);
                        break;
                    case "intl_rms_city":
                        batchProcessCities(requests);
                        break;
                    case "intl_rms_secondarychannel":
                        batchProcessSecondaryChannels(requests);
                        break;
                    case "intl_rms_product":
                        batchProcessProducts(requests);
                        break;
                    default:
                        throw new RuntimeException("SyncRmsDb:table not found : tableName" + tableName);
                }
            } catch (Exception e) {
                sendMessageService.sendGroupTextMessage("oc_1e6d1e54ac5eae84730fe55e86a013e7",
                        AlarmKey.RMS_SYNC_DB.getEmail(), e.getMessage());
                log.error("SyncRmsDbError:{}, tableName:{}", e, tableName);
            }
        }
    }

    private void batchProcessStores(List<RmsDbContentRequest> requests) {
        requests.forEach(this::addStore);
    }

    private void batchProcessUsers(List<RmsDbContentRequest> requests) {
        requests.forEach(this::addUser);
    }

    private void batchProcessPositions(List<RmsDbContentRequest> requests) {
        List<IntlRmsPosition> positionsToInsert = new ArrayList<>();
        List<IntlRmsPosition> positionsToUpdate = new ArrayList<>();
        Map<String, IntlRmsPosition> existingPositionsMap = new HashMap<>();

        // 解析所有请求数据
        List<IntlRmsPosition> parsedPositions = requests.stream().map(request -> {
            IntlRmsPosition position = JsonUtil.json2bean(JsonUtil.bean2json(request.getContent()),
                    IntlRmsPosition.class);
            if (position != null && StringUtils.isBlank(position.getPositionCategory())) {
                position.setPositionCategory("[]");
            }
            return position;
        }).filter(position -> position != null && position.getPositionId() != null).collect(Collectors.toList());
        int batchSize = 500;
        // 获取已存在的记录
        if (!parsedPositions.isEmpty()) {
            List<String> positionIds =
                    parsedPositions.stream().map(IntlRmsPosition::getPositionId).collect(Collectors.toList());
            // 分批处理，避免SQL参数超过500个
            for (int i = 0; i < positionIds.size(); i += batchSize) {
                int endIndex = Math.min(i + batchSize, positionIds.size());
                List<String> batchPositionIds = positionIds.subList(i, endIndex);
                LambdaQueryWrapper<IntlRmsPosition> queryWrapper = Wrappers.lambdaQuery();
                queryWrapper.in(IntlRmsPosition::getPositionId, batchPositionIds);
                List<IntlRmsPosition> existingPositions = intlRmsPositionMapper.selectList(queryWrapper);
                existingPositionsMap.putAll(existingPositions.stream()
                        .collect(Collectors.toMap(IntlRmsPosition::getPositionId, position -> position)));
            }
        }

        long currentTime = System.currentTimeMillis();
        // 分类插入和更新的数据
        for (IntlRmsPosition position : parsedPositions) {
            IntlRmsPosition existingPosition = existingPositionsMap.get(position.getPositionId());
            if (existingPosition != null) {
                position.setId(existingPosition.getId());
                position.setUpdatedAt(currentTime);
                positionsToUpdate.add(position);
            } else {
                position.setCreatedAt(currentTime);
                position.setUpdatedAt(currentTime);
                positionsToInsert.add(position);
            }
        }

        // 批量插入
        if (!positionsToInsert.isEmpty()) {
            // 分批次插入，每批100条
            int size = 100;
            int totalBatches = (positionsToInsert.size() + size - 1) / size;
            for (int i = 0; i < positionsToInsert.size(); i += size) {
                try {
                    int endIndex = Math.min(i + size, positionsToInsert.size());
                    List<IntlRmsPosition> batch = positionsToInsert.subList(i, endIndex);
                    intlRmsPositionMapper.insertList(batch);
                    log.info("SyncRmsDb:insert positions batch {}/{}, size:{}",
                            i / size + 1, totalBatches, batch.size());
                } catch (Exception e) {
                    log.error("SyncRmsDb:insert positions batch {} failed", i / size + 1, e);
                    throw new RuntimeException("Failed to insert positions batch " + (i / size + 1), e);
                }
            }
        }

        // 批量更新
        if (!positionsToUpdate.isEmpty()) {
            positionsToUpdate.forEach(intlRmsPositionMapper::updateById);
            log.info("SyncRmsDb:update positions:{}", positionsToUpdate.size());

            // 批量获取旧记录，避免多次selectById
            List<Long> idsToUpdate = positionsToUpdate.stream()
                    .map(IntlRmsPosition::getId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            Map<Long, IntlRmsPosition> oldPositionsMap = new HashMap<>();
            if (!idsToUpdate.isEmpty()) {
                // 分批处理，避免SQL参数超过500个
                for (int i = 0; i < idsToUpdate.size(); i += batchSize) {
                    int endIndex = Math.min(i + batchSize, idsToUpdate.size());
                    List<Long> batchIds = idsToUpdate.subList(i, endIndex);
                    LambdaQueryWrapper<IntlRmsPosition> wrapper = Wrappers.lambdaQuery();
                    wrapper.in(IntlRmsPosition::getId, batchIds);
                    List<IntlRmsPosition> oldPositions = intlRmsPositionMapper.selectList(wrapper);
                    oldPositionsMap.putAll(oldPositions.stream()
                            .collect(Collectors.toMap(IntlRmsPosition::getId, Function.identity())));
                }
            }

            // 处理需要触发ES更新的记录
            for (IntlRmsPosition newPosition : positionsToUpdate) {
                if (StringUtils.isNotBlank(newPosition.getPositionId())) {
                    IntlRmsPosition oldPosition = oldPositionsMap.get(newPosition.getId());
                    if (oldPosition != null &&
                            oldPosition.getName() != null &&
                            !oldPosition.getName().equalsIgnoreCase(newPosition.getName())) {
                        log.info("batchProcessPositions_update_name id:{}, code:{}",
                                newPosition.getId(), newPosition.getCode());
                        // 更新IMEI数据es信息
                        processDataSync(oldPosition.getCode(), intlSoImeiMapper, IntlSoImei::getPositionRmsCode,
                                IntlSoImei::getId, DataSyncDataTypeEnum.IMEI);
                        // 更新QTY数据es信息
                        processDataSync(oldPosition.getCode(), intlSoQtyMapper, IntlSoQty::getPositionRmsCode,
                                IntlSoQty::getId, DataSyncDataTypeEnum.QTY);
                    }
                }
            }
        }
    }

    private void batchProcessCountryTimezones(List<RmsDbContentRequest> requests) {
        requests.forEach(this::addCountryTimezone);
    }

    private void batchProcessPersonnelPositions(List<RmsDbContentRequest> requests) {
        requests.forEach(this::addPersonnelPosition);
    }

    private void batchProcessSignRules(List<RmsDbContentRequest> requests) {
        requests.forEach(this::addSignRule);
    }

    private void batchProcessRetailers(List<RmsDbContentRequest> requests) {
        requests.forEach(this::addRetailer);
    }

    private void batchProcessSoSnBlacklists(List<RmsDbContentRequest> requests) {
        requests.forEach(this::addSoSnBlacklist);
    }

    private void batchProcessRrps(List<RmsDbContentRequest> requests) {
        requests.forEach(this::addRrp);
    }

    private void batchProcessProvinces(List<RmsDbContentRequest> requests) {
        requests.forEach(this::addProvince);
    }

    private void batchProcessCities(List<RmsDbContentRequest> requests) {
        requests.forEach(this::addCity);
    }

    private void batchProcessSecondaryChannels(List<RmsDbContentRequest> requests) {
        requests.forEach(this::addSecondaryChannel);
    }

    private void batchProcessProducts(List<RmsDbContentRequest> requests) {
        requests.forEach(this::updateProduct);
    }

    private void addSoSnBlacklist(RmsDbContentRequest rmsDBContentRequest) {
        log.info("SoSnBlacklist start :{}", rmsDBContentRequest);
        soBlacklistApiService.syncSoSnBlacklist(rmsDBContentRequest.getContent());
    }

    private void addRetailer(RmsDbContentRequest rmsDBContentRequest) {
        log.info("Retailer:{}", rmsDBContentRequest);
        IntlRmsRetailer intlRmsRetailer =
                JsonUtil.json2bean(JsonUtil.bean2json(rmsDBContentRequest.getContent()), IntlRmsRetailer.class);
        if (intlRmsRetailer != null && intlRmsRetailer.getRetailerId() != null) {
            LambdaQueryWrapper<IntlRmsRetailer> lambdaQuery = Wrappers.lambdaQuery();
            lambdaQuery.eq(IntlRmsRetailer::getRetailerId, intlRmsRetailer.getRetailerId());
            IntlRmsRetailer haveStore = intlRmsRetailerMapper.selectOne(lambdaQuery);
            if (haveStore != null) {
                intlRmsRetailer.setId(haveStore.getId());
                intlRmsRetailer.setCreatedAt(haveStore.getCreatedAt());
                intlRmsRetailer.setUpdatedAt(System.currentTimeMillis());
                intlRmsRetailer.setIsNew(0);
                intlRmsRetailerMapper.updateById(intlRmsRetailer);
                log.info("updateRetailer_update:{}", intlRmsRetailer);
            } else {
                intlRmsRetailer.setCreatedAt(System.currentTimeMillis());
                intlRmsRetailer.setUpdatedAt(System.currentTimeMillis());
                intlRmsRetailer.setIsNew(1);
                intlRmsRetailerMapper.insert(intlRmsRetailer);
                log.info("addRetailer_insert:{}", intlRmsRetailer);
            }
            saveOrUpdateSoRuleRetailer(intlRmsRetailer);
        }
    }

    /**
     * 保存或更新，so规则零售商
     *
     * @param intlRmsRetailer INTL RMS零售商
     */
    private void saveOrUpdateSoRuleRetailer(IntlRmsRetailer intlRmsRetailer) {
        SoRuleRetailerModel retailerModel = new SoRuleRetailerModel();
        retailerModel.setRetailerCode(intlRmsRetailer.getName());
        retailerModel.setRetailerName(intlRmsRetailer.getRetailerName());
        retailerModel.setChannelType(intlRmsRetailer.getRetailerChannelTypeName());
        retailerModel.setCountryName(intlRmsRetailer.getCountryName());
        retailerModel.setCountryCode(intlRmsRetailer.getCountryShortcode());
        retailerModel.setCreateRetailerTime(intlRmsRetailer.getCreatedAt());
        intlSoRuleRetailerApiService.saveOrUpdateByRetailerCode(retailerModel);
    }

    private void addStore(RmsDbContentRequest rmsDBContentRequest) {
        IntlRmsStore intlRmsStore =
                JsonUtil.json2bean(JsonUtil.bean2json(rmsDBContentRequest.getContent()), IntlRmsStore.class);
        if (intlRmsStore != null && intlRmsStore.getStoreId() != null) {
            LambdaQueryWrapper<IntlRmsStore> lambdaQuery = Wrappers.lambdaQuery();
            lambdaQuery.eq(IntlRmsStore::getStoreId, intlRmsStore.getStoreId());
            IntlRmsStore haveStore = intlRmsStoreReadMapper.selectOne(lambdaQuery);
            if (haveStore != null) {
                intlRmsStore.setId(haveStore.getId());
                intlRmsStore.setUpdatedAt(System.currentTimeMillis());
                intlRmsStoreMapper.updateById(intlRmsStore);
                log.info("addStore_update:{}", intlRmsStore);
                //更新时，Store Name(name)，Store City（city_code/city_id_name），StoreProvince（province_code\province_label），
                // 这些字段发生更新，触发ES更新
                if (!intlRmsStore.getName().equalsIgnoreCase(haveStore.getName()) ||
                        !intlRmsStore.getCityCode().equalsIgnoreCase(haveStore.getCityCode()) ||
                        !intlRmsStore.getCityIdName().equalsIgnoreCase(haveStore.getCityIdName()) ||
                        !intlRmsStore.getProvinceCode().equalsIgnoreCase(haveStore.getProvinceCode()) ||
                        !intlRmsStore.getProvinceLabel().equalsIgnoreCase(haveStore.getProvinceLabel())) {
                    //更新IMEI数据ES信息
                    processDataSync(haveStore.getCode(), intlSoImeiMapper,
                            IntlSoImei::getStoreRmsCode, IntlSoImei::getId, DataSyncDataTypeEnum.IMEI);
                    //更新QTY数据ES信息
                    processDataSync(haveStore.getCode(), intlSoQtyMapper,
                            IntlSoQty::getStoreRmsCode, IntlSoQty::getId, DataSyncDataTypeEnum.QTY);
                }
            } else {
                intlRmsStore.setCreatedAt(System.currentTimeMillis());
                intlRmsStore.setUpdatedAt(System.currentTimeMillis());
                intlRmsStoreMapper.insert(intlRmsStore);
                log.info("addStore_insert:{}", intlRmsStore);
            }
        }
    }

    private <T> void processDataSync(String code, BaseMapper<T> mapper,
                                     SFunction<T, String> codeField,
                                     SFunction<T, Long> idField,
                                     DataSyncDataTypeEnum dataType) {
        try {
            LambdaQueryWrapper<T> lambdaQuery = Wrappers.lambdaQuery();
            lambdaQuery.eq(codeField, code);
            List<T> records = mapper.selectList(lambdaQuery);
            if (!records.isEmpty()) {
                List<Long> idList = records.stream().map(idField).collect(Collectors.toList());
                // 拆分ID列表，每批次不超过500条
                int batchSize = 500;
                for (int i = 0; i < idList.size(); i += batchSize) {
                    int endIndex = Math.min(i + batchSize, idList.size());
                    List<Long> batchIds = idList.subList(i, endIndex);
                    syncSoToEsProducer.sendSyncEsMsg(dataType, batchIds, true);
                }
            } else {
                // 处理空集合情况，记录详细日志
                log.warn("获取{}类型数据ID列表为空或为null，dataType: {}, idList: {}", dataType, dataType, records);
            }
        } catch (Exception e) {
            log.error("处理{}类型数据更新时发生异常", dataType, e);
            // 根据业务需求决定是否重新抛出异常
            throw new RuntimeException("数据更新失败: " + dataType, e);
        }
    }

    private void addUser(RmsDbContentRequest rmsDBContentRequest) {

        log.info("addUser_MQ:{}", rmsDBContentRequest);
        IntlRmsUser intlRmsUser = JsonUtil.json2bean(JsonUtil.bean2json(rmsDBContentRequest.getContent()),
                IntlRmsUser.class);
        log.info("addUser_parsed result:{}", intlRmsUser);
        if (intlRmsUser != null && intlRmsUser.getRmsUserid() != null) {
            LambdaQueryWrapper<IntlRmsUser> lambdaQuery = Wrappers.lambdaQuery();
            lambdaQuery.eq(IntlRmsUser::getRmsUserid, intlRmsUser.getRmsUserid());
            IntlRmsUser haveUser = intlRmsUserReadMapper.selectOne(lambdaQuery);
            if (haveUser != null) {
                intlRmsUser.setId(haveUser.getId());
                intlRmsUser.setUpdatedAt(System.currentTimeMillis());
                intlRmsUserMapper.updateById(intlRmsUser);
            } else {
                intlRmsUser.setCreatedAt(System.currentTimeMillis());
                intlRmsUser.setUpdatedAt(System.currentTimeMillis());
                intlRmsUserMapper.insert(intlRmsUser);
            }
            log.info("addUser_result:{}", "success");
        }
    }

    private void addCountryTimezone(RmsDbContentRequest rmsDBContentRequest) {
        IntlRmsCountryTimezone intlRmsCountryTimezone =
                JsonUtil.json2bean(JsonUtil.bean2json(rmsDBContentRequest.getContent()), IntlRmsCountryTimezone.class);
        if (intlRmsCountryTimezone != null && intlRmsCountryTimezone.getCountryCode() != null) {
            LambdaQueryWrapper<IntlRmsCountryTimezone> lambdaQuery = Wrappers.lambdaQuery();
            lambdaQuery.eq(IntlRmsCountryTimezone::getCountryCode, intlRmsCountryTimezone.getCountryCode());
            IntlRmsCountryTimezone haveCountryTimezone = intlRmsCountryTimezoneReadMapper.selectOne(lambdaQuery);
            if (haveCountryTimezone != null) {
                intlRmsCountryTimezone.setId(haveCountryTimezone.getId());
                intlRmsCountryTimezone.setUpdatedAt(System.currentTimeMillis());
                intlRmsCountryTimezoneMapper.updateById(intlRmsCountryTimezone);
                log.info("addCountryTimezone_update:{}", intlRmsCountryTimezone);
            } else {
                intlRmsCountryTimezone.setCreatedAt(System.currentTimeMillis());
                intlRmsCountryTimezone.setUpdatedAt(System.currentTimeMillis());
                intlRmsCountryTimezoneMapper.insert(intlRmsCountryTimezone);
                log.info("addCountryTimezone_insert:{}", intlRmsCountryTimezone);
            }
        }
    }

    private void addPersonnelPosition(RmsDbContentRequest rmsDBContentRequest) {
        IntlRmsPersonnelPosition intlRmsPersonnelPosition =
                JsonUtil.json2bean(JsonUtil.bean2json(rmsDBContentRequest.getContent()),
                        IntlRmsPersonnelPosition.class);
        if (intlRmsPersonnelPosition != null && intlRmsPersonnelPosition.getAssociationId() != null) {
            LambdaQueryWrapper<IntlRmsPersonnelPosition> lambdaQuery = Wrappers.lambdaQuery();
            lambdaQuery.eq(IntlRmsPersonnelPosition::getAssociationId, intlRmsPersonnelPosition.getAssociationId());
            IntlRmsPersonnelPosition havePersonnelPosition = intlRmsPersonnelPositionReadMapper.selectOne(lambdaQuery);
            if (havePersonnelPosition != null) {
                intlRmsPersonnelPosition.setId(havePersonnelPosition.getId());
                intlRmsPersonnelPosition.setUpdatedAt(System.currentTimeMillis());
                intlRmsPersonnelPositionMapper.updateById(intlRmsPersonnelPosition);
                log.info("addPersonnelPosition_update:{}", intlRmsPersonnelPosition);
            } else {
                intlRmsPersonnelPosition.setCreatedAt(System.currentTimeMillis());
                intlRmsPersonnelPosition.setUpdatedAt(System.currentTimeMillis());
                intlRmsPersonnelPositionMapper.insert(intlRmsPersonnelPosition);
                log.info("addPersonnelPosition_insert:{}", intlRmsPersonnelPosition);
            }
        }
    }

    private void addSignRule(RmsDbContentRequest rmsDBContentRequest) {
        IntlRmsSignRule intlRmsSignRule = JsonUtil.json2bean(JsonUtil.bean2json(rmsDBContentRequest.getContent()),
                IntlRmsSignRule.class);
        if (intlRmsSignRule != null && intlRmsSignRule.getSignRuleId() != null) {
            LambdaQueryWrapper<IntlRmsSignRule> lambdaQuery = Wrappers.lambdaQuery();
            lambdaQuery.eq(IntlRmsSignRule::getSignRuleId, intlRmsSignRule.getSignRuleId());
            IntlRmsSignRule haveSignRule = intlRmsSignRuleReadMapper.selectOne(lambdaQuery);
            if (haveSignRule != null) {
                intlRmsSignRule.setId(haveSignRule.getId());
                intlRmsSignRule.setUpdatedAt(System.currentTimeMillis());
                intlRmsSignRuleMapper.updateById(intlRmsSignRule);
                log.info("addSignRule_update:{}", intlRmsSignRule);
            } else {
                intlRmsSignRule.setCreatedAt(System.currentTimeMillis());
                intlRmsSignRule.setUpdatedAt(System.currentTimeMillis());
                intlRmsSignRuleMapper.insert(intlRmsSignRule);
                log.info("addSignRule_insert:{}", intlRmsSignRule);
            }
        }
    }

    private void addRrp(RmsDbContentRequest rmsDBContentRequest) {
        try {
            IntlRmsRrp intlRmsRrp =
                    JsonUtil.json2bean(JsonUtil.bean2json(rmsDBContentRequest.getContent()), IntlRmsRrp.class);
            if (intlRmsRrp != null && intlRmsRrp.getRrpId() != null) {
                LambdaQueryWrapper<IntlRmsRrp> lambdaQuery = Wrappers.lambdaQuery();
                lambdaQuery.eq(IntlRmsRrp::getRrpId, intlRmsRrp.getRrpId());
                IntlRmsRrp haveRrp = intlRmsRrpMapper.selectOne(lambdaQuery);
                if (haveRrp != null) {
                    intlRmsRrp.setId(haveRrp.getId());
                    intlRmsRrp.setUpdatedAt(System.currentTimeMillis());
                    intlRmsRrpMapper.updateById(intlRmsRrp);
                    log.info("addRrp_update:{}", intlRmsRrp);
                } else {
                    intlRmsRrp.setCreatedAt(System.currentTimeMillis());
                    intlRmsRrp.setUpdatedAt(System.currentTimeMillis());
                    intlRmsRrpMapper.insert(intlRmsRrp);
                    log.info("addRrp_insert:{}", intlRmsRrp);
                }
            }
        } catch (Exception e) {
            log.error("addRrp error", e);
        }
    }

    private void addProvince(RmsDbContentRequest rmsDBContentRequest) {
        try {
            IntlRmsProvince intlRmsProvince =
                    JsonUtil.json2bean(JsonUtil.bean2json(rmsDBContentRequest.getContent()), IntlRmsProvince.class);
            if (intlRmsProvince != null && intlRmsProvince.getProvinceCode() != null) {
                LambdaQueryWrapper<IntlRmsProvince> lambdaQuery = Wrappers.lambdaQuery();
                lambdaQuery.eq(IntlRmsProvince::getProvinceCode, intlRmsProvince.getProvinceCode());
                IntlRmsProvince haveProvince = intlRmsProvinceMapper.selectOne(lambdaQuery);
                if (haveProvince != null) {
                    intlRmsProvince.setId(haveProvince.getId());
                    intlRmsProvince.setUpdatedAt(System.currentTimeMillis());
                    intlRmsProvinceMapper.updateById(intlRmsProvince);
                    log.info("addProvince_update:{}", intlRmsProvince);
                } else {
                    intlRmsProvince.setCreatedAt(System.currentTimeMillis());
                    intlRmsProvince.setUpdatedAt(System.currentTimeMillis());
                    intlRmsProvinceMapper.insert(intlRmsProvince);
                    log.info("addProvince_insert:{}", intlRmsProvince);
                }
            }
        } catch (Exception e) {
            log.error("addProvince_error", e);
        }
    }

    private void addCity(RmsDbContentRequest rmsDBContentRequest) {
        try {
            IntlRmsCity intlRmsCity =
                    JsonUtil.json2bean(JsonUtil.bean2json(rmsDBContentRequest.getContent()), IntlRmsCity.class);
            if (intlRmsCity != null && intlRmsCity.getCityCode() != null) {
                LambdaQueryWrapper<IntlRmsCity> lambdaQuery = Wrappers.lambdaQuery();
                lambdaQuery.eq(IntlRmsCity::getCityCode, intlRmsCity.getCityCode());
                IntlRmsCity haveCity = intlRmsCityMapper.selectOne(lambdaQuery);
                if (haveCity != null) {
                    intlRmsCity.setId(haveCity.getId());
                    intlRmsCity.setUpdatedAt(System.currentTimeMillis());
                    intlRmsCityMapper.updateById(intlRmsCity);
                    log.info("addCity_update:{}", intlRmsCity);
                } else {
                    intlRmsCity.setCreatedAt(System.currentTimeMillis());
                    intlRmsCity.setUpdatedAt(System.currentTimeMillis());
                    intlRmsCityMapper.insert(intlRmsCity);
                    log.info("addCity_insert:{}", intlRmsCity);
                }
            }
        } catch (Exception e) {
            log.error("addCity_error", e);
        }
    }

    private void addSecondaryChannel(RmsDbContentRequest rmsDBContentRequest) {
        try {
            IntlRmsSecondarychannel intlRmsSecondarychannel =
                    JsonUtil.json2bean(JsonUtil.bean2json(rmsDBContentRequest.getContent()),
                            IntlRmsSecondarychannel.class);
            if (intlRmsSecondarychannel != null && intlRmsSecondarychannel.getChannelId() != null) {
                LambdaQueryWrapper<IntlRmsSecondarychannel> lambdaQuery = Wrappers.lambdaQuery();
                lambdaQuery.eq(IntlRmsSecondarychannel::getChannelId, intlRmsSecondarychannel.getChannelId());
                IntlRmsSecondarychannel haveSecondaryChannel = intlRmsSecondarychannelMapper.selectOne(lambdaQuery);
                if (haveSecondaryChannel != null) {
                    intlRmsSecondarychannel.setId(haveSecondaryChannel.getId());
                    intlRmsSecondarychannel.setUpdatedAt(System.currentTimeMillis());
                    intlRmsSecondarychannelMapper.updateById(intlRmsSecondarychannel);
                    log.info("addSecondaryChannel_update:{}", intlRmsSecondarychannel);
                } else {
                    intlRmsSecondarychannel.setCreatedAt(System.currentTimeMillis());
                    intlRmsSecondarychannel.setUpdatedAt(System.currentTimeMillis());
                    intlRmsSecondarychannelMapper.insert(intlRmsSecondarychannel);
                    log.info("addSecondaryChannel_insert:{}", intlRmsSecondarychannel);
                }
            }
        } catch (Exception e) {
            log.error("addSecondaryChannel_error", e);
        }
    }

    private void updateProduct(RmsDbContentRequest rmsDBContentRequest) {
        try {
            IntlRmsProduct intlRmsProduct =
                    JsonUtil.json2bean(JsonUtil.bean2json(rmsDBContentRequest.getContent()), IntlRmsProduct.class);
            if (intlRmsProduct != null && intlRmsProduct.getGoodsId() != null) {
                LambdaQueryWrapper<IntlRmsProduct> lambdaQuery = Wrappers.lambdaQuery();
                lambdaQuery.eq(IntlRmsProduct::getGoodsId, intlRmsProduct.getGoodsId());
                IntlRmsProduct haveProduct = intlRmsProductMapper.selectOne(lambdaQuery);
                if (haveProduct != null) {
                    haveProduct.setModelLevel(intlRmsProduct.getModelLevel());
                    haveProduct.setModelLevelName(intlRmsProduct.getModelLevelName());
                    intlRmsProductMapper.updateById(haveProduct);
                }
            }
        } catch (Exception e) {
            log.error("updateProduct_error", e);
        }

    }
}
