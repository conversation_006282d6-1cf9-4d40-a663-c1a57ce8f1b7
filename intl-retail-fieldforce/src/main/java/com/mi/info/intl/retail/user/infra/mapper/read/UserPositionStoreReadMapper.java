package com.mi.info.intl.retail.user.infra.mapper.read;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mi.info.intl.retail.user.infra.entity.UserPositionStore;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface UserPositionStoreReadMapper extends BaseMapper<UserPositionStore> {

    List<UserPositionStore> getUserPositionStore(@Param("country") String country,
            @Param("titles") List<Integer> titles, @Param("storeGrades") List<String> storeGrades, @Param("midList") List<Long> midList);

    /**
     * 查询促销员用户阵地信息（新的MySQL查询逻辑）
     *
     * @param region 国家短代码（必填）
     * @param titleCodeList 职位代码列表
     * @param retailerCodeList 零售商代码列表
     * @param positionTypeList 阵地类型列表
     * @param positionCodeList 阵地代码列表
     * @param channelTypeList 渠道类型列表
     * @param offset 分页偏移量
     * @param limit 每页数量
     * @return 促销员用户阵地信息列表
     */
    List<UserPositionStore> getPromoterUserPositions(@Param("region") String region,
            @Param("titleCodeList") List<Integer> titleCodeList,
            @Param("retailerCodeList") List<String> retailerCodeList,
            @Param("positionTypeList") List<Integer> positionTypeList,
            @Param("positionCodeList") List<String> positionCodeList,
            @Param("channelTypeList") List<Integer> channelTypeList,
            @Param("offset") Integer offset,
            @Param("limit") Integer limit);

    /**
     * 统计促销员用户阵地信息总数（用于分页）
     *
     * @param region 国家短代码（必填）
     * @param titleCodeList 职位代码列表
     * @param retailerCodeList 零售商代码列表
     * @param positionTypeList 阵地类型列表
     * @param positionCodeList 阵地代码列表
     * @param channelTypeList 渠道类型列表
     * @return 总记录数
     */
    Integer countPromoterUserPositions(@Param("region") String region,
            @Param("titleCodeList") List<Integer> titleCodeList,
            @Param("retailerCodeList") List<String> retailerCodeList,
            @Param("positionTypeList") List<Integer> positionTypeList,
            @Param("positionCodeList") List<String> positionCodeList,
            @Param("channelTypeList") List<Integer> channelTypeList);

}
