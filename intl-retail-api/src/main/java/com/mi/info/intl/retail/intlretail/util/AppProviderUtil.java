package com.mi.info.intl.retail.intlretail.util;

import java.util.Optional;
import java.util.function.Consumer;

import org.slf4j.Logger;

import com.mi.info.intl.retail.exception.BizException;
import com.mi.info.intl.retail.model.CommonApiResponse;
import com.xiaomi.mit.common.function.Function0;
import com.xiaomi.mit.common.function.Function1;
import com.xiaomi.mit.common.function.Function2;
import com.xiaomi.mit.common.json.Jacksons;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;

/**
 * provider统一返回值工具
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/11 19:12
 */
@SuppressWarnings("all")
public class AppProviderUtil {

    public static final String UNKNOWN_ERROR = "unknown error :";
    public static final String SYS_ERROR = "system error";


    /**
     * 封装一个调用，将结果用 CommonApiResponse<T> 封装返回。
     *
     * @param log 日志
     * @param desc desc
     * @param req req
     * @param func 功能
     * @param printResLog 打印RES日志
     * @return {@link CommonApiResponse }<{@link R }>
     */
    public static <T, R> CommonApiResponse<R> wrap(Logger log, String desc, T req, Function1<T, R> func,
        boolean printResLog) {
        return wrap(log, desc, req, func, Optional.empty(), printResLog);
    }

    /**
     * 封装一个调用，将结果用 CommonApiResponse<T> 封装返回。
     *
     * @param log 日志
     * @param desc desc
     * @param req1 REQ1
     * @param req2 REQ2
     * @param func 功能
     * @return {@link CommonApiResponse }<{@link R }>
     */
    public static <T1, T2, R> CommonApiResponse<R> wrap(Logger log, String desc, T1 req1, T2 req2,
        Function2<T1, T2, R> func) {
        return wrap(log, desc, req1, req2, func, Optional.empty());
    }

    /**
     * 封装一个调用，将结果用 CommonApiResponse<T> 封装返回。
     *
     * @param log
     * @param desc
     * @param req
     * @param func
     * @param onFail
     * @param <T>
     * @param <R>
     * @return
     */
    public static <T, R> CommonApiResponse<R> wrap(Logger log, String desc, T req, Function1<T, R> func,
        Optional<R> onFail, boolean printResLog) {
        long beginTime = System.currentTimeMillis();
        CommonApiResponse<R> res;
        try {
            res = CommonApiResponse.success(func.apply(req));
        } catch (BizException e) {
            log.error("{}  error : {}", desc, e.getMessage());
            res = onFail.map(CommonApiResponse::success)
                .orElseGet(() -> CommonApiResponse.failure(e.getErrorCode().getCode(), e.getMessage()));
        } catch (Exception e) {
            String errorMessage = desc + "error" + e.getMessage();
            log.error(errorMessage, e);
            res = onFail.map(CommonApiResponse::success)
                .orElseGet(() -> CommonApiResponse.failure(GeneralCodes.InternalError.getCode(), SYS_ERROR));
        }
        log.info(desc + " cost:[" + (System.currentTimeMillis() - beginTime) + "]");
        if (printResLog) {
            log.info(desc + " res=" + Jacksons.BASE.lazyToJson(res));
        }
        return res;
    }


    /**
     * 封装一个调用，将结果用 CommonApiResponse<T> 封装返回。
     *
     * @param log 日志
     * @param desc desc
     * @param req1 REQ1
     * @param req2 REQ2
     * @param func 功能
     * @param onFail
     * @return {@link CommonApiResponse }<{@link R }>
     */
    public static <T1, T2, R> CommonApiResponse<R> wrap(Logger log, String desc, T1 req1, T2 req2,
        Function2<T1, T2, R> func, Optional<R> onFail) {
        CommonApiResponse<R> res;
        try {
            res = CommonApiResponse.success(func.apply(req1, req2));
        } catch (BizException e) {
            log.error("{}  error : {}", desc, e.getMessage());
            res = onFail.map(CommonApiResponse::success)
                .orElseGet(() -> CommonApiResponse.failure(e.getErrorCode().getCode(), e.getMessage()));
        } catch (Exception e) {
            String errorMessage = desc + UNKNOWN_ERROR + e.getMessage();
            log.error(errorMessage, e);
            res = onFail.map(CommonApiResponse::success)
                .orElseGet(() -> CommonApiResponse.failure(GeneralCodes.InternalError.getCode(), SYS_ERROR));
        }
        return res;
    }

    /**
     * 封装一个调用，将结果用 CommonApiResponse<T> 封装返回。
     *
     * @param log 日志
     * @param desc desc
     * @param func 功能
     * @param printResLog 打印RES日志
     * @return {@link CommonApiResponse }<{@link R }>
     */
    public static <R> CommonApiResponse<R> wrap(Logger log, String desc, Function0<R> func, boolean printResLog) {
        return wrap(log, desc, func, printResLog, Optional.empty());
    }

    /**
     * 裹
     *
     * @param log 日志
     * @param desc desc
     * @param func 功能
     * @param printResLog 打印RES日志
     * @param onFail 失败
     * @return {@link CommonApiResponse }<{@link R }>
     */
    public static <R> CommonApiResponse<R> wrap(Logger log, String desc, Function0<R> func, boolean printResLog,
        Optional<R> onFail) {
        CommonApiResponse<R> res;
        try {
            res = CommonApiResponse.success(func.apply());
        } catch (BizException e) {
            log.error("{}  error : {}", desc, e.getMessage());
            res = onFail.map(CommonApiResponse::success)
                .orElseGet(() -> CommonApiResponse.failure(e.getErrorCode(), e.getMessage()));
        } catch (Exception e) {
            String errorMessage = desc + UNKNOWN_ERROR + e.getMessage();
            log.error(errorMessage, e);
            res = onFail.map(CommonApiResponse::success)
                .orElseGet(() -> CommonApiResponse.failure(GeneralCodes.InternalError, SYS_ERROR));
        }
        if (printResLog) {
            log.info(desc + " res=" + Jacksons.BASE.lazyToJson(res));
        }
        return res;
    }

    /**
     * 包装一个调用，将结果用 CommonApiResponse<T> 封装返回。用于被调用方法无返回值的情况
     *
     * @param log log
     * @param desc 描述
     * @param func 调用方法
     * @param req 请求参数
     * @param printResLog 是否打印响应日志
     * @return CommonApiResponse<Void>
     * @param <T> 入参类型
     */
    public static <T> CommonApiResponse<Void> wrap(Logger log, String desc, T req, Consumer<T> func,
        boolean printResLog) {
        long beginTime = System.currentTimeMillis();
        CommonApiResponse<Void> res;
        try {
            func.accept(req);
            res = CommonApiResponse.success(null);
        } catch (BizException e) {
            log.error("{}  error : {}", desc, e.getMessage());
            res = CommonApiResponse.failure(e.getErrorCode().getCode(), e.getMessage());
        } catch (Exception e) {
            String errorMessage = desc + "error" + e.getMessage();
            log.error(errorMessage, e);
            res = CommonApiResponse.failure(GeneralCodes.InternalError.getCode(), SYS_ERROR);
        }
        log.info(desc + " cost:[" + (System.currentTimeMillis() - beginTime) + "]");
        if (printResLog) {
            log.info(desc + " res=" + Jacksons.BASE.lazyToJson(res));
        }
        return res;
    }
}
