package com.mi.info.intl.retail.api.task.enums;

import lombok.Getter;

@Getter
public enum TaskActionEnum {
    //督导任务
    SR_INSPECTION_MAIN("SR_INSPECTION_MAIN", 201L, ""),
    SR_INSPECTION("SR_INSPECTION", 202L, ""),
    IN_STORE_TRAINING("InStoreTraining", 1090L, "TRAINING_REPORTING"),
    STORE_CHECK_SR("StoreCheck", 1091L, "STORE_CHECK"),
    CHECK_IN_SR("SignIn", 1092L, "CHECK_IN"),
    CHECK_OUT_SR("SignOut", 1093L, "CHECK_OUT"),
    SAMPLE_DEVICE_REPORTING_SR("LDU", 1094L, "SAMPLE_DEVICE_REPORTING"),
    DISPLAY_INSPECTION("DisplayInspection", 1095L, "DISPLAY_REPORTING"),
    STOCK_UPLOAD("StockUpload", 1096L, "INVENTORY_REPORTING"),
    SALES_UPLOAD_QTY("SalesUpload_Qty", 1097L, "SALES_REPORTING"),
    SALES_UPLOAD_IMEI("new_CreationIMEIInfo", 1097L, "SALES_REPORTING"),
    SALES_UPLOAD_NO_SALES("SalesUpload_No_Sales", 1097L, "SALES_REPORTING"),
    CREATION_IMEI_INFO("--", -1L, ""), // -1 代表无任务ID

    // 促销员任务
    CHECK_IN("", 101L, "CHECK_IN"),
    CHECK_OUT("", 102L, "CHECK_OUT"),
    SHIFT_SCHEDULING("", 103L, "SHIFT_SCHEDULING"),
    SAMPLE_DEVICE_REPORTING("", 104L, "SAMPLE_DEVICE_REPORTING"),
    DISPLAY_REPORTING("", 105L, "DISPLAY_REPORTING"),
    SALES_REPORTING("", 106L, "SALES_REPORTING"),
    INVENTORY_REPORTING("", 107L, "INVENTORY_REPORTING");

    private final String actionName;
    private final Long actionCode;

    /**
     * 大脑事件动作名称
     */
    private final String eventActionName;

    TaskActionEnum(String actionName, Long actionCode, String eventActionName) {
        this.actionName = actionName;
        this.actionCode = actionCode;
        this.eventActionName = eventActionName;
    }

    public static TaskActionEnum fromActionName(String actionName) {
        for (TaskActionEnum e : values()) {
            if (e.actionName.equals(actionName)) {
                return e;
            }
        }
        return null;
    }
}