package com.mi.info.intl.retail.org.infra.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.mi.info.intl.retail.intlretail.service.api.position.dto.InspectionSummaryDTO;
import com.mi.info.intl.retail.intlretail.service.api.position.dto.PositionInspectionRequest;
import com.mi.info.intl.retail.intlretail.service.api.position.dto.UnRemindedTaskDTO;
import com.mi.info.intl.retail.intlretail.service.api.position.enums.InspectionBusinessTypeEnum;
import com.mi.info.intl.retail.intlretail.service.api.position.enums.DisapproveReasonEnum;
import com.mi.info.intl.retail.intlretail.service.api.position.enums.I18nDesc;
import com.mi.info.intl.retail.intlretail.service.api.position.enums.InspectionStatusEnum;
import com.mi.info.intl.retail.intlretail.service.api.position.enums.StoreLimitedRangeEnum;
import com.mi.info.intl.retail.intlretail.service.api.position.enums.TaskStatusEnum;
import com.mi.info.intl.retail.org.domain.InspectionRecordDomain;
import com.mi.info.intl.retail.org.domain.dto.InspectionRecordDTO;
import com.mi.info.intl.retail.org.domain.repository.InspectionRecordRepository;
import com.mi.info.intl.retail.org.infra.entity.InspectionRecord;
import com.mi.info.intl.retail.org.infra.mapper.InspectionRecordMapper;
import com.mi.info.intl.retail.org.infra.mapper.read.InspectionRecordReadMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import javax.annotation.Resource;

/**
 * 巡检记录Repository实现类
 */
@Slf4j
@Repository
public class InspectionRecordRepositoryImpl implements InspectionRecordRepository {

    @Resource
    private InspectionRecordMapper inspectionRecordMapper;

    @Autowired
    private InspectionRecordReadMapper inspectionRecordReadMapper;

    @Override
    public boolean save(InspectionRecordDomain inspectionRecordDomain) {
        InspectionRecord entity = convertToEntity(inspectionRecordDomain);
        return inspectionRecordMapper.insert(entity) > 0;
    }

    @Override
    public InspectionRecordDomain getById(Long id) {
        InspectionRecord entity = inspectionRecordReadMapper.selectById(id);
        return entity != null ? convertToDomain(entity) : null;
    }

    @Override
    public List<InspectionRecordDomain> getByBusinessCode(String businessCode) {
        LambdaQueryWrapper<InspectionRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(InspectionRecord::getBusinessCode, businessCode);
        queryWrapper.orderByDesc(InspectionRecord::getCreatedTime);

        List<InspectionRecord> records = inspectionRecordReadMapper.selectList(queryWrapper);
        return records.stream()
                .map(this::convertToDomain)
                .collect(Collectors.toList());
    }

    @Override
    public List<InspectionRecordDomain> getByBusinessCodeList(List<String> businessCodes) {
        LambdaQueryWrapper<InspectionRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(InspectionRecord::getBusinessCode, businessCodes);

        List<InspectionRecord> records = inspectionRecordReadMapper.selectList(queryWrapper);
        return records.stream().map(this::convertToDomain).collect(Collectors.toList());
    }

    @Override
    public List<InspectionRecordDomain> getPendingApprovalByBusinessCode(String businessCode) {
        LambdaQueryWrapper<InspectionRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(InspectionRecord::getBusinessCode, businessCode)
                .eq(InspectionRecord::getInspectionStatus, InspectionStatusEnum.TO_BE_VERIFIED.getCode());

        List<InspectionRecord> records = inspectionRecordReadMapper.selectList(queryWrapper);
        return records.stream()
                .map(this::convertToDomain)
                .collect(Collectors.toList());
    }

    @Override
    public boolean update(InspectionRecordDomain inspectionRecordDomain) {
        InspectionRecord entity = convertToEntity(inspectionRecordDomain);
        return inspectionRecordMapper.updateById(entity) > 0;
    }

    @Override
    public InspectionRecordDomain getByRuleCodeAndBusinessCode(String ruleCode, String businessCode) {
        LambdaQueryWrapper<InspectionRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(InspectionRecord::getRuleCode, ruleCode)
                .eq(InspectionRecord::getBusinessCode, businessCode);
        
        InspectionRecord entity = inspectionRecordReadMapper.selectOne(queryWrapper);
        return entity != null ? convertToDomain(entity) : null;
    }

    @Override
    public List<InspectionRecordDTO> getPendingInspectionsByCountries(List<String> countries) {
        log.info("通过国家列表查询未下发的巡检记录: {}", countries);

        if (countries == null || countries.isEmpty()) {
            log.warn("国家列表为空，返回空结果");
            return Collections.emptyList();
        }

        // 调用Mapper查询未下发的巡检记录
        List<InspectionRecordDTO> records = inspectionRecordReadMapper.selectPendingInspectionsByCountries(
                countries, InspectionBusinessTypeEnum.getPostionInspectionCodeList()
        );
        return  records;

    }

    @Override
    public InspectionRecordDomain getByBusinessCodeAndConstructionActionCode(String businessCode, String constructionActionCode) {
        log.info("通过阵地代码和施工行为代码获取巡检记录: businessCode={}, constructionActionCode={}", businessCode, constructionActionCode);
        
        LambdaQueryWrapper<InspectionRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(InspectionRecord::getBusinessCode, businessCode)
                .eq(InspectionRecord::getConstructionActionCode, constructionActionCode)
                .in(InspectionRecord::getBusinessType, InspectionBusinessTypeEnum.getPostionInspectionCodeList())
                .orderByDesc(InspectionRecord::getCreatedTime)
                .last("LIMIT 1");  // 只取最新的一条记录
        
        InspectionRecord entity = inspectionRecordReadMapper.selectOne(queryWrapper);
        return entity != null ? convertToDomain(entity) : null;
    }

    /**
     * 将DTO对象转换为领域对象
     */
    private InspectionRecordDomain convertToDomain(InspectionRecordDTO dto) {
        if (dto == null) {
            return null;
        }
        InspectionRecordDomain domain = new InspectionRecordDomain();

        // 基本字段映射
        domain.setId(dto.getId());
        domain.setRuleCode(dto.getRuleCode());
        domain.setPositionCode(dto.getBusinessCode());
        domain.setPositionConstructionType(dto.getBusinessType());
        domain.setCountry(dto.getCountry());
        domain.setPositionCreationTime(dto.getBusinessCreationTime());
        domain.setActionCode(dto.getConstructionActionCode());

        // 状态字段映射
        domain.setTaskStatus(TaskStatusEnum.getByCode(dto.getTaskStatus()));
        domain.setTaskCompletionTime(dto.getTaskCompletionTime());
        domain.setInspectionStatus(InspectionStatusEnum.getByCode(dto.getInspectionStatus()));
        domain.setVerificationTime(dto.getVerificationTime());
        domain.setReminderTime(dto.getReminderTime());

        // 负责人信息
        domain.setInspectionOwner(dto.getInspectionOwner());
        domain.setInspectionOwnerMiId(dto.getInspectionOwnerMiId());

        return domain;
    }



    /**
     * 将领域对象转换为实体对象
     */
    private InspectionRecord convertToEntity(InspectionRecordDomain domain) {
        if (domain == null) {
            return null;
        }
        InspectionRecord entity = new InspectionRecord();

        // 基本字段
        entity.setId(domain.getId());
        entity.setRuleCode(domain.getRuleCode());

        // 映射业务代码和类型
        entity.setBusinessCode(domain.getPositionCode());
        entity.setBusinessType(domain.getPositionConstructionType());
        entity.setBusinessCreationTime(domain.getPositionCreationTime());
        entity.setConstructionActionCode(domain.getActionCode());

        // 映射任务状态
        entity.setTaskStatus(I18nDesc.safeGetCode(domain.getTaskStatus()));
        entity.setTaskCompletionTime(domain.getTaskCompletionTime());

        // 映射巡检状态
        entity.setInspectionStatus(I18nDesc.safeGetCode(domain.getInspectionStatus()));
        entity.setVerificationTime(domain.getVerificationTime());

        // 其他字段
        entity.setUploadData(domain.getUploadData());
        entity.setInspectionOwner(domain.getInspectionOwner());
        entity.setInspectionOwnerMiId(domain.getInspectionOwnerMiId());
        entity.setRemark(domain.getRemark());
        entity.setDisapproveReason(I18nDesc.safeGetCode(domain.getDisapproveReason()));
        entity.setVerifier(domain.getVerifier());
        entity.setVerifierMiid(domain.getVerifierMiid());

        // 创建和更新信息
        entity.setCreatedBy(domain.getCreatedBy());
        entity.setCreatedTime(domain.getCreatedOn());
        entity.setUpdatedBy(domain.getModifiedBy());
        entity.setUpdatedTime(domain.getModifiedOn());
        entity.setStoreLimitedRange(Optional.ofNullable(domain.getStoreLimitedRange()).map(StoreLimitedRangeEnum::getCode).orElse(null));
        entity.setTaskCreateInstanceTime(domain.getCreateInstanceTime());
        entity.setReminderTime(domain.getReminderTime());

        return entity;
    }

    /**
     * 将实体对象转换为领域对象
     */
    private InspectionRecordDomain convertToDomain(InspectionRecord entity) {
        if (entity == null) {
            return null;
        }
        InspectionRecordDomain domain = new InspectionRecordDomain();
        // 手动映射字段，避免自动映射导致的字段不匹配问题
        domain.setId(entity.getId());
        domain.setRuleCode(entity.getRuleCode());
        domain.setPositionCode(entity.getBusinessCode());
        domain.setPositionConstructionType(entity.getBusinessType());
        domain.setPositionCreationTime(entity.getBusinessCreationTime());
        domain.setActionCode(entity.getConstructionActionCode());
        domain.setTaskStatus(TaskStatusEnum.getByCode(entity.getTaskStatus()));
        domain.setTaskCompletionTime(entity.getTaskCompletionTime());
        domain.setInspectionStatus(InspectionStatusEnum.getByCode(entity.getInspectionStatus()));
        domain.setVerificationTime(entity.getVerificationTime());
        domain.setVerifier(entity.getVerifier());
        domain.setUploadData(entity.getUploadData());
        domain.setInspectionOwner(entity.getInspectionOwner());
        domain.setInspectionOwnerMiId(entity.getInspectionOwnerMiId());
        domain.setRemark(entity.getRemark());
        domain.setDisapproveReason(DisapproveReasonEnum.getByCode(entity.getDisapproveReason()));
        domain.setCreatedBy(entity.getCreatedBy());
        domain.setCreatedOn(entity.getCreatedTime());
        domain.setModifiedBy(entity.getUpdatedBy());
        domain.setModifiedOn(entity.getUpdatedTime());
        domain.setStoreLimitedRange(StoreLimitedRangeEnum.getByCode(entity.getStoreLimitedRange()));
        domain.setCreateInstanceTime(entity.getTaskCreateInstanceTime());
        return domain;
    }

    @Override
    public boolean existsUnCompletedTaskByOwner(String inspectionOwner) {
        LambdaQueryWrapper<InspectionRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(InspectionRecord::getInspectionOwner, inspectionOwner)
                .eq(InspectionRecord::getTaskStatus, TaskStatusEnum.NOT_COMPLETED.getCode())
                .select(InspectionRecord::getId);
        return inspectionRecordReadMapper.exists(queryWrapper);
    }

    /**
     * 查询未完成任务状态且规则状态为激活的巡检记录
     *
     * @return 符合条件的阵地巡检领域对象列表
     */
    @Override
    public List<UnRemindedTaskDTO> findActiveTasksWithActiveRules() {
        log.info("查询未完成任务状态且规则状态为激活的巡检记录");
        return inspectionRecordReadMapper.findActiveTasksWithActiveRules();
    }

    @Override
    public InspectionSummaryDTO getInspectionSummary(PositionInspectionRequest request) {
         // 处理时间戳：将秒级时间戳转换为毫秒级
         if (request.getCreateTimeStart() != null) {
            request.setCreateTimeStart(request.getCreateTimeStart() * 1000);
        }
        if (request.getCreateTimeEnd() != null) {
            request.setCreateTimeEnd(request.getCreateTimeEnd() * 1000 + 24 * 60 * 60 * 1000);
        }

         // 如果positionType请求参数为空，则默认设置为1，2参数
         if (request.getPositionConstructionType() == null || request.getPositionConstructionType().isEmpty()) {
            request.setPositionConstructionType(InspectionBusinessTypeEnum.getPostionInspectionCodeList());
        }
        

        return inspectionRecordReadMapper.getInspectionSummary(request);
    }

}
