package com.mi.info.intl.retail.intlretail.service.api.retailer.dto;

import lombok.Getter;

import java.io.Serializable;

/**
 * 公共响应体
 *
 * <AUTHOR>
 * @date 2025/07/25
 */
@Getter
public class CommonResponse<T> implements Serializable {
    private static final long serialVersionUID = 6465906308962043704L;
    private int code;
    private String message;
    private T data;
    private String traceId;

    public CommonResponse(T body) {
        this.code = 0;
        this.message = "ok";
        this.data = body;
    }

    public CommonResponse(int code, String message, T data) {
        this.code = code;
        this.message = message;
        this.data = data;
    }

    public static CommonResponse<String> failure(int code, String message) {
        return new CommonResponse<>(code, message, "");
    }
}

