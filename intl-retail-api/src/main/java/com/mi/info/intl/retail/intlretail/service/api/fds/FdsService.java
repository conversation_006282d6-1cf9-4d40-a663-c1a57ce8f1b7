package com.mi.info.intl.retail.intlretail.service.api.fds;

import com.mi.info.intl.retail.intlretail.service.api.fds.dto.FdsUploadResult;

import java.io.File;
import java.io.InputStream;

public interface FdsService {

    /**
     * upload file to fds
     *
     * @return public download url
     */
    FdsUploadResult upload(String objectName, File file, Boolean isDel);

    FdsUploadResult upload(String bucketName, String packageName, String objectName, File file, Boolean isDel);

    /**
     * FDS上传文件
     */
    String uploadFile(String objectName, InputStream inputStream);

}
