package com.mi.info.intl.retail.org.app.service.position;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mi.info.intl.retail.advice.excel.export.ExportExcelAspect;
import com.mi.info.intl.retail.cooperation.task.dto.dto.IntlRmsUserDto;
import com.mi.info.intl.retail.cooperation.task.inspection.IntlRmsUserService;
import com.mi.info.intl.retail.intlretail.service.api.fds.FdsService;
import com.mi.info.intl.retail.intlretail.service.api.position.PositionInspectionService;
import com.mi.info.intl.retail.intlretail.service.api.position.dto.*;
import com.mi.info.intl.retail.intlretail.service.api.position.enums.AbnormalReasonEnum;
import com.mi.info.intl.retail.intlretail.service.api.position.enums.I18nDesc;
import com.mi.info.intl.retail.intlretail.service.api.proxy.dto.taskcenter.TaskCenterFinishTaskReq;
import com.mi.info.intl.retail.intlretail.service.api.store.dto.PositionInspectionResponsiblePersonDTO;
import com.mi.info.intl.retail.model.CommonApiResponse;
import com.mi.info.intl.retail.model.PageResponse;
import com.mi.info.intl.retail.org.domain.InspectionRecordDomain;
import com.mi.info.intl.retail.org.domain.position.service.PositionInspectionDomainService;
import com.mi.info.intl.retail.org.domain.util.BeanConverter;
import com.xiaomi.mone.docs.annotations.dubbo.ApiModule;
import com.xiaomi.nr.global.dev.base.HeraContextKeyValueHolder;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.dubbo.rpc.RpcContext;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import javax.annotation.Resource;
import java.io.File;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.io.*;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Path;

/**
 * 阵地巡检服务实现类
 */
@Slf4j
@Service
@DubboService(group = "${dubbo-group.provider.intl-retail:}", interfaceClass = PositionInspectionService.class)
@ApiModule(value = "阵地巡检服务实现类", apiInterface = PositionInspectionService.class)
public class PositionInspectionServiceImpl implements PositionInspectionService {

    //主数据筛选框业务场景
    private static final String CHANNEL_RETAIL = "channelRetail";
    @Resource
    private PositionInspectionDomainService positionInspectionDomainService;

    @Resource
    private IntlRmsUserService intlRmsUserService;

    @Resource
    private FdsService fdsService;


    /**
     * 列表查询 
     * APP
     */
    @Override
    public PageResponse<PositionInspectionItem> listPositionInspection(PositionInspectionRequest request) {
        log.info("分页查询阵地巡检信息: {}", request);
        // 调用仓储层查询数据
        Page<PositionInspectionItem> resultPage = positionInspectionDomainService.pagePositionInspection(request);

        PositionItemListRequest positionItemListRequest = new PositionItemListRequest();
        positionItemListRequest.setBusinessScene(CHANNEL_RETAIL);
        //所有的映射枚举
        PositionSelectorItemList positionSelectorItemList = positionInspectionDomainService.getSelectorList(positionItemListRequest);

        Map<String, String> positionLocationMap = Optional.ofNullable(positionSelectorItemList)
                .map(PositionSelectorItemList::getPositionLocation)
                .orElse(Collections.emptyList())
                .stream()
                .filter(item -> item != null && item.getKey() != null)
                .collect(Collectors.toMap(OptionalItem::getKey, OptionalItem::getValue));

        Map<String, String> displayStandardizationMap = Optional.ofNullable(positionSelectorItemList)
                .map(PositionSelectorItemList::getDisplayStandardization)
                .orElse(Collections.emptyList())
                .stream()
                .filter(item -> item != null && item.getKey() != null)
                .collect(Collectors.toMap(OptionalItem::getKey, OptionalItem::getValue));

        Map<String, String> positionCategoryMap = Optional.ofNullable(positionSelectorItemList)
                .map(PositionSelectorItemList::getPositionCategory)
                .orElse(Collections.emptyList())
                .stream()
                .filter(item -> item != null && item.getKey() != null)
                .collect(Collectors.toMap(OptionalItem::getKey, OptionalItem::getValue));

        // 获取结果列表
        List<PositionInspectionItem> records = resultPage.getRecords();

        // 处理owner字段，替换为英文名字+(domain Name)格式
        if (records != null && !records.isEmpty()) {
            List<String> domainNames = records.stream().map(PositionInspectionItem::getOwner)
                    .filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
            List<IntlRmsUserDto> userDtos = intlRmsUserService.getIntlRmsUserByDomainNames(domainNames);
            Map<String, IntlRmsUserDto> userDtoMapByDomainName = userDtos.stream()
                    .collect(Collectors.toMap(IntlRmsUserDto::getDomainName, Function.identity(), (v1, v2) -> v1));

            for (PositionInspectionItem item : records) {
                item.setPositionCategory(convertPositionCategory(item.getPositionCategory(), positionCategoryMap));
                item.setPositonLocation(
                        Optional.ofNullable(positionLocationMap.get(item.getPositonLocation()))
                                .orElse(item.getPositonLocation()));
                item.setDisplayStandardization(
                        Optional.ofNullable(displayStandardizationMap.get(item.getDisplayStandardization()))
                                .orElse(item.getDisplayStandardization()));
                IntlRmsUserDto userDto = userDtoMapByDomainName.get(item.getOwner());
                if (userDto != null) {
                    item.setOwner(userDto.getEnglishName() + " (" + userDto.getDomainName() + ")");
                }
                if (request.getLatitude() != null && request.getLongitude() != null) {
                    Double userLatitude = request.getLatitude();
                    Double userLongitude = request.getLongitude();
                    if (item.getPositionLatitude() != null && item.getPositionLongitude() != null) {
                        double distance = com.mi.info.intl.retail.org.domain.CalculateSphericalDistance.getDistance(
                                userLatitude, userLongitude, item.getPositionLatitude(), item.getPositionLongitude());
                        item.setDistance(distance);
                    } else {
                        item.setDistance(Double.MAX_VALUE);
                    }
                }
            }
            //按照distance 先后顺序排序
            if (request.getLatitude() != null && request.getLongitude() != null) {
                records.sort(java.util.Comparator.comparingDouble(PositionInspectionItem::getDistance));
            }
        }
        // 构建分页响应对象
        return new PageResponse<>(resultPage.getTotal(), request.getPageNum(), request.getPageSize(), records);
    }

    /**
     * PC 审批
     */
    @Override
    public CommonApiResponse<String> approvePositionInspection(PositionInspectionApproveRequest request) {
        log.info("审批阵地巡检: {}", request);
        String result = positionInspectionDomainService.approvePositionInspection(request);
        return new CommonApiResponse<>(result);
    }

    /**
     * 阵地信息提交
     * APP
     */
    @Override
    public CommonApiResponse<String> submitPositionInspection(PositionInspectionSubmitRequest request) {

        return positionInspectionDomainService.submitPositionInspection(request);
    }

    @Override
    public CommonApiResponse<PositionInspectionDetailResponse> getPositionInspectionDetail(PositionInspectionDetailRequest request) {

        return positionInspectionDomainService.getPositionInspectionDetail(request);
    }

    /**
     * PC
     */
    @Override
    public CommonApiResponse<PositionInspectionAllDetailDTO> getPositionInspectionAllDetail(PositionInspectionDetailRequest request) {

        return positionInspectionDomainService.getPositionInspectionAllDetail(request);
    }

    @Override
    public CommonApiResponse<PositionSelectorItemList> getSelectorList(PositionItemListRequest request) {
        try {
            return new CommonApiResponse<>(positionInspectionDomainService.getSelectorList(request));
        } catch (Exception e) {
            log.error("getSelectorList failed", e.getMessage());
            return CommonApiResponse.failure(500, e.getMessage());
        }
    }

    /**
     * 获取巡检记录汇总数据
     *
     * @param request 阵地巡检请求DTO
     * @return 巡检记录汇总数据响应
     */
    @Override
    public InspectionSummaryDTO summary(PositionInspectionRequest request) {
        log.info("获取巡检记录汇总数据: {}", request);

        // 调用领域服务获取汇总数据
        InspectionSummaryDTO domainSummary = positionInspectionDomainService.getInspectionSummary(request);

        // 将领域DTO转换为API DTO
        InspectionSummaryDTO apiSummary = new InspectionSummaryDTO();
        if (domainSummary != null) {
            BeanUtils.copyProperties(domainSummary, apiSummary);
        }

        return apiSummary;
    }

    @Override
    public CommonApiResponse<List<PositionInspectionHistoryItem>> operationHistory(PositionInspectionHistoryRequest request) {

        List<PositionInspectionHistoryItem> historyItems = positionInspectionDomainService.operationHistory(request.getPositionInspectionId());
        return new CommonApiResponse<>(historyItems);
    }

    /**
     * 标记任务为无需完成
     *
     * @param req 任务中心完成任务请求
     * @return 操作结果
     */
    @Override
    public CommonApiResponse<String> noNeedCompleteTask(TaskCenterFinishTaskReq req) {
        log.info("标记阵地巡检任务为无需完成: {}", req);
        String result = positionInspectionDomainService.noNeedCompleteTask(req);
        return new CommonApiResponse<>(result);
    }

    /**
     * 完成用户当前任务动作
     *
     * @param req 任务中心完成任务请求
     * @return 操作结果
     */
    @Override
    public CommonApiResponse<String> outerTaskFinish(TaskCenterFinishTaskReq req) {
        log.info("完成用户当前任务动作: {}", req);
        String result = positionInspectionDomainService.outerTaskFinish(req);
        return new CommonApiResponse<>(result);
    }


    /**
     * 根据阵地代码获取负责人信息
     *
     * @param positionCode 阵地代码
     * @return 负责人信息
     */
    @Override
    public CommonApiResponse<PositionInspectionResponsiblePersonDTO> getResponsiblePersonByPositionCode(String positionCode) {
        log.info("根据阵地代码获取负责人信息: positionCode={}", positionCode);

        // 调用领域服务获取负责人信息
        PositionInspectionResponsiblePersonDTO responsiblePerson = positionInspectionDomainService.getResponsiblePersonByPositionCode(positionCode);

        return new CommonApiResponse<>(responsiblePerson);
    }


    /**
     * 根据阵地编码创建巡检记录
     *
     * @param positionCode 阵地编码
     * @param operatorId   操作人ID
     * @return 创建结果响应
     */
    @Override
    public CommonApiResponse<String> createInspectionByPositionCode(String areaId, String positionCode, String operatorId) {

        log.info("根据阵地编码创建巡检记录: areaId={}, positionCode={}, operatorId={}", areaId, positionCode, operatorId);

        try {
            String result = positionInspectionDomainService.createInspectionByPositionCode(areaId, positionCode, operatorId);
            return new CommonApiResponse<>(result);
        } catch (Exception e) {
            log.error("创建阵地巡检记录异常", e);
            return new CommonApiResponse<>(500, "创建阵地巡检记录失败: " + e.getMessage(), null);
        }
    }

    @Override
    public CommonApiResponse<List<String>> initInspectionByPositionCode(List<InitInspectionByPositionCodeRequest> requestList) {
        //开始循环调用dubbo服务接口
        try {
            List<String> result = new ArrayList<>();
            for (InitInspectionByPositionCodeRequest request : requestList) {
                log.info("根据阵地编码创建巡检记录: countryId={}, positionCode={}, actionType={}, actionTime={}",
                        request.getCountryId(), request.getPositionCode(), request.getActionType(), request.getActionTime());
                String res = positionInspectionDomainService.initInspectionByPositionCode(request);
                result.add(res);
            }
            return new CommonApiResponse<>(result);

        } catch (Exception e) {
            log.error("创建阵地巡检记录异常", e);
            return new CommonApiResponse<>(500, "创建阵地巡检记录失败: " + e.getMessage(), null);
        }
    }

    @Override
    public CommonApiResponse<List<String>> initInspectionFileDownload(String url) {
        log.info("初始化巡检文件下载: url={}", url);

        Path tempFile = null;
        List<InitInspectionByPositionCodeRequest> requestList = new ArrayList<>();
        try {
            // 1. 下载文件到临时目录
            tempFile = Files.createTempFile("download-", ".xlsx");
            try (InputStream in = new URL(url).openStream();
                 OutputStream out = Files.newOutputStream(tempFile)) {
                byte[] buffer = new byte[8192];
                int len;
                while ((len = in.read(buffer)) != -1) {
                    out.write(buffer, 0, len);
                }
            }

            // 2. 使用EasyExcel读取Excel内容并组装DTO列表
            try (InputStream excelIn = Files.newInputStream(tempFile)) {
                List<InitInspectionByPositionCodeRequest> excelList = EasyExcel.read(excelIn)
                    .head(InitInspectionByPositionCodeRequest.class)
                    .sheet()
                    .doReadSync();
                // 跳过表头（EasyExcel会自动处理），直接加入到requestList
                requestList.addAll(excelList);
            }
        } catch (Exception e) {
            log.error("下载或读取Excel文件失败", e);
            return new CommonApiResponse<>(500, "文件处理失败: " + e.getMessage(), null);
        } finally {
            if (tempFile != null) {
                try { Files.deleteIfExists(tempFile); } catch (IOException ignore) { }
            }
        }

        // 3. 调用服务
        return initInspectionByPositionCode(requestList);
    }

    /**
     * 阵地巡检任务下发
     * 流程：1.获取当前时间为凌晨0点的国家列表
     * 2.获取这些国家未下发的巡检任务
     * 3.获取门店负责人并更新巡检任务
     * 4.下发任务
     *
     * @return 处理结果
     */
    @Override
    public CommonApiResponse<String> dispatchInspectionTasks(Integer regularTime) {
        log.info("接收到阵地巡检任务下发请求");
        return positionInspectionDomainService.dispatchInspectionTasks(regularTime);
    }

    @Override
    public CommonApiResponse<List<OptionalItem<Integer>>> getAbnormalReason() {
        return new CommonApiResponse<>(I18nDesc.toOptionalItems(AbnormalReasonEnum.class));
    }

    /**
     * 根据业务代码获取照片链接JSON，提供dubbo服务
     *
     * @param businessCode 业务代码
     * @return 包含照片链接的JSON字符串响应
     */
    @Override
    public CommonApiResponse<List<ImageCenterDto>> getPhotosByBusinessCode(String businessCode) {
        log.info("获取阵地照片信息: businessCode={}", businessCode);
        if (businessCode == null || businessCode.trim().isEmpty()) {
            // 修正泛型推断问题，显式指定泛型类型
            return new CommonApiResponse<>(500, "业务代码不能为空", null);
        }

        // 调用领域服务获取InspectionRecordDomain列表
        List<InspectionRecordDomain> inspectionRecords = positionInspectionDomainService.getInspectionRecordsByBusinessCode(businessCode);
        if (CollectionUtils.isEmpty(inspectionRecords)) {
            // 修正泛型推断问题，显式指定泛型类型，并返回null而不是"{}"
            return new CommonApiResponse<>(500, "无对应巡检记录", null);
        }
        // 返回图片集合Json
        List<ImageCenterDto> result = positionInspectionDomainService.getImageCenterData(inspectionRecords);
        return new CommonApiResponse<>(result);
    }

    /**
     * 查询阵地家具列表
     *
     * @param request 阵地家具查询请求参数
     * @return 阵地家具列表响应
     */
    @Override
    public CommonApiResponse<List<OptionalItem<Integer>>> getPositionFurnitureList(PositionFurnitureRequest request) {

        // 调用领域服务查询阵地家具列表
        List<OptionalItem<Integer>> furnitureList = positionInspectionDomainService.getPositionFurnitureList(request);

        return new CommonApiResponse<>(furnitureList);
    }

    @Override
    public CommonApiResponse<String> taskReminder() {
        log.info("执行阵地巡检定时任务提醒");
        // 调用领域服务
        CommonApiResponse<String> result = positionInspectionDomainService.taskReminder();
        return result;
    }

    @Override
    public CommonApiResponse<Void> batchUpload(PositionImgBatchUploadRequest request) {
        log.info("batch upload request: {}", request);
        try {
            positionInspectionDomainService.batchUpload(request);
            return new CommonApiResponse<>(null);
        } catch (Exception e) {
            log.error("batchUpload failed", e);
            return CommonApiResponse.failure(500, e.getMessage());
        }
    }

    /**
     * 获取巡检记录汇总数据网关 (PC专用)
     *
     * @param request 请求参数
     * @return 巡检记录汇总数据响应
     */
    @Override
    public CommonApiResponse<InspectionSummaryDTO> summaryGateWay(PositionInspectionRequest request) {
        log.info("获取巡检记录汇总数据网关: {}", request);

        // 调用原有的summary方法获取数据
        InspectionSummaryDTO summary = this.summary(request);

        return new CommonApiResponse<>(summary);
    }

    /**
     * 分页查询阵地巡检信息（PC专用）
     *
     * @param request 阵地巡检请求DTO
     * @return 阵地巡检信息分页响应
     */
    @Override
    public CommonApiResponse<PageResponse<PositionInspectionItemDTO>> listPositionInspectionGateWay(
            PositionInspectionRequest request) {
        //获取对应的时区
        PageResponse<PositionInspectionItem> pageResponse = this.listPositionInspection(request);
        return new CommonApiResponse<>(BeanConverter.INSTANCE.map(pageResponse));

    }

    @Override
    public CommonApiResponse<PositionInspectionExportResponse> exportPositionInspection(PositionInspectionRequest request) {
        //方便miapi调试，给heraContext兜底
        String areaId = Optional.ofNullable(RpcContext.getContext().getAttachment("$area_id")).orElse("ID");
        HeraContextKeyValueHolder.setAreaForDev(areaId);

        log.info("导出阵地巡检数据: {}", request);

        File tempFile = null;
        ExcelWriter excelWriter = null;

        try {
            // 创建临时文件
            String timestamp = DateUtil.format(DateUtil.date(), "yyyyMMddHHmmss");
            String fileName = "position_inspection_" + timestamp + ".xlsx";
            tempFile = File.createTempFile("position_inspection_", ".xlsx");

            // 创建Excel写入器
            excelWriter = EasyExcel.write(tempFile, PositionInspectionItem.class)
                    .registerWriteHandler(new ExportExcelAspect.AdaptiveWidthStyleStrategy())
                    .build();

            WriteSheet writeSheet = EasyExcel.writerSheet("阵地巡检数据").build();

            // 设置分页参数，每次查询1000条数据
            long pageSize = 1000L;
            long currentPage = 1L;
            boolean hasNext = true;

            // 循环查询所有数据
            while (hasNext) {
                request.setPageNum(currentPage);
                request.setPageSize(pageSize);

                // 调用分页查询方法
                PageResponse<PositionInspectionItem> resultPage = this.listPositionInspection(request);
                List<PositionInspectionItem> records = resultPage.getList();

                if (CollUtil.isEmpty(records)) {
                    hasNext = false;
                    continue;
                }

                // 写入数据到Excel
                excelWriter.write(records, writeSheet);

                // 判断是否还有下一页
                hasNext = currentPage * pageSize < resultPage.getTotalCount();
                hasNext = currentPage * pageSize < resultPage.getTotalCount();
                currentPage++;
            }

            // 完成写入并关闭
            if (excelWriter != null) {
                excelWriter.finish();
            }

            // 上传到FDS
            String objectName = "position_inspection/" + UUID.randomUUID().toString() + "/" + fileName;
            String downloadUrl = fdsService.upload(objectName, tempFile, true).getUrl();

            // 构建返回结果
            PositionInspectionExportResponse response = new PositionInspectionExportResponse(downloadUrl);
            return new CommonApiResponse<>(response);

        } catch (Exception e) {
            log.error("导出阵地巡检数据异常", e);
            return new CommonApiResponse<>(500, "导出阵地巡检数据失败: " + e.getMessage(), null);
        } finally {
            // 关闭Excel写入器
            if (excelWriter != null) {
                excelWriter.finish();
            }

            // 删除临时文件
            if (tempFile != null && tempFile.exists() && !tempFile.delete()) {
                log.warn("临时文件删除失败: {}", tempFile.getAbsolutePath());
            }
        }
    }

    /**
     * 将positionCategory的JSON字符串转换为映射后的字符串
     *
     * @param positionCategory    原始positionCategory JSON字符串
     * @param positionCategoryMap 映射表
     * @return 映射后的字符串
     */
    private String convertPositionCategory(String positionCategory, Map<String, String> positionCategoryMap) {
        if (positionCategory == null || positionCategory.isEmpty()) {
            return positionCategory;
        }
        ObjectMapper mapper = new ObjectMapper();
        List<String> categoryList = new ArrayList<>();
        try {
            categoryList = mapper.readValue(positionCategory, new TypeReference<List<String>>() {
            });
        } catch (Exception e) {
            log.error("positionCategory 转换失败: {}", positionCategory, e);
        }
        List<String> convertedCategories = categoryList.stream()
                .map(category -> Optional.ofNullable(positionCategoryMap.get(category)).orElse(category))
                .collect(Collectors.toList());
        return String.join(",", convertedCategories);
    }
}