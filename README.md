 # intl-retail

International Retail 

## Modules

### API 定义，dto, service 定义
- `intl-retail-api` 对外 API 定义。特别重要，对外接口定义，一定要规范，稳定。

### Web/dubbo Service
- `intl-retail-server` 服务启动

### 基础包
- `intl-retail-core` 核心配置，中间件服务
- `intl-retail-common` 通用的工具类、常量等。不联网工具。

### 业务领域服务
_暂定，不时调整。 注意，系统功能与业务领域不一定一对一，一系统可支持多领域。_
- `intl-retail-fieldforce` 人力领域（人主数据，入转调离，考勤打卡，请假排班，巡店计划，人店关系，HC/招需，离职管理，成长体系，薪资/激励等）
- `intl-retail-front` 阵地领域（门店/阵地主数据、标签属性，商店关系，家具物料，样机，店内检查，陈列检查，经营管理，竞品分析，活动管理，费用，地址，销售目标，客流等）
- `intl-retail-sales` 销售/库存领域（销量上报，库存上报，收货，新品管理等）
- `intl-retail-cooperation` 协同（账号登录，菜单权限，组织结构，任务管理，审批，产品主数据，类目、价格，消息中心，隐私政策等）
- `intl-retail-training（先放人力领域）` 学练考（知识库，培训，考试，店内培训，直播，AI Copilot，扫码签到）

### 以下模块主要负责 RN 化接口转发、及外部服务透传
_结构暂时保留原结构。后续逐步把代码迁移到其他模块_
- `intl-retail-proxy` RN 接口转发，pilot 接口透传，大脑接口透传等。
  - `intl-retail-app`
  - `intl-retail-domain`
  - `intl-retail-infra`

### Q&A
#### Q1: 新引入的jar包无法正常下载？
- 使用该文档的setting.xml https://xiaomi.f.mioffice.cn/wiki/wikk42L4FAlZdrbn4IBluJxSi7g



