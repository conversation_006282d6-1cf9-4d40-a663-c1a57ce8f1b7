<?xml version="1.0" encoding="UTF-8"?>

<configuration>
    <include resource="org/springframework/boot/logging/logback/defaults.xml" />
    <include resource="org/springframework/boot/logging/logback/console-appender.xml" />
    <springProperty scope="context" name="LOG_NAME" source="spring.application.name" defaultValue=""/>

    <!-- 日志文件路径，不加/代表使用相对路径，本地开发可以这样设置，但是线上必须设置绝对路径 -->
    <springProfile name="dev, default">
        <property name="LOG_HOME" value="logs"/>
    </springProfile>

    <springProfile name="!(dev | default)">
       <property name="LOG_HOME" value="/home/<USER>/log"/>
    </springProfile>

<!-- 日志保留天数 -->
    <property name="MAX_HISTORY" value="10"/>
    <!-- 所有存档日志最大 size, 超过则删除旧的存档 -->
    <property name="TOTAL_SIZE_CAP" value="20GB"/>
    <!-- 滚动日志单个文件最大 size -->
    <property name="MAX_FILE_SIZE" value="100MB"/>
    <!-- 文件日志输出模式 -->
    <property name="LOG_PATTERN"
              value="%d{yyyy-MM-dd HH:mm:ss.SSS}|%-5level|%X{trace_id}|%15.15t|%-40.40logger{39}|%m%n%xException}"/>

    <!-- 操作索引按照每天生成日志文件 -->
    <appender name="TRACE_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_HOME}/${LOG_NAME}/info.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_HOME}/${LOG_NAME}/info-%d{yyyy-MM-dd}-%i.log.gz</fileNamePattern>
            <maxFileSize>${MAX_FILE_SIZE}</maxFileSize>
            <maxHistory>${MAX_HISTORY}</maxHistory>
            <totalSizeCap>${TOTAL_SIZE_CAP}</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>${LOG_PATTERN}</pattern>
        </encoder>
    </appender>

    <!-- 错误日志记录，将其和正常的日志文件分开，只是为了更容易找到错误的信息。在正常的日志文件中也会有错误的日志记录。 -->
    <appender name="ERROR_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!-- 只打印错误日志 -->
        <file>${LOG_HOME}/${LOG_NAME}/error.log</file>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>ERROR</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <FileNamePattern>
                ${LOG_HOME}/${LOG_NAME}/error-%d{yyyy-MM-dd}-%i.log.gz
            </FileNamePattern>
            <maxFileSize>${MAX_FILE_SIZE}</maxFileSize>
            <maxHistory>${MAX_HISTORY}</maxHistory>
            <totalSizeCap>${TOTAL_SIZE_CAP}</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>${LOG_PATTERN}</pattern>
        </encoder>
    </appender>

    <logger name="org.apache.dubbo" level="WARN" />
    <logger name="org.apache.ibatis" level="WARN" />

    <root level="INFO" additivity="false">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="TRACE_FILE"/>
        <appender-ref ref="ERROR_FILE"/>
    </root>


</configuration>

