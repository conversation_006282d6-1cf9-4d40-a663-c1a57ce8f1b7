package com.mi.info.intl.retail.http.http;

import cn.hutool.core.lang.Assert;
import com.mi.info.intl.retail.http.exception.HttpClientException;
import com.mi.info.intl.retail.http.utils.Utils;
import okhttp3.MediaType;
import okhttp3.RequestBody;

import java.io.File;
import java.io.InputStream;

/**
 * 文件包装类
 *
 * <AUTHOR> on 2016/12/9.
 */
public class FileWrapper {

    private InputStream content;
    private File file;
    private String filename;

    private MediaType mediaType;

    FileWrapper(Builder builder) {
        this.mediaType = builder.mediaType;
        this.file = builder.file;
        this.filename = builder.filename;
        this.content = builder.content;
    }

    public String getFilename() {
        return filename;
    }

    public static Builder create() {
        return new Builder();
    }

    public RequestBody requestBody() {
        if (this.file != null) {
            return new FileRequestBody(this.file, this.mediaType);
        }
        return new InputStreamRequestBody(this.content, this.mediaType);
    }

    public static class Builder {

        private InputStream content;

        private File file;
        private String filename;

        private MediaType mediaType;

        public Builder file(File file) {
            Assert.notNull(file, "File may be null.");
            if (!file.exists()) {
                throw new HttpClientException("File does not exist.");
            }
            this.file = file;
            return this;
        }

        public Builder filename(String filename) {
            Assert.notBlank(filename, "Filename may be null.");
            this.filename = filename;
            return this;
        }

        public Builder stream(InputStream stream) {
            Assert.notNull(stream, "Stream may be null.");
            this.content = stream;
            return this;
        }

        public Builder contentType(String contentType) {
            Assert.notBlank(contentType, "ContentType may be null.");
            this.mediaType = MediaType.parse(contentType);
            return this;
        }

        public Builder mediaType(MediaType mediaType) {
            Assert.notNull(mediaType, "Media may be null.");
            this.mediaType = mediaType;
            return this;
        }

        public FileWrapper build() {
            if (this.file != null) {
                if (this.filename == null) {
                    this.filename = file.getName();
                }
            } else if (this.content != null) {
                if (this.filename == null) {
                    throw new HttpClientException("Filename may  be null");
                }
            } else {
                throw new HttpClientException("The content is null.");
            }
            if (this.mediaType == null) {
                this.mediaType = Utils.guessMediaType(this.filename);
            }
            return new FileWrapper(this);
        }

    }
}
