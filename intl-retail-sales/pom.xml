<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.mi.info.intl.retail</groupId>
        <artifactId>intl-retail</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>intl-retail-sales</artifactId>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>
    <dependencies>
        <dependency>
            <groupId>com.mi.info.intl.retail</groupId>
            <artifactId>intl-retail-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.mi.info.intl.retail</groupId>
            <artifactId>intl-retail-inner-api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.mi.info.intl.retail</groupId>
            <artifactId>intl-retail-core</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.mi.info.intl.retail</groupId>
            <artifactId>intl-retail-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.mi.info.intl.retail</groupId>
            <artifactId>intl-retail-domain</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-elasticsearch</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.rocketmq</groupId>
            <artifactId>rocketmq-spring-boot</artifactId>
            <version>2.2.0-mdh2.2.7-RELEASE</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <artifactId>dubbo-auth</artifactId>
            <groupId>com.xiaomi.newretail</groupId>
        </dependency>
        <dependency>
            <artifactId>nr-upload-client</artifactId>
            <groupId>com.xiaomi.nr</groupId>
        </dependency>
        <dependency>
            <groupId>com.mi.info.intl.retail</groupId>
            <artifactId>intl-retail-infra</artifactId>
        </dependency>
        <dependency>
            <groupId>run.mone</groupId>
            <artifactId>hera-metrics-sdk</artifactId>
        </dependency>

    </dependencies>

</project>