package com.mi.info.intl.retail.intlretail.service.app.store.impl.mq;

import com.mi.info.intl.retail.intlretail.domain.common.utils.JsonUtil;
import com.mi.info.intl.retail.intlretail.domain.http.rms.RmsAppliUserOauthServiceProvider;
import com.mi.info.intl.retail.intlretail.service.api.position.PositionInspectionService;
import com.mi.info.intl.retail.intlretail.service.api.store.dto.PositionMessage;
import com.mi.info.intl.retail.intlretail.service.api.store.dto.RmsRequest;
import com.mi.info.intl.retail.intlretail.app.config.ConsumerCountryConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.NullArgumentException;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Slf4j
@Service
@ConditionalOnProperty(name = "intl-retail.rocketmq.enabled", havingValue = "true", matchIfMissing = true)
@RocketMQMessageListener(topic = "${intl-retail.rocketmq.position.topic}", consumerGroup = "${intl-retail.rocketmq.position.group}")
public class PositionConsumer implements RocketMQListener<String> {

    static final String SAVE_POSITION_PATH = "/api/data/v9.2/new_SavePositionForChannelRetail";

    @Autowired
    private RmsAppliUserOauthServiceProvider rmsAppliUserOauthServiceProvider;

    @Autowired
    private PositionInspectionService positionInspectionService;

    @Autowired
    private ConsumerCountryConfig consumerCountryConfig;

    //建设阶段变化的字段常量
    private static final String CONSTRUCTION_PHASE = "constructionPhase";

    @Override
    public void onMessage(String message) {
        //constructionPhase
        log.info("PositionConsumer message:{}", message);
        try {
            // 将 message 转换为 PositionMessage 对象
            PositionMessage positionMessage = Optional.ofNullable(JsonUtil.json2bean(message, PositionMessage.class))
                    .orElseThrow(() -> new NullArgumentException("positionMessage is null"));

            // 获取 areaId 并检查是否为 null 或空字符串
            String areaId = Optional.ofNullable(positionMessage.getAreaId())
                    .orElseThrow(() -> new NullArgumentException("areaId is null or areaId is empty"));

            // 因新零售和渠道零售某些国家的部署机房有差异，导致数字门店这些国家会在新加披和欧洲同时发消息
            // 为了避免可能的数据覆盖问题发生，不符合渠道零售机房部署的国家不在对应的机房消费消息
            List<String> skippedCountries = consumerCountryConfig.getSkippedCountryList();
            log.debug("skippedCountries is {}", skippedCountries);
            if (!skippedCountries.isEmpty() && skippedCountries.contains(areaId)) {
                log.info("PositionConsumer skipped country is {}", areaId);
                return;
            }

            // 只有positionMessage中的changeField 包含 constructionPhase 且 areaId在配置列表中才会处理
            if (positionMessage.getChangeField() != null && positionMessage.getChangeField().contains(CONSTRUCTION_PHASE)) {
                // 获取配置的国家列表
                List<String> allowedCountries = consumerCountryConfig.getConsumerCountryList();
                
                // 如果配置列表为空，则允许所有国家；否则检查 areaId 是否在允许列表中
                if (allowedCountries.isEmpty() || allowedCountries.contains(areaId)) {
                    // 处理阵地巡检创建
                    positionInspectionService.createInspectionByPositionCode(areaId, positionMessage.getPositionCode(),
                            "0");
                    log.info("PositionConsumer: 处理阵地巡检创建，areaId: {}, positionCode: {}", areaId, positionMessage.getPositionCode());
                } else {
                    log.info("PositionConsumer: 跳过处理，areaId: {} 不在允许的国家列表中", areaId);
                }
            }
            RmsRequest rmsRequest = new RmsRequest();
            rmsRequest.setInput(message);
            rmsAppliUserOauthServiceProvider.httpForObject(areaId, SAVE_POSITION_PATH, rmsRequest, "POST",
                    String.class);
            
        } catch (Exception e) {
            throw new RuntimeException("PositionConsumer.onMessage error", e);
        }
    }
}