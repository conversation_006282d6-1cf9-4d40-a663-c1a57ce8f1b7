package com.mi.info.intl.retail.cooperation.task.inspection.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.info.intl.retail.component.ComponentLocator;
import com.mi.info.intl.retail.cooperation.task.dto.IntlInspectionTaskQuery;
import com.mi.info.intl.retail.cooperation.task.dto.dto.InspectionTaskConfDTO;
import com.mi.info.intl.retail.cooperation.task.infra.entity.IntlBigPromotionConf;
import com.mi.info.intl.retail.cooperation.task.infra.entity.IntlInspectionTaskConf;
import com.mi.info.intl.retail.cooperation.task.infra.mapper.read.IntlInspectionTaskConfReadMapper;
import com.mi.info.intl.retail.cooperation.task.inspection.BigPromotionConfigService;
import com.mi.info.intl.retail.cooperation.task.inspection.IntlInspectionTaskConfService;
import com.mi.info.intl.retail.utils.time.DateTimeUtil;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/6/5
 **/
@Service
public class IntlInspectionTaskConfServiceImpl
        extends ServiceImpl<IntlInspectionTaskConfReadMapper, IntlInspectionTaskConf>
        implements IntlInspectionTaskConfService {
    
    @Resource
    private BigPromotionConfigService bigPromotionConfigService;
    
    @Override
    public IPage<InspectionTaskConfDTO> pageList(IntlInspectionTaskQuery query) {
        Page<IntlInspectionTaskConf> page = new Page<>(query.getPageNum(), query.getPageSize());
        Page<IntlInspectionTaskConf> taskConfPage = this.page(page, Wrappers.<IntlInspectionTaskConf>lambdaQuery()
                .in(CollUtil.isNotEmpty(query.getRegionList()), IntlInspectionTaskConf::getRegionCode, query.getRegionList())
                .in(CollUtil.isNotEmpty(query.getCountryList()), IntlInspectionTaskConf::getCountryCode, query.getCountryList())
                .orderByDesc(IntlInspectionTaskConf::getId));
        Page<InspectionTaskConfDTO> pageDTO = new Page<>(query.getPageNum(), query.getPageSize());
        pageDTO.setCurrent(taskConfPage.getCurrent());
        pageDTO.setSize(taskConfPage.getSize());
        
        if (CollUtil.isEmpty(taskConfPage.getRecords())) {
            return pageDTO;
        }
        List<IntlInspectionTaskConf> intlInspectionTaskConfList = taskConfPage.getRecords();
        Set<String> countryCodeList = intlInspectionTaskConfList.stream().map(IntlInspectionTaskConf::getCountryCode)
                .collect(Collectors.toSet());
        Map<String, Boolean> hasBigPromotionMap = bigPromotionConfigService.hasBigPromotionTaskConfig(
                new ArrayList<>(countryCodeList));
        List<String> countryCodes = intlInspectionTaskConfList.stream().map(IntlInspectionTaskConf::getCountryCode).collect(Collectors.toList());
        // 获取大促配置，状态为0（未停用），且结束时间大于当前时间（未过期）
        List<IntlBigPromotionConf> promotionConfList = bigPromotionConfigService.list(
                Wrappers.<IntlBigPromotionConf>lambdaQuery()
                        .in(IntlBigPromotionConf::getCountry, countryCodes)
                        .eq(IntlBigPromotionConf::getIsDisabled, 0)
                        .ge(IntlBigPromotionConf::getEndTime, DateTimeUtil.formatLocalDateTimeWithCustomTime(
                                LocalDateTime.now(), "23:59:59"))
        );

        Map<String, IntlBigPromotionConf> promotionConfMap = promotionConfList.stream().collect(Collectors.toMap(IntlBigPromotionConf::getCountry,
                Function.identity(), (v1, v2) -> v1));

        List<InspectionTaskConfDTO> inspectionTaskConfDTOS = intlInspectionTaskConfList.stream().map(item -> {
            InspectionTaskConfDTO inspectionTaskConfDTO = ComponentLocator.getConverter()
                    .convert(item, InspectionTaskConfDTO.class);
            inspectionTaskConfDTO.setIsNewProductPromotion(hasBigPromotionMap.getOrDefault(item.getCountryCode(), false));
            IntlBigPromotionConf promotionConf = promotionConfMap.getOrDefault(item.getCountryCode(), new IntlBigPromotionConf());
            inspectionTaskConfDTO.setPromotionStartTime(promotionConf.getStartTime());
            inspectionTaskConfDTO.setPromotionEndTime(promotionConf.getEndTime());
            inspectionTaskConfDTO.setCreateTime(DateTimeUtil.timestampToDate(Long.valueOf(item.getCreatedAt())));
            inspectionTaskConfDTO.setUpdateTime(DateTimeUtil.timestampToDate(Long.valueOf(item.getUpdatedAt())));
            return inspectionTaskConfDTO;
        }).collect(Collectors.toList());
        pageDTO.setPages(taskConfPage.getPages());
        pageDTO.setTotal(taskConfPage.getTotal());
        pageDTO.setRecords(inspectionTaskConfDTOS);
        
        return pageDTO;
    }
}
