package com.mi.info.intl.retail.so.infra.database.mapper.upload;

import com.mi.info.intl.retail.so.domain.datasync.dto.IntlSoQtyBatchSaveData;
import com.mi.info.intl.retail.so.domain.upload.entity.IntlSoQty;
import com.mi.info.intl.retail.intlretail.service.api.so.dto.GetSkuListResponse;
import com.mi.info.intl.retail.intlretail.service.api.so.dto.QueryQtyListResponse;
import com.mi.info.intl.retail.intlretail.service.api.so.dto.GetQtyBoardDataResponse;
import com.mi.info.intl.retail.intlretail.service.api.so.dto.QueryQtyDetailResponse;
import com.mi.info.intl.retail.intlretail.service.api.so.dto.GetStoreListResponse;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
* <AUTHOR>
* @description 针对表【intl_so_qty(销量qty)】的数据库操作Mapper
* @createDate 2025-07-24 19:21:34
* @Entity com.mi.info.intl.retail.so.infra.entity.IntlSoQty
*/
public interface IntlSoQtyMapper extends BaseMapper<IntlSoQty> {

    /**
     * 查询可售SKU列表
     *
     * @param countryCode 国家代码
     * @param productLineCode 产品线代码
     * @param shortName 产品简称
     * @param productName 产品名称
     * @param search 模糊查询关键词
     * @return 可售SKU列表
     */
    List<GetSkuListResponse.ProductInfo> getSkuList(@Param("countryCode") String countryCode,
                                                   @Param("productLineCode") Long productLineCode,
                                                   @Param("shortName") String shortName,
                                                   @Param("productName") String productName,
                                                   @Param("search") String search,
                                                   @Param("miId") Long miId,
                                                   @Param("yesterdayTimestamp") Long yesterdayTimestamp);

    /**
     * 获取用户门店列表
     *
     * @param miId 用户miId
     * @return 门店ID列表
     */
    List<String> getUserStoreList(@Param("miId") Long miId);

    /**
     * 查询所有可售产品数据 - 用于批量验证
     *
     * @return 可售产品列表，包含goods_id、sku_id、code69
     */
    List<Map<String, Object>> getAllAvailableProducts(@Param("countryCode") String countryCode);


    /**
     * 统计当前筛选条件下QTY数量汇总（SUM(qty.quantity)）
     *
     * @param countryCode 国家代码
     * @param miId 用户miId
     * @param userStoreList 用户门店列表
     * @param search 搜索关键词
     * @param reportType 上报类型
     * @param storeCode 门店代码列表
     * @param productLine 产品线列表
     * @param storeType 门店类型列表
     * @param channelType 渠道类型列表
     * @param dateFilterType 日期筛选类型
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return QTY数量汇总
     */
    Integer queryQtyQuantitySum(@Param("countryCode") String countryCode,
                              @Param("miId") Long miId,
                              @Param("userStoreList") List<String> userStoreList,
                              @Param("search") String search,
                              @Param("reportType") Integer reportType,
                              @Param("storeCode") List<String> storeCode,
                              @Param("productLine") List<String> productLine,
                              @Param("storeType") List<Integer> storeType,
                              @Param("channelType") List<Integer> channelType,
                              @Param("dateFilterType") String dateFilterType,
                              @Param("startTime") String startTime,
                              @Param("endTime") String endTime);

    /**
     * 统计QTY数据（分组查询，一次查询获取总数、PC数量、APP数量）
     *
     * @param countryCode 国家代码
     * @param miId 用户miId
     * @param userStoreList 用户门店列表
     * @param search 搜索关键词
     * @param reportType 上报类型
     * @param storeCode 门店代码列表
     * @param productLine 产品线列表
     * @param storeType 门店类型列表
     * @param channelType 渠道类型列表
     * @param dateFilterType 日期筛选类型
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 统计结果Map，包含totalCount、pcCount、appCount
     */
    Map<String, Object> queryQtyStatisticsGroup(@Param("countryCode") String countryCode,
                                              @Param("miId") Long miId,
                                              @Param("userStoreList") List<String> userStoreList,
                                              @Param("search") String search,
                                              @Param("reportType") Integer reportType,
                                              @Param("storeCode") List<String> storeCode,
                                              @Param("productLine") List<String> productLine,
                                              @Param("storeType") List<Integer> storeType,
                                              @Param("channelType") List<Integer> channelType,
                                              @Param("dateFilterType") String dateFilterType,
                                              @Param("startTime") String startTime,
                                              @Param("endTime") String endTime);


    /**
     * 查询QTY明细页数据
     *
     * @param qtyId QTY记录ID
     * @return QTY明细
     */
    QueryQtyDetailResponse queryQtyDetail(@Param("qtyId") String qtyId
                                      );

    /**
     * 查询QTY明细的图片列表
     *
     * @param qtyId QTY记录ID
     * @return 图片列表
     */
    List<QueryQtyDetailResponse.PhotoInfo> queryQtyPhotoList(@Param("qtyId") String qtyId);

    /**
     * 查询QTY销售时间戳
     *
     * @param qtyId QTY记录ID
     * @param miId 用户miId
     * @return 销售时间戳
     */
    Long queryQtySalesTime(@Param("qtyId") String qtyId, @Param("miId") Long miId);

    /**
     * 分页查询QTY列表
     *
     * @param countryCode 国家代码
     * @param miId 用户miId
     * @param userStoreList 用户门店列表
     * @param search 搜索关键词
     * @param reportType 上报类型
     * @param storeCode 门店代码列表
     * @param productLine 产品线列表
     * @param storeType 门店类型列表
     * @param channelType 渠道类型列表
     * @param dateFilterType 日期筛选类型
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param offset 偏移量
     * @param limit 限制数量
     * @return QTY明细列表
     */
    List<Map<String, Object>> queryQtyListWithPage(@Param("countryCode") String countryCode,
                                                   @Param("miId") Long miId,
                                                   @Param("userStoreList") List<String> userStoreList,
                                                   @Param("search") String search,
                                                   @Param("reportType") Integer reportType,
                                                   @Param("storeCode") List<String> storeCode,
                                                   @Param("productLine") List<String> productLine,
                                                   @Param("storeType") List<Integer> storeType,
                                                   @Param("channelType") List<Integer> channelType,
                                                   @Param("dateFilterType") String dateFilterType,
                                                   @Param("startTime") String startTime,
                                                   @Param("endTime") String endTime,
                                                   @Param("offset") Integer offset,
                                                   @Param("limit") Integer limit);

    /**
     * 获取门店列表
     *
     * @param miId 用户miId
     * @param search 搜索关键词
     * @return 门店列表
     */
    List<GetStoreListResponse.StoreInfo> getStoreList(@Param("miId") Long miId,
                                                     @Param("search") String search);

    // 在IntlSoQtyMapper接口中
    void batchInsert(@Param("list") List<IntlSoQtyBatchSaveData> qtyList);

    /**
     * 查询QTY看板数据 - 按时间范围（支持门店列表）
     *
     * @param countryCode 国家代码
     * @param miId 用户miId
     * @param userStoreList 用户门店列表
     * @param startTime 开始时间戳
     * @param endTime 结束时间戳
     * @return 按产品线分组的QTY数据
     */
    List<Map<String, Object>> getQtyBoardDataByTimeRange(@Param("countryCode") String countryCode,
                                                         @Param("miId") Long miId,
                                                         @Param("userStoreList") List<String> userStoreList,
                                                         @Param("startTime") Long startTime,
                                                         @Param("endTime") Long endTime);

    /**
     * 查询IMEI看板数据 - 按时间范围（支持门店列表）
     *
     * @param countryCode 国家代码
     * @param miId 用户miId
     * @param userStoreList 用户门店列表
     * @param startTime 开始时间戳
     * @param endTime 结束时间戳
     * @return 按产品线分组的IMEI数据
     */
    List<Map<String, Object>> getImeiBoardDataByTimeRange(@Param("countryCode") String countryCode,
                                                          @Param("miId") Long miId,
                                                          @Param("userStoreList") List<String> userStoreList,
                                                          @Param("startTime") Long startTime,
                                                          @Param("endTime") Long endTime);
}




