package com.mi.info.intl.retail.so.domain.rule.service.impl;

import static java.util.stream.Collectors.groupingBy;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.ibatis.session.ResultHandler;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.style.column.SimpleColumnWidthStyleStrategy;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.mi.info.intl.retail.api.country.CountryTimeZoneApiService;
import com.mi.info.intl.retail.api.country.dto.CountryDTO;
import com.mi.info.intl.retail.api.fieldforce.retailer.IntlRetailerApiService;
import com.mi.info.intl.retail.api.fieldforce.retailer.dto.IntlRetailerDTO;
import com.mi.info.intl.retail.api.front.position.IntlPositionApiService;
import com.mi.info.intl.retail.api.front.position.dto.IntlPositionDTO;
import com.mi.info.intl.retail.api.so.rule.model.SoRuleRetailerModel;
import com.mi.info.intl.retail.api.so.rule.req.GetRetailerSoRuleReq;
import com.mi.info.intl.retail.api.so.rule.service.IntlRuleRetailerApiService;
import com.mi.info.intl.retail.api.so.rule.service.IntlSoRuleRetailerApiService;
import com.mi.info.intl.retail.bean.BasePage;
import com.mi.info.intl.retail.bean.PageDTO;
import com.mi.info.intl.retail.constant.CacheType;
import com.mi.info.intl.retail.core.feishu.service.SendMessageService;
import com.mi.info.intl.retail.core.utils.CacheUtils;
import com.mi.info.intl.retail.enums.SoRuleType;
import com.mi.info.intl.retail.enums.SwitchEnum;
import com.mi.info.intl.retail.exception.BizException;
import com.mi.info.intl.retail.exception.ErrorCodes;
import com.mi.info.intl.retail.intlretail.service.api.fds.FdsService;
import com.mi.info.intl.retail.intlretail.service.api.so.rule.dto.PhotoRuleDTO;
import com.mi.info.intl.retail.intlretail.service.api.so.rule.dto.SoRuleRetailerDTO;
import com.mi.info.intl.retail.intlretail.service.api.so.rule.dto.SoRuleRetailerStatisticsDTO;
import com.mi.info.intl.retail.intlretail.service.api.so.rule.request.QuerySuRuleRetailerReq;
import com.mi.info.intl.retail.so.domain.rule.bean.GetRetailerSoRuleResp;
import com.mi.info.intl.retail.so.domain.rule.constants.CommonConstants;
import com.mi.info.intl.retail.so.domain.rule.enums.SoRuleEnum;
import com.mi.info.intl.retail.so.domain.rule.service.IntlSoRuleDetailService;
import com.mi.info.intl.retail.so.domain.rule.service.IntlSoRuleRetailerService;
import com.mi.info.intl.retail.so.infra.database.dataobject.rule.IntlSoRuleDetail;
import com.mi.info.intl.retail.so.infra.database.dataobject.rule.IntlSoRuleRetailer;
import com.mi.info.intl.retail.so.infra.database.mapper.rule.IntlSoRuleRetailerMapper;
import com.mi.info.intl.retail.so.util.ConvertUtils;
import com.mi.info.intl.retail.utils.UserInfoUtil;
import com.mi.info.intl.retail.utils.time.DateTimeUtil;
import com.xiaomi.cnzone.commons.utils.DateUtils;

import cn.hutool.core.lang.Assert;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2025/7/28 09:48
 */
@Slf4j
@Service("soRuleService") // 指定bean名称
public class IntlSoRuleRetailerServiceImpl
        extends ServiceImpl<IntlSoRuleRetailerMapper, IntlSoRuleRetailer>
        implements IntlSoRuleRetailerService,
                   IntlRuleRetailerApiService,
                   IntlSoRuleRetailerApiService {

    @Resource
    private IntlPositionApiService intlPositionApiService;

    @Resource
    private IntlSoRuleDetailService intlSoRuleDetailService;

    @Resource
    private IntlRetailerApiService intlRetailerApiService;

    @Resource
    private CountryTimeZoneApiService countryTimeZoneApiService;

    @Resource
    private FdsService fdsService;

    @Resource
    private SendMessageService sendMessageService;

    @Override
    public List<IntlSoRuleRetailer> getByCountryCodeAndCategoryWithRuleId(Long ruleId, String countryCode,
        Integer category) {
        LambdaQueryWrapper<IntlSoRuleRetailer> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(IntlSoRuleRetailer::getRuleId, ruleId).eq(IntlSoRuleRetailer::getCountryCode, countryCode)
            .eq(IntlSoRuleRetailer::getCategory, category);
        return this.list(queryWrapper);
    }

    @Override
    public GetRetailerSoRuleResp getRetailerSoRule(GetRetailerSoRuleReq req) {
        GetRetailerSoRuleResp resp = getRetailerRespFromCache(req);
        // 设置图片验证规则
        List<PhotoRuleDTO> list = resp.getPhotoRuleList();
        if (CollectionUtils.isNotEmpty(list)) {
            list.stream().filter(t -> CollectionUtils.isNotEmpty(t.getUserTitleList()))
                .filter(t -> t.getUserTitleList().stream()
                    .anyMatch(item -> Objects.equals(item.getUserTitleId(), req.getUserTitle())))
                .findFirst().ifPresent(t -> {
                    resp.setImeiRequirePhoto(Objects.equals(t.getImeiRequirePhoto(), SwitchEnum.ON.getValue()));
                    resp.setQtyRequirePhoto(Objects.equals(t.getQtyRequirePhoto(), SwitchEnum.ON.getValue()));
                });
        }
        //如果此处不设置，默认为null，返回结果中不包含该字段
        resp.setRuleType(SoRuleType.SO_RULE);
        return resp;
    }

    /**
     * 从CACHE获取零售商Resp
     *
     * @param req req
     * @return {@link GetRetailerSoRuleResp }
     */
    public GetRetailerSoRuleResp getRetailerRespFromCache(GetRetailerSoRuleReq req) {
        GetRetailerSoRuleResp resp =
            CacheUtils.get(CacheType.SO_RULE_DETAIL_POSITION, req.getPositionCode(), GetRetailerSoRuleResp.class);
        if (resp != null) {
            return resp;
        }
        IntlPositionDTO intlPositionDTO = getRetailerCodeByPositionId(req);
        IntlSoRuleRetailer retailer =
            getRetailerByCode(intlPositionDTO.getRetailerCode(), SoRuleEnum.MASTER.getValue());
        IntlSoRuleDetail soRuleDetail = intlSoRuleDetailService.getById(retailer.getRuleId());
        if (soRuleDetail == null) {
            throw BizException.buildFormatException(ErrorCodes.SO_RULE_NOT_EXIST, intlPositionDTO.getRetailerCode());
        }
        resp = new GetRetailerSoRuleResp();
        resp.setRuleId(retailer.getRuleId());
        resp.setCountryCode(soRuleDetail.getCountryCode());
        resp.setRetailerCode(retailer.getRetailerCode());
        resp.setStoreCode(intlPositionDTO.getStoreCode());
        resp.setEnableImei(retailer.getImeiSwitch());
        resp.setEnableQty(retailer.getQtySwitch());
        String imeiRequirePhoto = soRuleDetail.getPhotoRuleList();
        List<PhotoRuleDTO> list = JSON.parseArray(imeiRequirePhoto, PhotoRuleDTO.class);
        resp.setPhotoRuleList(list);
        CacheUtils.put(CacheType.SO_RULE_DETAIL_POSITION, req.getPositionCode(), resp);
        return resp;
    }

    /**
     * 按职位ID获取零售商代码
     *
     * @param req req
     * @return {@link IntlPositionDTO }
     */
    public IntlPositionDTO getRetailerCodeByPositionId(GetRetailerSoRuleReq req) {
        IntlPositionDTO dto = new IntlPositionDTO();
        dto.setPositionCode(req.getPositionCode());
        Optional<IntlPositionDTO> optional = intlPositionApiService.getRetailerByPositionCode(dto);
        if (!optional.isPresent()) {
            throw BizException.buildFormatException(ErrorCodes.POSITION_NOT_EXIST, req.getPositionCode());
        }
        return optional.get();
    }

    /**
     * 通过代码获取零售商
     *
     * @param retailerCode 零售商代码
     * @param category 类别
     * @return {@link IntlSoRuleRetailer }
     */
    public IntlSoRuleRetailer getRetailerByCode(String retailerCode, int category) {
        IntlSoRuleRetailer retailer = this
            .getOne(new QueryWrapper<IntlSoRuleRetailer>().eq("retailer_code", retailerCode).eq("category", category));
        if (retailer == null) {
            throw BizException.buildFormatException(ErrorCodes.RETAILER_NOT_EXIST, retailerCode);
        }
        return retailer;
    }

    @Override
    public List<IntlSoRuleRetailer> getByCountryCodeAndRetailerCodes(String countryCode, Integer category,
        List<String> retailerCodes) {
        if (CollectionUtils.isEmpty(retailerCodes) || StringUtils.isBlank(countryCode)) {
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<IntlSoRuleRetailer> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(IntlSoRuleRetailer::getCountryCode, countryCode).eq(IntlSoRuleRetailer::getCategory, category)
            .in(IntlSoRuleRetailer::getRetailerCode, retailerCodes);
        return this.list(queryWrapper);
    }

    /**
     * 根据条件获取零售商
     *
     * @param req req
     * @return {@link IPage }<{@link SoRuleRetailerDTO }>
     */
    @Override
    public PageDTO<SoRuleRetailerDTO> getRetailerByCondition(QuerySuRuleRetailerReq req) {
        CountryDTO countryInfo = getCountryDTO(req);
        LambdaQueryWrapper<IntlSoRuleRetailer> queryWrapper = getIntlSoRuleRetailerLambdaQueryWrapper(req);

        Page<IntlSoRuleRetailer> page = new Page<>(req.getPageNum(), req.getPageSize());
        IPage<IntlSoRuleRetailer> result = this.page(page, queryWrapper);
        String timeZone = DateTimeUtil.getTimeZoneByCountryCode(UserInfoUtil.getUserContext().getLanguage());
        // 构建分页参数
        List<SoRuleRetailerDTO> collect = result.getRecords().stream()
            .map(t -> getSoRuleRetailerDTO(t, countryInfo, timeZone)).collect(Collectors.toList());

        PageDTO<SoRuleRetailerDTO> resultPage = new PageDTO<>(result.getCurrent(), result.getSize(), result.getTotal());
        resultPage.setRecords(collect);

        return resultPage;
    }

    private CountryDTO getCountryDTO(QuerySuRuleRetailerReq req) {
        Assert.notNull(req.getType(), "type is null");
        return getCountryDTO(req.getCountryCode());
    }

    private CountryDTO getCountryDTO(String countryCode) {
        Assert.notNull(countryCode, "countryCode is null");
        Optional<CountryDTO> optional = countryTimeZoneApiService.getCountryInfoByCode(countryCode);
        if (!optional.isPresent()) {
            log.error("country code {} not exist ", countryCode);
            throw BizException.buildFormatException(ErrorCodes.COUNTRY_CODE_NOT_EXIST, countryCode);
        }
        return optional.get();
    }

    /**
     * 获得SO Rule Quare Do dto
     *
     * @param t t
     * @param countryInfo 国家信息
     * @param timeZone
     * @return {@link SoRuleRetailerDTO }
     */
    private SoRuleRetailerDTO getSoRuleRetailerDTO(IntlSoRuleRetailer t, CountryDTO countryInfo, String timeZone) {
        SoRuleRetailerDTO dto = new SoRuleRetailerDTO();
        dto.setCountryName(countryInfo.getCountryName());
        dto.setRegionName(countryInfo.getArea());
        dto.setCountryCode(t.getCountryCode());
        dto.setRegionCode(t.getRegionCode());
        dto.setRetailerCode(t.getRetailerCode());
        dto.setChannelType(t.getChannelType());
        dto.setRetailerName(t.getRetailerName());
        dto.setImeiSwitch(t.getImeiSwitch());
        dto.setQtySwitch(t.getQtySwitch());
        dto.setCreateRetailerTime(t.getCreateRetailerTime());
        dto.setCreateRetailerTimeStr(DateTimeUtil.getLocalDateTimeByTimeZone(timeZone, t.getCreateRetailerTime()));
        return dto;
    }

    private LambdaQueryWrapper<IntlSoRuleRetailer> getIntlSoRuleRetailerLambdaQueryWrapper(QuerySuRuleRetailerReq req) {
        // 构造基本查询参数
        LambdaQueryWrapper<IntlSoRuleRetailer> queryWrapper = new LambdaQueryWrapper<>();
        // 查询Type为1
        if (req.getType() == 1) {
            queryWrapper.eq(IntlSoRuleRetailer::getCountryCode, req.getCountryCode())
                .eq(IntlSoRuleRetailer::getCategory, SoRuleEnum.MASTER.getValue());
        } else if (req.getType() == 2) {
            Assert.notNull(req.getId(), "id is null");
            // 查询主数据关了的记录
            queryWrapper.eq(IntlSoRuleRetailer::getRuleId, req.getId()).eq(IntlSoRuleRetailer::getCategory,
                SoRuleEnum.MASTER.getValue());
        } else if (req.getType() == 3) {
            Assert.notNull(req.getId(), "id is null");
            // 查询变更记录
            queryWrapper.eq(IntlSoRuleRetailer::getRuleId, req.getId()).eq(IntlSoRuleRetailer::getCategory,
                SoRuleEnum.COPY.getValue());
        } else {
            queryWrapper.eq(IntlSoRuleRetailer::getCategory, SoRuleEnum.MASTER.getValue());
        }
        queryWrapper
            .like(StringUtils.isNotEmpty(req.getRetailerCode()), IntlSoRuleRetailer::getRetailerCode,
                req.getRetailerCode())
            .like(StringUtils.isNotEmpty(req.getRetailerName()), IntlSoRuleRetailer::getRetailerName,
                req.getRetailerName())
            .eq(StringUtils.isNotEmpty(req.getImeiSwitch()), IntlSoRuleRetailer::getImeiSwitch, req.getImeiSwitch())
            .eq(StringUtils.isNotEmpty(req.getQtySwitch()), IntlSoRuleRetailer::getQtySwitch, req.getQtySwitch())
            .ge(req.getStartTime() != null, IntlSoRuleRetailer::getCreateRetailerTime, req.getStartTime())
            .le(req.getEndTime() != null, IntlSoRuleRetailer::getCreateRetailerTime, req.getEndTime())
            .orderByDesc(IntlSoRuleRetailer::getCreateRetailerTime);
        return queryWrapper;
    }

    @Override
    public String exportRetailerList(QuerySuRuleRetailerReq req) {
        CountryDTO countryInfo = getCountryDTO(req);
        LambdaQueryWrapper<IntlSoRuleRetailer> queryWrapper = getIntlSoRuleRetailerLambdaQueryWrapper(req);
        List<IntlSoRuleRetailer> list = this.list(queryWrapper);
        return exportRetailerList(list, countryInfo);
    }

    @Override
    public void initAllCountryRetailer() {
        BasePage req = new BasePage();
        req.setPageNum(1);
        req.setPageSize(1000);
        Map<String, CountryDTO> countryDTOMap = countryTimeZoneApiService.getAllCountryInfo();

        List<IntlSoRuleDetail> soRuleDetailList = intlSoRuleDetailService.list();
        Map<String, IntlSoRuleDetail> detailMap =
            soRuleDetailList.stream().collect(Collectors.toMap(IntlSoRuleDetail::getCountryCode, Function.identity()));
        try {
            // 然后重新同步
            do {
                List<IntlRetailerDTO> list = intlRetailerApiService.getRetailerListByPage(req);
                if (list.isEmpty()) {
                    break;
                }
                req.setPageNum(req.getPageNum() + 1);
                List<IntlSoRuleRetailer> retailerList = list.stream()
                    .filter(
                        t -> StringUtils.isNotBlank(t.getCountryCode()) && StringUtils.isNotBlank(t.getRetailerCode()))
                    .map(t -> getIntlSoRuleRetailer(t, countryDTOMap)).collect(Collectors.toList());
                LambdaQueryWrapper<IntlSoRuleRetailer> queryWrapper = new LambdaQueryWrapper<>();
                List<IntlSoRuleRetailer> existRetailerList =
                    this.list(queryWrapper.eq(IntlSoRuleRetailer::getCategory, SoRuleEnum.MASTER.getValue()).in(
                        IntlSoRuleRetailer::getRetailerCode,
                        retailerList.stream().map(IntlSoRuleRetailer::getRetailerCode).collect(Collectors.toList())));
                insertBatchList(retailerList, existRetailerList, detailMap);
            } while (true);
            // 更新新增数据为初始后的
            updateIncrementRetailer();
            // 更新主规则的零售商imei、qty开关统计信息
            countryDTOMap.forEach((countryCode, country) -> updateRuleRetailerStatistics(countryCode));
        } catch (Exception e) {
            // 删除所有初始化零售商
            LambdaQueryWrapper<IntlSoRuleRetailer> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(IntlSoRuleRetailer::getRuleId, CommonConstants.INIT_RULE_ID)
                .eq(IntlSoRuleRetailer::getCategory, SoRuleEnum.MASTER.getValue())
                .isNull(IntlSoRuleRetailer::getCreatedBy);
            log.error("initAllCountryRetailer error {}", ExceptionUtils.getStackTrace(e));
            // 发生异常，则删除所有初始化零售商
            this.remove(wrapper);
        }
    }

    public void insertBatchList(List<IntlSoRuleRetailer> retailerList, List<IntlSoRuleRetailer> existRetailerList,
        Map<String, IntlSoRuleDetail> ruleMap) {

        List<IntlSoRuleRetailer> insertList = Lists.newArrayListWithCapacity(retailerList.size());
        List<IntlSoRuleRetailer> updateList = Lists.newArrayListWithCapacity(retailerList.size());
        // 得到已经存在的map
        Map<String, IntlSoRuleRetailer> existRetailerMap = existRetailerList.stream()
            .collect(Collectors.toMap(IntlSoRuleRetailer::getRetailerCode, t -> t, (t, t2) -> t));
        for (IntlSoRuleRetailer retailer : retailerList) {
            IntlSoRuleRetailer existRetailer = existRetailerMap.get(retailer.getRetailerCode());
            if (Objects.nonNull(existRetailer)) {
                existRetailer.setRegionCode(retailer.getRegionCode());
                existRetailer.setCountryCode(retailer.getCountryCode());
                existRetailer.setRetailerName(retailer.getRetailerName());
                existRetailer.setCreateRetailerTime(retailer.getCreateRetailerTime());
                existRetailer.setChannelType(retailer.getChannelType());
                existRetailer.setUpdatedAt(System.currentTimeMillis());
                updateList.add(existRetailer);
            } else {
                // 判断规则是否存在，如果规则存在，那么根据规则使能是否启用qty和imei规则，并且关联ruleId，否则写入数据
                IntlSoRuleDetail soRuleDetail = ruleMap.get(retailer.getCountryCode());
                if (soRuleDetail != null) {
                    // 查询主数据关了的记录
                    List<Integer> retailersSwitchList =
                        JSON.parseArray(soRuleDetail.getDefaultRetailersSwitch(), Integer.class);
                    if (CollectionUtils.isNotEmpty(retailersSwitchList)) {
                        if (retailersSwitchList.contains(1)) {
                            retailer.setImeiSwitch(1);
                        }
                        if (retailersSwitchList.contains(2)) {
                            retailer.setQtySwitch(1);
                        }
                        retailer.setIsNew(1);
                    }
                    // 设置规则的id
                    retailer.setRuleId(soRuleDetail.getId());
                } else {
                    // 新增的国家，未配置规则列表，那么写入临时数据
                    retailer.setIsNew(0);
                }
                insertList.add(retailer);
            }
        }
        if (CollectionUtils.isNotEmpty(updateList)) {
            this.baseMapper.batchUpdateById(updateList);
        }
        if (CollectionUtils.isNotEmpty(insertList)) {
            this.baseMapper.batchInsertRetailer(insertList);
        }
    }

    /**
     * 单个retailer的更新
     *
     * @param retailerModel 零售商模型
     */
    @Override
    public void saveOrUpdateByRetailerCode(SoRuleRetailerModel retailerModel) {
        IntlSoRuleRetailer retailer =
            ConvertUtils.buildSoRuleRetailer(retailerModel, SoRuleEnum.MASTER, CommonConstants.INIT_RULE_ID);
        Optional<CountryDTO> optional =
            countryTimeZoneApiService.getCountryInfoFromCache(retailerModel.getCountryCode());
        optional.ifPresent(dto -> retailer.setRegionCode(dto.getAreaCode()));

        LambdaQueryWrapper<IntlSoRuleRetailer> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(IntlSoRuleRetailer::getRetailerCode, retailer.getRetailerCode())
            .eq(IntlSoRuleRetailer::getCategory, SoRuleEnum.MASTER.getValue());
        IntlSoRuleRetailer old = this.getOne(queryWrapper);
        if (old != null) {
            old.setRegionCode(retailer.getRegionCode());
            old.setCountryCode(retailer.getCountryCode());
            old.setRetailerName(retailer.getRetailerName());
            old.setChannelType(retailer.getChannelType());
            old.setUpdatedAt(System.currentTimeMillis());
            old.setCreateRetailerTime(retailerModel.getCreateRetailerTime());
            this.updateById(old);
            log.info("updateRetailer_update:{}", old.getRetailerCode());
        } else {
            Optional<IntlSoRuleDetail> soRuleDetail =
                intlSoRuleDetailService.getByCountryCode(retailerModel.getCountryCode());
            // 判断规则是否存在，如果规则存在，那么根据规则使能是否启用qty和imei规则，并且关联ruleId，否则写入数据
            if (soRuleDetail.isPresent()) {
                // 查询主数据关了的记录
                List<Integer> retailersSwitchList =
                    JSON.parseArray(soRuleDetail.get().getDefaultRetailersSwitch(), Integer.class);
                if (CollectionUtils.isNotEmpty(retailersSwitchList)) {
                    if (retailersSwitchList.contains(1)) {
                        retailer.setImeiSwitch(1);
                    }
                    if (retailersSwitchList.contains(2)) {
                        retailer.setQtySwitch(1);
                    }
                    retailer.setIsNew(1);
                }
                // 设置规则的id
                retailer.setRuleId(soRuleDetail.get().getId());
            } else {
                // 新增的国家，未配置规则列表，那么写入临时数据
                retailer.setIsNew(0);
            }
            this.save(retailer);
            // 更新规则的零售商imei、qty开关数量统计信息
            this.updateRuleRetailerStatistics(retailer.getCountryCode());
            log.info("save retailer:{}", retailer.getRetailerCode());
        }
    }

    private IntlSoRuleRetailer getIntlSoRuleRetailer(IntlRetailerDTO t, Map<String, CountryDTO> countryDTOMap) {
        IntlSoRuleRetailer retailer =
            ConvertUtils.buildRetailerWithRetailerType(t, SoRuleEnum.MASTER, CommonConstants.INIT_RULE_ID);
        CountryDTO dto = countryDTOMap.get(t.getCountryCode());
        if (dto != null) {
            retailer.setRegionCode(dto.getAreaCode());
        }
        return retailer;
    }

    @Override
    public void dealIncrementRetailer() {
        LambdaQueryWrapper<IntlSoRuleRetailer> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(IntlSoRuleRetailer::getIsNew, 1);
        List<IntlSoRuleRetailer> list = list(queryWrapper);
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        Map<String, List<IntlSoRuleRetailer>> map =
            list.stream().collect(groupingBy(IntlSoRuleRetailer::getCountryCode));
        StringBuilder sb = new StringBuilder();
        map.forEach((countryCode, retailerDtoList) -> {
            sb.append("●").append(countryCode).append("：新增").append(retailerDtoList.size()).append("个Retailer\n");
        });
        // 更新新增数据为初始后的
        updateIncrementRetailer();
        sendMessageService.sendTextMessageToPosition(sb.toString(), "256");
        // 更新主规则的零售商规则启用imei、qty开关数量
        map.forEach((countryCode, retailerDtoList) -> updateRuleRetailerStatistics(countryCode));
    }

    private void updateRuleRetailerStatistics(String countryCode) {
        SoRuleRetailerStatisticsDTO retailerStatistics =
            getRetailerStatistics(countryCode, SoRuleEnum.MASTER.getValue(), null);
        intlSoRuleDetailService.updateRetailerStatistics(countryCode, retailerStatistics);
    }

    public void updateIncrementRetailer() {
        LambdaUpdateWrapper<IntlSoRuleRetailer> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(IntlSoRuleRetailer::getIsNew, 1);
        update(updateWrapper.set(IntlSoRuleRetailer::getIsNew, 0));
    }

    @Override
    public SoRuleEnum getRetailerCategoryByCreateType(Integer type) {
        return Objects.equals(type, 1) ? SoRuleEnum.MASTER : SoRuleEnum.COPY;
    }

    @Override
    public SoRuleRetailerStatisticsDTO getRetailerStatistics(String countryCode, Integer category, String operatorId) {
        return getBaseMapper().getRetailerStatistics(countryCode, category, operatorId, Lists.newArrayList());
    }

    @Override
    public SoRuleRetailerStatisticsDTO getRetailerStatisticsWithExcludes(String countryCode, Integer category,
        List<String> excludeRetailerCodes) {
        return getBaseMapper().getRetailerStatistics(countryCode, category, null, excludeRetailerCodes);
    }

    @Override
    public String exportRetailerForApproveFlow(Long soRuleDetailLogId, String countryCode) {
        CountryDTO countryInfo = getCountryDTO(countryCode);
        LambdaQueryWrapper<IntlSoRuleRetailer> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(IntlSoRuleRetailer::getRuleId, soRuleDetailLogId)
            .eq(IntlSoRuleRetailer::getCountryCode, countryCode)
            .eq(IntlSoRuleRetailer::getCategory, SoRuleEnum.COPY.getValue());
        List<IntlSoRuleRetailer> list = this.list(queryWrapper);
        return exportRetailerList(list, countryInfo);
    }

    private String exportRetailerList(List<IntlSoRuleRetailer> list, CountryDTO countryInfo) {
        String timeZone = DateTimeUtil.getTimeZoneByCountryCode(UserInfoUtil.getUserContext().getLanguage());
        List<SoRuleRetailerDTO> dtoList =
            list.stream().map(t -> getSoRuleRetailerDTO(t, countryInfo, timeZone)).collect(Collectors.toList());
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            String fileName = "Retailers_" + System.currentTimeMillis() + ".xlsx";
            EasyExcel.write(outputStream, SoRuleRetailerDTO.class)
                .registerWriteHandler(new SimpleColumnWidthStyleStrategy(25)).sheet(countryInfo.getCountryName())
                .doWrite(dtoList);
            return fdsService.uploadFile(fileName, new ByteArrayInputStream(outputStream.toByteArray()));
        } catch (Exception e) {
            log.error("export retailer list error", e);
            throw new BizException(ErrorCodes.EXPORT_TO_EXCEL_FAILED);
        }
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    @Override
    public void updateImeiAndQtySwitch(String countryCode, Integer category, SwitchEnum imeiSwitch,
        SwitchEnum qtySwitch, String updatedBy) {
        IntlSoRuleRetailer retailer = new IntlSoRuleRetailer();
        if (Objects.nonNull(imeiSwitch)) {
            retailer.setImeiSwitch(imeiSwitch.getValue());
        }
        if (Objects.nonNull(qtySwitch)) {
            retailer.setQtySwitch(qtySwitch.getValue());
        }
        retailer.setUpdatedAt(DateUtils.getNowDate().getTime());
        retailer.setUpdatedBy(updatedBy);
        LambdaQueryWrapper<IntlSoRuleRetailer> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(IntlSoRuleRetailer::getCountryCode, countryCode).eq(IntlSoRuleRetailer::getCategory, category);
        update(retailer, queryWrapper);
    }

    @Override
    public boolean isInitializedModifyRetailers(String countryCode) {
        LambdaQueryWrapper<IntlSoRuleRetailer> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(IntlSoRuleRetailer::getCountryCode, countryCode).eq(IntlSoRuleRetailer::getCategory,
            SoRuleEnum.MASTER.getValue());
        long masterCount = this.count(queryWrapper);
        queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(IntlSoRuleRetailer::getCountryCode, countryCode)
            .eq(IntlSoRuleRetailer::getCategory, SoRuleEnum.COPY.getValue())
            .eq(IntlSoRuleRetailer::getRuleId, CommonConstants.INIT_RULE_ID);
        long copyCount = this.count(queryWrapper);
        return copyCount >= masterCount;
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    @Override
    public void initModifyRetailers(String countryCode, List<String> excludeRetailerCodes,
        ResultHandler<IntlSoRuleRetailer> resultHandler) {
        // 查询主规则对应的零售商，复制到修改规则零售商副本中，并初始化开关
        LambdaQueryWrapper<IntlSoRuleRetailer> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(IntlSoRuleRetailer::getCountryCode, countryCode)
            .eq(IntlSoRuleRetailer::getCategory, SoRuleEnum.MASTER.getValue())
            .notIn(CollectionUtils.isNotEmpty(excludeRetailerCodes), IntlSoRuleRetailer::getRetailerCode,
                excludeRetailerCodes);
        // 此处使用流式查询，避免查询过多数据到内存中
        baseMapper.selectList(queryWrapper, resultHandler);
    }

    @Override
    public void updateRuleId(String countryCode, int category, Long ruleId, String createdBy, String updatedBy) {
        IntlSoRuleRetailer retailer = new IntlSoRuleRetailer();
        retailer.setRuleId(ruleId);
        retailer.setUpdatedAt(DateUtils.getNowDate().getTime());
        retailer.setUpdatedBy(updatedBy);
        LambdaQueryWrapper<IntlSoRuleRetailer> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(IntlSoRuleRetailer::getCountryCode, countryCode).eq(IntlSoRuleRetailer::getCategory, category)
            .eq(IntlSoRuleRetailer::getRuleId, CommonConstants.INIT_RULE_ID)
            .eq(StringUtils.isNotBlank(createdBy), IntlSoRuleRetailer::getCreatedBy, createdBy);
        update(retailer, queryWrapper);
    }

    @Override
    public List<IntlSoRuleRetailer> getRetailerListByRuleId(Long ruleId) {
        LambdaQueryWrapper<IntlSoRuleRetailer> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(IntlSoRuleRetailer::getRuleId, ruleId);
        return list(queryWrapper);
    }

    @Override
    public List<IntlSoRuleRetailer> getCurrentInitRetailers(String countryCode, List<String> retailerCodes) {
        if (CollectionUtils.isEmpty(retailerCodes)) {
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<IntlSoRuleRetailer> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(IntlSoRuleRetailer::getCountryCode, countryCode)
            .in(IntlSoRuleRetailer::getRetailerCode, retailerCodes)
            .eq(IntlSoRuleRetailer::getCategory, SoRuleEnum.COPY.getValue())
            .eq(IntlSoRuleRetailer::getRuleId, CommonConstants.INIT_RULE_ID);
        return this.list(queryWrapper);
    }

    @Override
    public String getType() {
        return "soRuleService";
    }

    @Override
    public GetRetailerSoRuleResp queryCountryRules(GetRetailerSoRuleReq request) {
        return getRetailerSoRule(request);
    }
}
