package com.mi.info.intl.retail.intlretail.service.api.store.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

@Setter
@Getter
public class BusinessDataInputRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @JsonProperty("Region")
    private String region;

    @JsonProperty("ChannelTypeList")
    private List<Integer> channelTypeList;

    /**
     * 阵地类型
     */
    @JsonProperty("PositionTypeList")
    private List<Integer> positionTypeList;

    @JsonProperty("RetailerCodeList")
    private List<String> retailerCodeList;

    @JsonProperty("PositionCodeList")
    private List<String> positionCodeList;

    @JsonProperty("TitleCodeList")
    private List<Integer> titleCodeList;

    @JsonProperty("MidList")
    private List<String> midList;

    @JsonProperty("StoreGradeList")
    private List<String> storeGradeList;

    @JsonProperty("IsPromotion")
    private Integer isPromotion;
    /**
     * 类型 1: 促销员 2: 督导 11: 新促销员查询
     */
    @JsonProperty("Type")
    private Integer type;

    /**
     * 页码（从1开始）
     */
    @JsonProperty("PageNum")
    private Integer pageNum;

    /**
     * 每页数量（默认100）
     */
    @JsonProperty("PageSize")
    private Integer pageSize;

}
