package com.mi.info.intl.retail.intlretail.app.exception.handler;

import com.mi.info.intl.retail.exception.RetailRunTimeException;
import com.mi.info.intl.retail.exception.RmsApiException;
import org.apache.dubbo.rpc.RpcException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import com.mi.info.intl.retail.exception.BizException;
import com.mi.info.intl.retail.model.CommonApiResponse;
import com.xiaomi.cnzone.storems.common.exception.BusinessException;

import lombok.extern.slf4j.Slf4j;

/**
 * created by MIT 业务定制全局异常处理，只需捕捉业务定制异常，通用异常捕捉在基础库已经处理 GlobalExceptionHandler ： 基础库的全局异常处理器，处理通用异常
 * CustomGlobalExceptionHandler ： 业务服务定制的的全局异常处理器，处理业务定制异常
 *
 * <AUTHOR>
 * @date 2021/12/29
 */
@RestControllerAdvice
@Slf4j
public class CustomGlobalExceptionHandler {

    @ExceptionHandler(RmsApiException.class)
    public CommonApiResponse<String> handleRmsException(RmsApiException ex) {
        log.warn("RmsApiException`s error:{},code:{}", ex.getMessage(), ex.getErrorCode(), ex.getCause());
        // 403:拒绝访问该资源，返回：4030，与前端约定代表该用户没有对应环境资源的访问权限
        return CommonApiResponse.failure(ex.getErrorCode() == 403 ? 4030 : 501, ex.getMessage(), ex.getResult());
    }

    @ExceptionHandler(RetailRunTimeException.class)
    public CommonApiResponse<String> handleProxyException(RetailRunTimeException ex) {
        log.warn("RmsApiException`s error:{}", ex.getMessage(), ex.getCause());
        return CommonApiResponse.failure(502, ex.getMessage());
    }

    @ExceptionHandler(BusinessException.class)
    public CommonApiResponse<String> handleBusinessException(BusinessException ex) {
        log.warn("BusinessException`s error:{}", ex.getMessage(), ex.getCause());
        return CommonApiResponse.failure(502, ex.getMessage());
    }

    /**
     * 处理dubbo 调用异常
     */
    @ExceptionHandler(RpcException.class)
    public CommonApiResponse<String> rpcException(RpcException rpcException) {
        // 记录详细异常日志
        log.error("RpcException`s error: {}", rpcException.getMessage(), rpcException);
        // 返回详细的异常信息给前端
        return CommonApiResponse.failure(400, rpcException.getMessage());
    }

    @ExceptionHandler(BizException.class)
    public CommonApiResponse<String> bizException(BizException bizException) {
        // 记录详细异常日志
        String message = bizException.getMessage();
        log.error("bizException`s error: {}", message);
        // 返回详细的异常信息给前端
        return CommonApiResponse.failure(bizException.getCode(), message);
    }
}
