package com.mi.info.intl.retail.intlretail.service.app.retailer.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 国际培训/考试系统使用
 */
@Getter
@AllArgsConstructor
public enum RmsUserTitleEnum {

    PROMOTER(500900001, 246, "Promoter"),
    TEMPORARY_PROMOTER(100000027, 247, "Temporary Promoter"),
    XIAOMI_STORE_PROMOTER(100000026, 261, "Xiaomi Store Promoter"),
    SUPERVISOR(500900002, 248, "Supervisor"),
    SUPERVISOR_WITHOUT_PROMOTERS(100000051, 249, "Supervisor without Promoters"),
    MERCHANDISER(100000024, 250, "Merchandiser"),
    TRAINER(100000007, 286, "Trainer");

    private final int id;
    private final int orgId;
    private final String name;

}