package com.mi.info.intl.retail.intlretail.app.controller;

import com.alibaba.fastjson.JSON;
import com.mi.info.intl.retail.api.task.enums.TaskActionEnum;
import com.mi.info.intl.retail.intlretail.app.enums.UserJobSupervisorEnum;
import com.mi.info.intl.retail.intlretail.app.event.RmsApiRequestEvent;
import com.mi.info.intl.retail.intlretail.infra.rpc.TaskCenterServiceAdapter;
import com.mi.info.intl.retail.intlretail.service.api.mq.dto.RmsAipRequestInfo;
import com.mi.info.intl.retail.intlretail.service.app.rpc.StoreBuildRpc;
import com.mi.info.intl.retail.model.CommonApiResponse;
import com.xiaomi.cnzone.brain.platform.api.model.req.admin.CurrentTaskInstanceReq;
import com.xiaomi.cnzone.storeapi.model.v2.storenode.req.NodeSubmitReq;
import com.xiaomi.cnzone.xmstore.action.api.model.action.req.ActionGetInspectListReq;
import com.xiaomi.cnzone.xmstore.action.api.model.action.req.ActionSubmitReq;
import com.xiaomi.cnzone.xmstore.action.api.model.action.resp.ActionGetInspectListDataResp;
import com.xiaomi.cnzone.xmstore.action.api.model.action.resp.ActionSubmitResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/api/proxy/xmstore")
public class XmStoreController extends BaseController {
    
    @Autowired
    private StoreBuildRpc storeBuildRpc;
    
    @Autowired
    private TaskCenterServiceAdapter taskCenterServiceAdapter;
    
    @Autowired
    private ApplicationEventPublisher eventPublisher;
    
    /**
     * 撤销审核
     *
     * @param request
     * @return
     */
    @PostMapping("/auditCancel")
    public CommonApiResponse auditCancel(NodeSubmitReq request) {
        Object result = storeBuildRpc.submit(request);
        return new CommonApiResponse<>(result);
    }
    
    /**
     * 删除草稿
     */
    @PostMapping("/deleteDraft")
    public CommonApiResponse deleteDraft(NodeSubmitReq request) {
        Object result = storeBuildRpc.submit(request);
        return new CommonApiResponse<>(result);
    }
    
    /**
     * 批量导入
     *
     * @param request
     * @return
     */
    @PostMapping("/batchImport")
    public CommonApiResponse batchImport(NodeSubmitReq request) {
        Object result = storeBuildRpc.submit(request);
        return new CommonApiResponse<>(result);
    }
    
    /**
     * 门店营业/闭店/周清/门店检查任务提交
     *
     * @param request
     * @return
     */
    @PostMapping("/actionSubmit")
    public CommonApiResponse<ActionSubmitResp> actionSubmit(@RequestBody ActionSubmitReq request) {
        // 接口转发
        log.info("actionSubmit#request={}", JSON.toJSONString(request));
        ActionSubmitResp result = storeBuildRpc.actionSubmit(request);
        if (UserJobSupervisorEnum.isValidJob(request.getUserTitle())) {
            // 发布事件
            String actionName = TaskActionEnum.STORE_CHECK_SR.getActionName();
            eventPublisher.publishEvent(new RmsApiRequestEvent(this,
                    new RmsAipRequestInfo(actionName, actionName, getAccount(), request.getOrgId())));
        }
        else {
            CurrentTaskInstanceReq req = new CurrentTaskInstanceReq();
            req.setMid(Long.valueOf(request.getMid().trim()));
            req.setOrgId(request.getPositionCode());
            req.setBusinessTypeId(109L);
            req.setRetailTenantId("2");
            req.setRetailAppSign("CHANNEL_RETAIL");
            taskCenterServiceAdapter.finishUserCurrentTaskInstance(req);
        }
        return new CommonApiResponse<>(result);
    }

    /**
     * 门店检查列表页面查询
     *
     * @param request
     * @return
     */
    @PostMapping("/getActionInspectPageList")
    public CommonApiResponse<ActionGetInspectListDataResp> getActionInspectPageList(@RequestBody ActionGetInspectListReq request) {
        // 接口转发
        log.info("getActionInspectPageList#request={}", JSON.toJSONString(request));
        ActionGetInspectListDataResp result = storeBuildRpc.getActionInspectPageList(request);
        return new CommonApiResponse<>(result);
    }

    /**
     * 门店检查列表页面导出
     *
     * @param request
     * @return
     */
    @PostMapping("/exportExcel")
    public CommonApiResponse<List<String>> exportExcel(@RequestBody ActionGetInspectListReq request) {
        // 接口转发
        log.info("exportExcel#request={}", JSON.toJSONString(request));
        List<String> result = storeBuildRpc.exportExcel(request);
        return new CommonApiResponse<>(result);
    }
}
