<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.info.intl.retail.so.infra.database.mapper.upload.IntlSoUserInfoMapper">

    <resultMap id="BaseResultMap" type="com.mi.info.intl.retail.so.domain.upload.entity.IntlSoUserInfo">
            <id property="id" column="id" />
            <result property="createdbyMid" column="createdby_mid" />
            <result property="createdbyRmsaccount" column="createdby_rmsaccount" />
            <result property="createdbyJobtitle" column="createdby_jobtitle" />
            <result property="salesmanMid" column="salesman_mid" />
            <result property="salesmanRmsaccount" column="salesman_rmsaccount" />
            <result property="salesmanJobtitle" column="salesman_jobtitle" />
    </resultMap>

    <sql id="Base_Column_List">
        id,createdby_mid,createdby_rmsaccount,createdby_jobtitle,salesman_mid,salesman_rmsaccount,
        salesman_jobtitle
    </sql>

    <insert id="batchInsert">
        INSERT INTO intl_so_user_info
        (createdby_mid, salesman_mid, createdby_rmsaccount, createdby_jobtitle,
        salesman_rmsaccount, salesman_jobtitle)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.createdByMid}, #{item.salesmanMid}, #{item.createdByRmsAccount}, #{item.createdByJobTitle},
            #{item.salesmanRmsAccount}, #{item.salesmanJobTitle})
        </foreach>
    </insert>
</mapper>
