package com.mi.info.intl.retail.intlretail.app.controller.ldu;

import javax.annotation.Resource;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.mi.info.intl.retail.intlretail.service.api.ldu.IntlLduTargetService;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.*;
import com.mi.info.intl.retail.model.CommonApiResponse;
import com.xiaomi.mone.docs.annotations.dubbo.ApiDoc;
import com.xiaomi.mone.docs.annotations.dubbo.ApiModule;
import com.xiaomi.mone.docs.annotations.http.MiApiRequestMethod;

/**
 * <AUTHOR>
 * @date 2025-07-07
 */
@RestController
@RequestMapping("/api/lduTargetMaintenance")
@ApiModule(value = "国际渠道零售服务", apiInterface = IntlLduTargetController.class)
public class IntlLduTargetController {

    @Resource
    private IntlLduTargetService intlLduTargetService;

    /**
     * 分页查询国际LDU目标
     */
    @PostMapping("/pageList")
    @ApiDoc(name = "分页查询国际LDU目标接口", value = "/api/lduTargetMaintenance/pageList", method = MiApiRequestMethod.POST)
    public CommonApiResponse<IPage<IntlLduTargetDto>> pageList(@RequestBody @Validated IntlLduTargetReq query) {
        return intlLduTargetService.pageList(query);
    }

    /**
     * 新增单条国际LDU目标
     */
    @ApiDoc(name = "LDU目标维护创建", value = "/api/lduTargetMaintenance/create", method = MiApiRequestMethod.POST)
    @PostMapping("/create")
    public CommonApiResponse<String> create(@RequestBody IntlLduTargetDto dto) {
        return intlLduTargetService.create(dto);
    }

    /**
     * 修改国际LDU目标
     */
    @ApiDoc(name = "LDU目标维护修改", value = "/api/lduTargetMaintenance/modify", method = MiApiRequestMethod.POST)
    @PostMapping("/modify")
    public CommonApiResponse<String> modify(@RequestBody IntlLduTargetDto dto) {
        return intlLduTargetService.modify(dto);
    }

    /**
     * 批量导入国际LDU目标（Excel上传）
     */
    @ApiDoc(name = "下载LDU目标维护excel模版", value = "/api/lduTargetMaintenance/exportTargetMaintenance",
            method = MiApiRequestMethod.POST)
    @PostMapping("/exportTargetMaintenance")
    public CommonResponse<String> exportTargetMaintenance(@RequestBody IntlLduTargetReq req) {
        return intlLduTargetService.exportTargetMaintenance(req);
    }

    @ApiDoc(name = "下载LDU目标维护excel模版", value = "/api/lduTargetMaintenance/downLoadLduTemp", method = MiApiRequestMethod.POST)
    @PostMapping("/downLoadLduTemp")
    public CommonApiResponse<String> downLoadLduTemp(@RequestBody BathConReq query) {
        return intlLduTargetService.downLoadLduTemp(query);
    }

    @ApiDoc(name = "批量新增LDU目标维护", value = "/api/lduTargetMaintenance/importTargetMaintenance", method = MiApiRequestMethod.POST)
    @PostMapping("/importTargetMaintenance")
    public CommonApiResponse<String> importTagetMaintenance(@RequestBody ImportIntlLduTargetReq query) {
        return intlLduTargetService.importTargetMaintenance(query);
    }
}
