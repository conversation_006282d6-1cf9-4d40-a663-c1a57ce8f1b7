package com.mi.info.intl.retail.user.app;

import com.mi.info.intl.retail.intlretail.service.api.store.dto.BusinessDataInputRequest;
import com.mi.info.intl.retail.intlretail.service.api.store.dto.BusinessDataResponse;
import com.mi.info.intl.retail.user.infra.entity.IntlRmsUser;
import java.util.List;

/**
 * <AUTHOR> 董鑫儒
 * @Description
 * @Date 创建于 2025/6/3 10:49
 */
public interface UserService {

    IntlRmsUser getUserInfo(String email);
    
    IntlRmsUser getUserInfoDomainName(String email);

    /**
     * 查询用户阵地信息列表数据
     */
    List<BusinessDataResponse> getUserPositions(BusinessDataInputRequest userInfoRequest);
}
