package com.mi.info.intl.retail.intlretail.app.config;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.alibaba.nacos.spring.context.annotation.config.NacosPropertySource;
import org.springframework.context.annotation.Configuration;
import java.util.ArrayList;
import java.util.List;

@Configuration
@NacosPropertySource(dataId = "consumer-country-config", autoRefreshed = true)
public class ConsumerCountryConfig {
    @NacosValue(value = "${consumer.inspection.countryList:}", autoRefreshed = true)
    private String consumerCountryList;

    @NacosValue(value = "${consumer.channelStore.skippedCountryList:}", autoRefreshed = true)
    private String skippedCountryList;

    public List<String> getConsumerCountryList() {
        List<String> list = new ArrayList<>();
        if (consumerCountryList != null && !consumerCountryList.isEmpty()) {
            for (String s : consumerCountryList.split(",")) {
                list.add(s.trim());
            }
        }
        return list;
    }

    public List<String> getSkippedCountryList() {
        List<String> list = new ArrayList<>();
        if (skippedCountryList != null && !skippedCountryList.isEmpty()) {
            for (String s : skippedCountryList.split(",")) {
                list.add(s.trim());
            }
        }
        return list;
    }
} 