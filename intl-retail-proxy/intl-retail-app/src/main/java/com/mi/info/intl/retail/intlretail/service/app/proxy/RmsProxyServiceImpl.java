package com.mi.info.intl.retail.intlretail.service.app.proxy;

import com.mi.info.intl.retail.intlretail.domain.http.rms.RmsOuathServiceProvider;
import com.mi.info.intl.retail.intlretail.domain.http.rms.fds.FdsOathServiceProvider;
import com.mi.info.intl.retail.intlretail.service.api.proxy.FileProxyService;
import com.mi.info.intl.retail.intlretail.service.api.proxy.RmsProxyService;
import com.mi.info.intl.retail.intlretail.service.app.proxy.dto.RmsApiRequest;
import com.mi.info.intl.retail.intlretail.service.app.proxy.dto.RmsTypeApiRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationToken;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;

@Service
public class RmsProxyServiceImpl implements RmsProxyService {

    @Autowired(required = false)
    private RmsOuathServiceProvider rmsOuathServiceProvider;

    @Autowired(required = false)
    private FdsOathServiceProvider fdsOathServiceProvider;

    @Resource
    private FileProxyService fileProxyService;

    private static final String ERROR_HTTP_METHOD_MESSAGE = "error:method is error,httpMethod:";

    @Value("${config-url.rms}")
    private String rmsUrl;

    @Value("${config-url.fds}")
    private String fdsUrl;

    @Override
    public String requestByUserToken(String path, String data, String userToken, String httpMethod) {
        HttpMethod method = HttpMethod.resolve(httpMethod);
        Assert.notNull(method, ERROR_HTTP_METHOD_MESSAGE + httpMethod);
        //data 转为rms的input
        RmsApiRequest request = new RmsApiRequest(data);
        return rmsOuathServiceProvider.httpForJson(rmsUrl + path, request, userToken, method);
    }

    @Override
    public String requestByUserToken(String path, String type, String data, String userToken, String httpMethod) {
        HttpMethod method = HttpMethod.resolve(httpMethod);
        Assert.notNull(method, ERROR_HTTP_METHOD_MESSAGE + httpMethod);
        //data 转为rms的input
        RmsApiRequest request = new RmsTypeApiRequest(data, type);
        return rmsOuathServiceProvider.httpForJson(rmsUrl + path, request, userToken, method);
    }

    @Override
    public ResponseEntity<ByteArrayResource> requestByUserToken(String path, String userToken, String httpMethod) {
        HttpMethod method = HttpMethod.resolve(httpMethod);
        Assert.notNull(method, ERROR_HTTP_METHOD_MESSAGE + httpMethod);
        Assert.notNull(path, "error:path is error,httpMethod:" + httpMethod);
        if (path.startsWith(rmsUrl)) {
            // path为连接ßß
            return fileProxyService.httpForFile(path, userToken, method);
        }

        if (path.startsWith(fdsUrl)) {
            return fdsOathServiceProvider.httpForFile(path, userToken, method);
        }

        return null;

    }

    @Override
    public String soRetailSyncToRms(String path, String data, String httpMethod) {
        JwtAuthenticationToken token = (JwtAuthenticationToken) SecurityContextHolder.getContext().getAuthentication();
        //data 转为rms的input
        RmsApiRequest request = new RmsApiRequest(data);
        return rmsOuathServiceProvider.httpForJson(rmsUrl + path, request, token.getToken().getTokenValue(), HttpMethod.POST);
    }
}
