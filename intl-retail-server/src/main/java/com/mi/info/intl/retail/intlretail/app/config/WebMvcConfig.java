package com.mi.info.intl.retail.intlretail.app.config;

import com.mi.info.intl.retail.intlretail.app.interceptor.ApiProxyInterceptor;
import com.mi.info.intl.retail.intlretail.app.interceptor.JwtUserInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
public class WebMvcConfig implements WebMvcConfigurer {

    private final ApiProxyInterceptor apiProxyInterceptor;

    private final JwtUserInterceptor jwtUserInterceptor;

    @Autowired
    public WebMvcConfig(ApiProxyInterceptor apiProxyInterceptor, JwtUserInterceptor jwtUserInterceptor) {
        this.apiProxyInterceptor = apiProxyInterceptor;
        this.jwtUserInterceptor = jwtUserInterceptor;
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        if (registry == null) {
            throw new IllegalArgumentException("InterceptorRegistry cannot be null");
        }
        registry.addInterceptor(apiProxyInterceptor)
                .addPathPatterns("/*/api/**", "/api/**");
        registry.addInterceptor(jwtUserInterceptor)
                .addPathPatterns("/*/api/**", "/api/**");
    }
}