package com.mi.info.intl.retail.intlretail.service.app.store.impl.mq;

import com.mi.info.intl.retail.intlretail.app.config.ConsumerCountryConfig;
import com.mi.info.intl.retail.intlretail.domain.common.utils.JsonUtil;
import com.mi.info.intl.retail.intlretail.domain.http.rms.RmsAppliUserOauthServiceProvider;
import com.mi.info.intl.retail.intlretail.service.api.management.StoreGradeService;
import com.mi.info.intl.retail.intlretail.service.api.retailer.dto.CommonResponse;
import com.mi.info.intl.retail.intlretail.service.api.store.dto.RmsRequest;
import com.mi.info.intl.retail.intlretail.service.api.store.dto.StoreMessage;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;

@Slf4j
@Service
@ConditionalOnProperty(name = "intl-retail.rocketmq.enabled", havingValue = "true", matchIfMissing = true)
@RocketMQMessageListener(topic = "${intl-retail.rocketmq.store.topic}", consumerGroup = "${intl-retail.rocketmq.store.group}")
public class StoreConsumer implements RocketMQListener<String> {

    static final String SAVE_STORE_PATH = "/api/data/v9.2/new_savestoreforchannelretail";

    @Autowired
    private RmsAppliUserOauthServiceProvider rmsAppliUserOauthServiceProvider;

    @Autowired
    private StoreGradeService storeGradeService;

    @Autowired
    private ConsumerCountryConfig consumerCountryConfig;

    @Override
    public void onMessage(String message) {
        log.info("StoreConsumer received message: {}", message);
        try {
            // 将 message 转换为 StoreMessage 对象
            StoreMessage storeMessage = Optional.ofNullable(JsonUtil.json2bean(message, StoreMessage.class))
                    .orElseThrow(() -> new RuntimeException("storeMessage is null"));

            log.debug("StoreConsumer parsed storeMessage: orgId={}, areaId={}, changeModule={}",
                     storeMessage.getOrgId(), storeMessage.getAreaId(), storeMessage.getChangeModule());

            // 获取 areaId 并检查是否为 null 或空字符串
            String areaId = Optional.ofNullable(storeMessage.getAreaId())
                    .filter(id -> !id.isEmpty())
                    .orElseThrow(() -> new RuntimeException("areaId is null or areaId is empty"));

            // 因新零售和渠道零售某些国家的部署机房有差异，导致数字门店这些国家会在新加披和欧洲同时发消息
            // 为了避免可能的数据覆盖问题发生，不符合渠道零售机房部署的国家不在对应的机房消费消息
            List<String> skippedCountries = consumerCountryConfig.getSkippedCountryList();
            log.debug("skippedCountries is {}", skippedCountries);
            if (!skippedCountries.isEmpty() && skippedCountries.contains(areaId)) {
                log.info("StoreConsumer skipped country is {}", areaId);
                return;
            }

            // 异步处理Store Grade计算（优先启动）
            processStoreGradeAsync(storeMessage);

            // 同步处理RMS请求
            processRmsRequest(areaId, message);

            log.info("StoreConsumer message processed successfully: orgId={}", storeMessage.getOrgId());
        } catch (Exception e) {
            log.error("StoreConsumer.onMessage error: message={}, error={}", message, e.getMessage(), e);
            throw new RuntimeException("StoreConsumer.onMessage error", e);
        }
    }

    /**
     * 处理RMS请求
     */
    private void processRmsRequest(String areaId, String message) {
        log.debug("Processing RMS request for areaId: {}", areaId);
        try {
            RmsRequest rmsRequest = new RmsRequest();
            rmsRequest.setInput(message);
            rmsAppliUserOauthServiceProvider.httpForObject(areaId, SAVE_STORE_PATH, rmsRequest, "POST", String.class);
            log.debug("RMS request processed successfully for areaId: {}", areaId);
        } catch (Exception e) {
            log.error("Failed to process RMS request for areaId: {}, error: {}", areaId, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 异步处理Store Grade计算
     * retailerCode变化属于orgBuild，月capa变化属于orgExtension
     */
    @Async
    public CompletableFuture<Void> processStoreGradeAsync(StoreMessage storeMessage) {
        log.debug("Starting async Store Grade processing for orgId: {}", storeMessage.getOrgId());

        try {
            boolean change = storeMessage.getChangeModule().contains("orgBuild") ||
                           storeMessage.getChangeModule().contains("orgExtension");

            if (change) {
                log.info("Store Grade change detected for orgId: {}, changeModule: {}",
                        storeMessage.getOrgId(), storeMessage.getChangeModule());

                CommonResponse<Void> response = storeGradeService.storeChangeTrigger(storeMessage.getOrgId());

                log.info("Store Grade processing completed for orgId: {}", storeMessage.getOrgId());
            } else {
                log.debug("No Store Grade change detected for orgId: {}, changeModule: {}",
                         storeMessage.getOrgId(), storeMessage.getChangeModule());
            }

            // 调用 editStoreGrade 方法
            try {
                storeGradeService.editStoreGrade(storeMessage.getOrgId());
                log.debug("editStoreGrade called successfully for orgId: {}", storeMessage.getOrgId());
            } catch (Exception e) {
                log.error("Failed to call editStoreGrade for orgId: {}, error: {}",
                         storeMessage.getOrgId(), e.getMessage(), e);
                // 不抛出异常，继续处理其他逻辑
            }

            return CompletableFuture.completedFuture(null);
        } catch (Exception e) {
            log.error("Failed to process Store Grade for orgId: {}, error: {}",
                     storeMessage.getOrgId(), e.getMessage(), e);
            CompletableFuture<Void> future = new CompletableFuture<>();
            future.completeExceptionally(e);
            return future;
        }
    }
}