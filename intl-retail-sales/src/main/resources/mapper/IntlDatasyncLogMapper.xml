<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.info.intl.retail.so.infra.database.mapper.datasync.IntlDatasyncLogMapper">

    <!-- 批量插入同步日志 -->
    <insert id="batchInsert" parameterType="com.mi.info.intl.retail.so.domain.datasync.entity.IntlDatasyncLog">
        INSERT INTO intl_datasync_log
        (type, rms_id, retail_id, message, operate_type, is_data_abnormal, abnormal_message, status,error_message)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.type}, #{item.rmsId}, #{item.retailId}, #{item.message}, #{item.operateType},
            #{item.isDataAbnormal}, #{item.abnormalMessage},#{item.status},#{item.errorMessage})
        </foreach>
    </insert>

    <!-- 删除超过4个月的历史日志数据，每次最多删除5000条 -->
    <delete id="deleteOldLogs">
        DELETE
        FROM intl_datasync_log
        WHERE createdon &lt; DATE_SUB(NOW(), INTERVAL #{days} DAY) LIMIT 5000
    </delete>

</mapper>
