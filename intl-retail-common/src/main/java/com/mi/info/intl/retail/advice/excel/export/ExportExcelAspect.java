package com.mi.info.intl.retail.advice.excel.export;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.metadata.data.CellData;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.style.column.AbstractColumnWidthStyleStrategy;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.mi.info.intl.retail.advice.excel.annotation.ExportExcel;
import com.mi.info.intl.retail.model.BasePageRequest;
import com.mi.info.intl.retail.model.CommonApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.CharEncoding;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Sheet;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.lang.reflect.Method;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * excel导出切面
 *
 * <AUTHOR>
 * @date 2025/6/4
 **/
@Aspect
@Component
@Slf4j
@Order(1)
public class ExportExcelAspect {

    /**
     * 递归获取目标泛型类型
     */
    private static Class<?> findInnerGenericType(Type type, Class<?> targetRawType) {
        if (type instanceof ParameterizedType) {
            ParameterizedType pt = (ParameterizedType) type;
            if (pt.getRawType() instanceof Class && pt.getRawType() == targetRawType) {
                Type arg = pt.getActualTypeArguments()[0];
                if (arg instanceof Class) {
                    return (Class<?>) arg;
                } else if (arg instanceof ParameterizedType) {
                    Type raw = ((ParameterizedType) arg).getRawType();
                    if (raw instanceof Class) {
                        return (Class<?>) raw;
                    }
                }
            }
            for (Type arg : pt.getActualTypeArguments()) {
                Class<?> result = findInnerGenericType(arg, targetRawType);
                if (result != null) {
                    return result;
                }
            }
        }
        return null;
    }

    /**
     * 环绕切面
     *
     * @param point 切点
     * @return 返回值
     */
    @Around("@annotation(com.mi.info.intl.retail.advice.excel.annotation.ExportExcel)")
    public Object around(ProceedingJoinPoint point) throws Throwable {
        // 获取请求上下文
        ServletRequestAttributes attributes = (ServletRequestAttributes) Objects.requireNonNull(
                RequestContextHolder.getRequestAttributes());
        // 获取请求
        HttpServletRequest request = attributes.getRequest();
        // 获取响应
        HttpServletResponse response = Objects.requireNonNull(attributes.getResponse());

        // 获取方法签名
        Method method = ((MethodSignature) point.getSignature()).getMethod();
        // 获取注解参数值，导出excel
        MethodSignature methodSignature = (MethodSignature) point.getSignature();
        ExportExcel exportExcel = methodSignature.getMethod().getAnnotation(ExportExcel.class);
        String fileName = URLEncoder.encode(exportExcel.fileName(), CharEncoding.UTF_8);
        String sheetName = exportExcel.sheetName();
        // 处理response
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding(CharEncoding.UTF_8);
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");

        Type returnType = method.getGenericReturnType();
        Class<?> voType = null;
        if (findInnerGenericType(returnType, IPage.class) != null) {
            voType = findInnerGenericType(returnType, IPage.class);
            boolean fullExport = exportExcel.fullExport();
            BasePageRequest basePageRequest = null;
            Object[] args = point.getArgs();
            for (Object arg : args) {
                if (arg instanceof BasePageRequest) {
                    basePageRequest = ((BasePageRequest) arg);
                    basePageRequest.setPageSize(basePageRequest.getPageSize());
                    basePageRequest.setPageNum(basePageRequest.getPageNum());
                    break;
                }
            }
            Assert.notNull(basePageRequest, method.getName()
                    + "接口返回值为page，入参对象必须继承com.mi.info.intl.retail.core.exception.vo.BasePageRequest");
            ExcelWriter excelWriter = EasyExcelFactory.write(response.getOutputStream(), voType)
                    .registerWriteHandler(new AdaptiveWidthStyleStrategy()).build();
            WriteSheet writeSheet = EasyExcelFactory.writerSheet(sheetName).build();
            if (fullExport) {
                long pages;
                do {
                    Object result = point.proceed(args);
                    CommonApiResponse<?> apiResponse = (CommonApiResponse<?>) result;
                    IPage<?> page = (IPage<?>) apiResponse.getData();
                    List<?> records = page.getRecords();
                    pages = page.getPages();
                    excelWriter.write(records, writeSheet);
                    records.clear();
                    if (basePageRequest.getPageNum() >= pages) {
                        break;
                    }
                    basePageRequest.setPageNum(basePageRequest.getPageNum() + 1);
                } while (true);
                excelWriter.finish();
            } else {
                Object result = point.proceed(args);
                CommonApiResponse<?> apiResponse = (CommonApiResponse<?>) result;
                IPage<?> page = (IPage<?>) apiResponse.getData();
                List<?> records = page.getRecords();
                excelWriter.write(records, writeSheet);
                records.clear();
                excelWriter.finish();
            }
        } else if (findInnerGenericType(returnType, List.class) != null) {
            voType = findInnerGenericType(returnType, List.class);
            Object result = point.proceed();
            // 写入文件
            EasyExcelFactory.write(response.getOutputStream(), voType)
                    .registerWriteHandler(new AdaptiveWidthStyleStrategy()).sheet(sheetName).doWrite((List<?>) result);
        }
        return null;
    }

    /**
     * 自适应宽度策略
     */
    public static class AdaptiveWidthStyleStrategy extends AbstractColumnWidthStyleStrategy {

        /**
         * 存储sheet索引: 列索引: 宽度
         */
        private final Map<Integer, Map<Integer, Integer>> cache = new HashMap<>();

        @Override
        protected void setColumnWidth(WriteSheetHolder writeSheetHolder, List<WriteCellData<?>> cellDataList, Cell cell,
                                      Head head, Integer relativeRowIndex, Boolean isHead) {
            boolean needSetWidth = isHead || CollUtil.isNotEmpty(cellDataList);
            if (!needSetWidth) {
                return;
            }
            Integer columnWidth = getColumnWidth(cellDataList, cell, isHead);
            if (columnWidth < 0) {
                return;
            }
            // 最大不超过25，最小不小于10
            columnWidth = Math.min(columnWidth, 25);
            columnWidth = Math.max(columnWidth, 10);
            Map<Integer, Integer> maxColumnWidthMap = cache.computeIfAbsent(writeSheetHolder.getSheetNo(),
                    k -> new HashMap<>(16));
            Integer maxColumnWidth = maxColumnWidthMap.get(cell.getColumnIndex());
            if (maxColumnWidth == null || columnWidth > maxColumnWidth) {
                maxColumnWidthMap.put(cell.getColumnIndex(), columnWidth);
                Sheet sheet = writeSheetHolder.getSheet();
                sheet.setColumnWidth(cell.getColumnIndex(), columnWidth * 256);
            }
        }

        private Integer getColumnWidth(List<WriteCellData<?>> cellDataList, Cell cell, Boolean isHead) {
            if (isHead) {
                //列头宽度打个折，当这列数据的宽度都很小时，列头文字会换行以节省空间
                return stringWidth(cell.getStringCellValue()) * 8 / 10;
            }
            CellData cellData = cellDataList.get(0);
            CellDataTypeEnum type = cellData.getType();
            if (null == type) {
                return -1;
            }
            switch (type) {
                case STRING:
                    return stringWidth(cellData.getStringValue());
                case BOOLEAN:
                    return cellData.getBooleanValue().toString().getBytes().length;
                case NUMBER:
                    return cellData.getNumberValue().toString().getBytes().length;
                default:
                    return -1;
            }
        }

        /**
         * 字符串宽度（中文宽度x2）
         *
         * @param str 字符串
         * @return 宽度
         */
        private Integer stringWidth(String str) {
            int chineseLength = (str.getBytes().length - str.length()) / 2;
            int otherLength = str.length() - chineseLength;
            return chineseLength * 2 + otherLength + 1;
        }
    }
}