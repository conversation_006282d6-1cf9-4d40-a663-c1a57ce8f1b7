package com.mi.info.intl.retail.intlretail.infra.mq;

import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class RocketMQProducer {

    @Autowired
    private RocketMQTemplate rocketMQTemplate;

    public void sendMessage(String topic, String message) {
        rocketMQTemplate.convertAndSend(topic, message);
    }

    public String sendMessageWithResult(String topic, String msg) {
        try {
            final SendResult sendResult = rocketMQTemplate.syncSend(topic, msg);
            return sendResult.getMsgId();
        } catch (Exception e) {
            log.error("sendMessageWithResult error, topic:{}", topic, e);
            throw new RuntimeException(e.getMessage());
        }
    }
}
