package com.mi.info.intl.retail.management.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.info.intl.retail.exception.BizException;
import com.mi.info.intl.retail.exception.ErrorCodes;
import com.mi.info.intl.retail.intlretail.service.api.bpm.BpmService;
import com.mi.info.intl.retail.intlretail.service.api.bpm.dto.ApprovalTaskListResp;
import com.mi.info.intl.retail.intlretail.service.api.bpm.enums.BpmApproveBusinessCodeEnum;
import com.mi.info.intl.retail.intlretail.service.api.fds.FdsService;
import com.mi.info.intl.retail.intlretail.service.api.fds.dto.FdsUploadResult;
import com.mi.info.intl.retail.intlretail.service.api.management.StoreGradeRuleService;
import com.mi.info.intl.retail.intlretail.service.api.management.dto.CommonApproveHistoryReq;
import com.mi.info.intl.retail.intlretail.service.api.management.dto.CommonApproveHistoryResp;
import com.mi.info.intl.retail.intlretail.service.api.management.dto.CommonApproveReq;
import com.mi.info.intl.retail.intlretail.service.api.management.dto.DetailsBelowResp;
import com.mi.info.intl.retail.intlretail.service.api.management.dto.DetailsTopmostResp;
import com.mi.info.intl.retail.intlretail.service.api.management.dto.FileTemplateReq;
import com.mi.info.intl.retail.intlretail.service.api.management.dto.RetailerQueryReq;
import com.mi.info.intl.retail.intlretail.service.api.management.dto.RetailerQueryResp;
import com.mi.info.intl.retail.intlretail.service.api.management.dto.RuleModificationLog;
import com.mi.info.intl.retail.intlretail.service.api.management.dto.RuleQueryReq;
import com.mi.info.intl.retail.intlretail.service.api.management.dto.StoreGradeExportReq;
import com.mi.info.intl.retail.intlretail.service.api.management.dto.StoreGradeReq;
import com.mi.info.intl.retail.intlretail.service.api.management.dto.StoreGradeResp;
import com.mi.info.intl.retail.intlretail.service.api.management.dto.StoreGradeRuleDetailResp;
import com.mi.info.intl.retail.intlretail.service.api.management.dto.StoreGradeRuleFormDataReq;
import com.mi.info.intl.retail.intlretail.service.api.management.dto.StoreGradeRulePageQueryReq;
import com.mi.info.intl.retail.intlretail.service.api.management.dto.StoreGradeRulePageResp;
import com.mi.info.intl.retail.intlretail.service.api.management.dto.StoreGradeRuleRecallReq;
import com.mi.info.intl.retail.intlretail.service.api.management.dto.StoreGradeRuleReq;
import com.mi.info.intl.retail.intlretail.service.api.management.dto.SubmitResp;
import com.mi.info.intl.retail.intlretail.service.api.management.dto.UploadManuallyRuleDetailsResp;
import com.mi.info.intl.retail.management.config.StoreGradeRuleConfig;
import com.mi.info.intl.retail.management.constant.CommonConstant;
import com.mi.info.intl.retail.management.constant.NrJobConst;
import com.mi.info.intl.retail.management.constant.RouteConstants;
import com.mi.info.intl.retail.management.dto.PicCommitParam;
import com.mi.info.intl.retail.management.dto.StoreGradeBatchData;
import com.mi.info.intl.retail.management.dto.StoreGradeExprot;
import com.mi.info.intl.retail.management.entity.CommonApproveLog;
import com.mi.info.intl.retail.management.entity.StoreGradeRelation;
import com.mi.info.intl.retail.management.entity.StoreGradeRule;
import com.mi.info.intl.retail.management.mapper.CommonApproveLogMapper;
import com.mi.info.intl.retail.management.mapper.StoreGradeMapper;
import com.mi.info.intl.retail.management.mapper.StoreGradeRelationMapper;
import com.mi.info.intl.retail.management.mapper.StoreGradeRuleMapper;
import com.mi.info.intl.retail.management.rpc.FileRemoteRpc;
import com.mi.info.intl.retail.management.rpc.MainDataRpc;
import com.mi.info.intl.retail.management.service.CommonGatewayService;
import com.mi.info.intl.retail.management.service.enums.ApprovalBusinessKeyEnum;
import com.mi.info.intl.retail.management.service.enums.ApprovalStatus;
import com.mi.info.intl.retail.management.service.enums.ChannelTypeEnum;
import com.mi.info.intl.retail.management.service.enums.FileTemplateEnum;
import com.mi.info.intl.retail.management.service.enums.RuleStatusEnum;
import com.mi.info.intl.retail.management.service.enums.StoreGradeEnum;
import com.mi.info.intl.retail.management.service.enums.SubmitTypeEnum;
import com.mi.info.intl.retail.management.service.enums.TieringModificationMethodEnum;
import com.mi.info.intl.retail.management.utils.ExcelUtil;
import com.mi.info.intl.retail.model.BaseCountryEntity;
import com.mi.info.intl.retail.model.CommonApiResponse;
import com.mi.info.intl.retail.model.StoreGradeCompleteCount;
import com.mi.info.intl.retail.model.StoreGradeCompleteStatistics;
import com.mi.info.intl.retail.org.infra.entity.IntlRmsCountryTimezone;
import com.mi.info.intl.retail.org.infra.entity.IntlRmsStore;
import com.mi.info.intl.retail.org.infra.mapper.IntlRmsCountryTimezoneMapper;
import com.mi.info.intl.retail.org.infra.mapper.IntlRmsStoreMapper;
import com.mi.info.intl.retail.retailer.entity.IntlRmsRetailer;
import com.mi.info.intl.retail.retailer.mapper.IntlRmsRetailerMapper;
import com.mi.info.intl.retail.utils.JsonUtils;
import com.mi.info.intl.retail.utils.RpcContextUtil;
import com.mi.info.intl.retail.utils.intl.IntlTimeUtil;
import com.mi.info.intl.retail.utils.time.DateTimeUtil;
import com.xiaomi.cnzone.commons.utils.JacksonUtil;
import com.xiaomi.cnzone.commons.utils.StringUtils;
import com.xiaomi.cnzone.maindataapi.model.OrgDataDto;
import com.xiaomi.cnzone.maindataapi.model.OrgResponse;
import com.xiaomi.cnzone.maindataapi.model.enums.DomainEnum;
import com.xiaomi.cnzone.maindataapi.model.req.org.OrgExtension;
import com.xiaomi.cnzone.storeapi.api.channelbuild.common.ChannelCommonProvider;
import com.xiaomi.cnzone.storeapi.model.channelbuild.common.req.CountryRoleAdminReq;
import com.xiaomi.cnzone.storeapi.model.channelbuild.common.resp.CountryRoleAdminResp;
import com.xiaomi.nr.eiam.admin.dto.provider.user.SearchUserSensitiveInfoRequest;
import com.xiaomi.nr.eiam.admin.provider.UserAdminProvider;
import com.xiaomi.nr.eiam.admin.vo.provider.user.UserSensitiveInfoResp;
import com.xiaomi.nr.eiam.api.dto.userinfo.GetParentOrganPositionUserReq;
import com.xiaomi.nr.eiam.api.dto.userinfo.UserPosition;
import com.xiaomi.nr.eiam.api.provider.UserProvider;
import com.xiaomi.nr.job.admin.dto.TriggerJobRequestDTO;
import com.xiaomi.nr.job.admin.service.NrJobService;
import com.xiaomi.nr.job.core.biz.model.HandleMsg;
import com.xiaomi.nr.job.core.context.JobHelper;
import com.xiaomi.nr.job.core.handler.annotation.NrJob;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.dubbo.config.annotation.Reference;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.util.CollectionUtils;

import java.io.File;
import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import javax.annotation.Resource;

/**
 * 门店等级规则服务实现类
 */

@DubboService(group = "${retail.dubbo.group:}", interfaceClass = StoreGradeRuleService.class)
@Slf4j
@Service
public class StoreGradeRuleServiceImpl extends ServiceImpl<StoreGradeRuleMapper, StoreGradeRule>
        implements StoreGradeRuleService {

    private static final Integer LOCK_TIMEOUT = 10;

    @Resource
    private CommonApproveLogMapper commonApproveLogMapper;

    @Resource
    private IntlRmsRetailerMapper intlRmsRetailerMapper;

    @Resource
    private IntlRmsStoreMapper intlRmsStoreMapper;

    @Resource
    private BpmService bpmService;

    @Resource
    private StoreGradeRelationMapper storeGradeRelationMapper;

    @Resource
    private FdsService fdsService;

    @Resource
    private MainDataRpc mainDataRpc;

    @Resource
    private FileRemoteRpc fileRemoteRpc;

    @Resource
    private IntlRmsCountryTimezoneMapper intlRmsCountryTimezoneMapper;

    @Reference(group = "${store.dubbo.group:}", check = false, timeout = 20000, retries = 0)
    private ChannelCommonProvider channelCommonProvider;

    @DubboReference(group = "${organization.dubbo-group}", interfaceClass = UserProvider.class, check = false, timeout = 5000)
    private UserProvider userProvider;

    @DubboReference(group = "${organization.dubbo-group}", interfaceClass = UserAdminProvider.class, check = false, timeout = 5000)
    private UserAdminProvider userAdminProvider;

    @Reference(group = "${nr.dubbo.group}", check = false, interfaceClass = NrJobService.class)
    private NrJobService nrJobService;

    @Value("${proretail.project.id:11}")
    private Long projectId;

    @Resource
    private StoreGradeRuleConfig storeGradeRuleConfig;

    @Resource
    private StoreGradeMapper storeGradeMapper;

    @Resource
    private TransactionAspectSupport transactionAspectSupport;

    @Resource
    private CommonGatewayService commonGatewayService;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonApiResponse<Integer> saveStoreGradeRule(StoreGradeRuleReq storeGradeRuleReq) {
        boolean lockAcquired = false;
        log.info("Start saving store grade rule, request parameters: {}", storeGradeRuleReq);

        // 验证零售商列表
        if (Objects.isNull(storeGradeRuleReq) ||
                (Objects.equals(SubmitTypeEnum.SUBMIT.getType(), storeGradeRuleReq.getSubmitType()) &&
                        !Objects.equals(ChannelTypeEnum.IR.getKey(), storeGradeRuleReq.getChannelType()) &&
                        Objects.isNull(storeGradeRuleReq.getRetailerCode()))) {
            log.error("param err");
            throw new BizException(ErrorCodes.BIZ_ERROR, "param err");
        }
        String lockKey = CommonConstant.STORE_RULE_LOCK_TOKEN_KEY + "_" + storeGradeRuleReq.getChannelType() + "_" +
                storeGradeRuleReq.getRetailerCode() + "_" + storeGradeRuleReq.getCountryCode();
        try {
            lockAcquired = commonGatewayService.getCommonLockByHolding(lockKey, LOCK_TIMEOUT);
            if (!lockAcquired) {
                throw new BizException(ErrorCodes.BIZ_ERROR, "Please do not resubmit.");
            }

            //获取操作人
            String user = Optional.ofNullable(RpcContextUtil.getCurrentAccount())
                    .orElseThrow(() -> new BizException(ErrorCodes.BIZ_ERROR, "未查询到用户信息"));

            StoreGradeRule oldStoreGradeRule = checkReSubmit(storeGradeRuleReq);

            boolean submitFlag = SubmitTypeEnum.SUBMIT.getType().equals(storeGradeRuleReq.getSubmitType());

            StoreGradeRule storeGradeRule = buildStoreGradeRule(storeGradeRuleReq, user, oldStoreGradeRule, submitFlag);
            if (Objects.nonNull(storeGradeRuleReq.getFileData())) {
                PicCommitParam param = new PicCommitParam();
                param.setIds(Arrays.asList(storeGradeRuleReq.getFileData().getId()));
                fileRemoteRpc.fileCommit(param);
            }
            IntlRmsCountryTimezone country = getCountryByCode(storeGradeRuleReq.getCountryCode());
            if (Objects.nonNull(country)) {
                storeGradeRuleReq.setCountryName(country.getCountryName());
            }

            if (Objects.isNull(storeGradeRule.getId())) {
                this.save(storeGradeRule);
            } else if (Objects.nonNull(oldStoreGradeRule) &&
                    !Objects.equals(RuleStatusEnum.IN_EFFECT.getCode(), oldStoreGradeRule.getRulesStatus())) {
                this.updateById(storeGradeRule);
            } else {
                // 更新审批状态
                UpdateWrapper<StoreGradeRule> updateWrapper = new UpdateWrapper<>();
                updateWrapper.eq("id", storeGradeRule.getId());
                updateWrapper.set("approve_status", storeGradeRule.getApproveStatus());
                updateWrapper.set("update_by", storeGradeRule.getUpdateBy());
                updateWrapper.set("approved_time", null);
                this.update(null, updateWrapper);
            }
            //storeGradeRules.add(storeGradeRule);
            CommonApproveLog commonApproveLog =
                    buildCommonApproveLog(storeGradeRuleReq, user, oldStoreGradeRule, storeGradeRule);
            if (Objects.nonNull(commonApproveLog.getFlowInstId())) {
                storeGradeRuleReq.setFlowInstId(commonApproveLog.getFlowInstId());
            }
            if (submitFlag) {
                commonApproveLog.setFlowStatus(ApprovalStatus.IN_APPROVAL.getCode());
                storeGradeRuleReq.setId(storeGradeRule.getId());
                SubmitResp submitResp = null;
                if (Objects.equals(TieringModificationMethodEnum.MANUAL_UPLOAD.getKey(),
                        storeGradeRuleReq.getModificationMethod())) {
                    submitResp = batchUpdateStoreGrade(storeGradeRuleReq);
                } else if (Objects.equals(TieringModificationMethodEnum.SYSTEM_CALCULATION.getKey(),
                        storeGradeRuleReq.getModificationMethod())) {
                    submitResp = submit(storeGradeRuleReq);
                }
                if (Objects.isNull(submitResp) || Objects.isNull(submitResp.getFlowInstId())) {
                    log.error("Failed to submit store grade rule approval");
                    throw new BizException(ErrorCodes.BIZ_ERROR, "Failed to submit store grade rule");
                }
                commonApproveLog.setFlowInstId(submitResp.getFlowInstId());
            } else {
                commonApproveLog.setFlowStatus(ApprovalStatus.DRAFT.getCode());
            }
            if (Objects.isNull(commonApproveLog.getId())) {
                commonApproveLogMapper.insert(commonApproveLog);
            } else {
                commonApproveLogMapper.updateById(commonApproveLog);
            }

            // 对规则预计算
            if (submitFlag) {
                // 使用 CompletableFuture 异步处理统计计算，避免阻塞主线程
                CompletableFuture.runAsync(() -> {
                    try {
                        calculateAndSaveStoreGradeStatistics(storeGradeRule, commonApproveLog);
                    } catch (Exception e) {
                        log.error("Failed to asynchronously calculate store grade statistics for ruleId: {}",
                                storeGradeRule.getId(), e);
                    }
                });
            }

            return new CommonApiResponse<>(storeGradeRule.getId());

        } catch (Exception e) {
            log.error("Failed to save store grade rule", e);
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return CommonApiResponse.failure(500, e.getMessage());
        } finally {
            // 只有在成功获取锁的情况下才释放锁
            if (lockAcquired) {
                commonGatewayService.expireCommonLock(lockKey);
                log.info("Released distributed lock: {}", lockKey);
            }
        }
    }

    /**
     * 异步计算并保存门店等级统计信息
     *
     * @param storeGradeRule 门店等级规则
     * @param commonApproveLog 审批日志
     */
    private void calculateAndSaveStoreGradeStatistics(StoreGradeRule storeGradeRule,
                                                      CommonApproveLog commonApproveLog) {
        try {
            List<StoreGradeCompleteStatistics> storeGradeCompleteStatistics = new ArrayList<>();
            if (Objects.equals(TieringModificationMethodEnum.SYSTEM_CALCULATION.getKey(), storeGradeRule.getMethod())) {
                storeGradeCompleteStatistics = calculateStoreGradeStatisticsBySystemCalculation(storeGradeRule);
            } else if (Objects.equals(TieringModificationMethodEnum.MANUAL_UPLOAD.getKey(),
                    storeGradeRule.getMethod())) {
                storeGradeCompleteStatistics = getRelationGradeStatistics(storeGradeRule);
            }

            // 更新预计算数据
            storeGradeRule.setPreCalculationData(JsonUtils.toStr(storeGradeCompleteStatistics));
            this.updateById(storeGradeRule);

            // 更新审批日志的目标内容
            commonApproveLog.setTargetBody(JsonUtils.toStr(storeGradeRule));
            commonApproveLogMapper.updateById(commonApproveLog);

            log.info("Successfully calculated and saved store grade statistics for ruleId: {}", storeGradeRule.getId());
        } catch (Exception e) {
            log.error("Failed to calculate and save store grade statistics for ruleId: {}", storeGradeRule.getId(), e);
            throw new BizException(ErrorCodes.BIZ_ERROR, "Failed to calculate store grade statistics", e);
        }
    }

    /**
     * 计算门店等级统计信息
     *
     * @param storeGradeRule 门店等级规则
     * @return 门店等级统计列表
     */
    private List<StoreGradeCompleteStatistics> calculateStoreGradeStatisticsBySystemCalculation(
            StoreGradeRule storeGradeRule) {
        Map<String, List<String>> storeGradeMap = initializeStoreGradeMap();
        int totalCount = 0;
        int pageNum = 1;
        int pageSize = 100;
        boolean hasMore = true;

        while (hasMore) {
            // 分页查询门店
            List<IntlRmsStore> intlRmsStores = queryStoresByPage(storeGradeRule, pageNum, pageSize);

            if (org.apache.commons.collections4.CollectionUtils.isEmpty(intlRmsStores)) {
                break;
            }

            List<String> storeCodes = extractValidStoreCodes(intlRmsStores);

            // 获取门店详细信息
            OrgResponse orgResponse = mainDataRpc.pageSelectStoreByOrgIds(storeCodes, 1, storeCodes.size());
            if (orgResponse == null || orgResponse.getOrgList() == null || orgResponse.getOrgList().isEmpty()) {
                log.warn("No store information found, store codes: {}", storeCodes);
                hasMore = storeCodes.size() == pageSize;
                pageNum++;
                continue;
            }

            // 按照 orgId 对 orgList 进行分组
            Map<String, List<OrgDataDto>> orgListGroupedByOrgId = orgResponse.getOrgList()
                    .stream().collect(Collectors.groupingBy(orgDataDto -> orgDataDto.getOrgBase().getOrgId()));

            // 处理每个门店
            for (String storeCode : storeCodes) {
                try {
                    totalCount =
                            processStore(storeCode, orgListGroupedByOrgId, storeGradeRule, storeGradeMap, totalCount);
                } catch (Exception e) {
                    log.error("Failed to process store change for storeCode: {}", storeCode, e);
                }
            }

            hasMore = intlRmsStores.size() == pageSize;
            pageNum++;
        }

        // 统计各等级门店数量
        return buildStoreGradeStatistics(storeGradeMap, totalCount);
    }

    /**
     * 初始化门店等级映射
     *
     * @return 初始化的门店等级映射
     */
    private Map<String, List<String>> initializeStoreGradeMap() {
        Map<String, List<String>> storeGradeMap = new HashMap<>();
        storeGradeMap.put("S", new ArrayList<>());
        storeGradeMap.put("A", new ArrayList<>());
        storeGradeMap.put("B", new ArrayList<>());
        storeGradeMap.put("C", new ArrayList<>());
        storeGradeMap.put("D", new ArrayList<>());
        return storeGradeMap;
    }

    /**
     * 分页查询门店信息
     *
     * @param storeGradeRule 门店等级规则
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @return 门店列表
     */
    private List<IntlRmsStore> queryStoresByPage(StoreGradeRule storeGradeRule, int pageNum, int pageSize) {
        if (Objects.equals(ChannelTypeEnum.IR.getKey(), storeGradeRule.getChannelType())) {
            return intlRmsStoreMapper.pageSelectByCountryCodeAndChannelType(storeGradeRule.getCountryCode(),
                    storeGradeRule.getChannelType(), (pageNum - 1) * pageSize, pageSize);
        } else {
            Page<IntlRmsStore> page = new Page<>(pageNum, pageSize);
            LambdaQueryWrapper<IntlRmsStore> storeQueryWrapper = Wrappers.lambdaQuery();
            storeQueryWrapper.eq(IntlRmsStore::getRetailerIdName, storeGradeRule.getRetailerCode())
                    .ne(IntlRmsStore::getOperationStatusName, "Closed").isNotNull(IntlRmsStore::getCrssCode)
                    .ne(IntlRmsStore::getCrssCode, "").select(IntlRmsStore::getCrssCode);
            Page<IntlRmsStore> storePage = intlRmsStoreMapper.selectPage(page, storeQueryWrapper);
            return storePage.getRecords();
        }
    }

    /**
     * 提取有效的门店编码
     *
     * @param stores 门店列表
     * @return 有效的门店编码列表
     */
    private List<String> extractValidStoreCodes(List<IntlRmsStore> stores) {
        return stores.stream().map(IntlRmsStore::getCrssCode).filter(Objects::nonNull)
                .filter(code -> !code.trim().isEmpty()).collect(Collectors.toList());
    }

    /**
     * 处理单个门店
     *
     * @param storeCode 门店编码
     * @param orgListGroupedByOrgId 按orgId分组的门店信息
     * @param storeGradeRule 门店等级规则
     * @param storeGradeMap 门店等级映射
     */
    private Integer processStore(String storeCode, Map<String, List<OrgDataDto>> orgListGroupedByOrgId,
                                 StoreGradeRule storeGradeRule, Map<String, List<String>> storeGradeMap,
                                 Integer totalCount) {
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(orgListGroupedByOrgId.get(storeCode))) {
            log.warn("Store does not exist, storeCode: {}", storeCode);
            return totalCount;
        }

        OrgDataDto orgDataDto = orgListGroupedByOrgId.get(storeCode).get(0);
        Integer capacity = orgDataDto.getOrgExtension().getCapaMonth();
        if (Objects.equals(capacity, 0)) {
            return totalCount;
        }
        String newGrade = getStoreGrade(capacity, storeGradeRule);

        if (newGrade != null) {
            storeGradeMap.computeIfAbsent(newGrade, k -> new ArrayList<>()).add(storeCode);
            totalCount++;
        }
        return totalCount;
    }

    /**
     * 构建门店等级统计信息
     *
     * @param storeGradeMap 门店等级映射
     * @param totalCount 总门店数
     * @return 门店等级统计列表
     */
    private List<StoreGradeCompleteStatistics> buildStoreGradeStatistics(Map<String, List<String>> storeGradeMap,
                                                                         int totalCount) {

        List<StoreGradeCompleteStatistics> storeGradeCompleteStatistics = new ArrayList<>();

        for (Map.Entry<String, List<String>> entry : storeGradeMap.entrySet()) {
            StoreGradeCompleteStatistics gradeCompleteStatistics = new StoreGradeCompleteStatistics();
            gradeCompleteStatistics.setGrade(entry.getKey());
            gradeCompleteStatistics.setCount(entry.getValue().size());
            gradeCompleteStatistics.setPercentage(
                    totalCount > 0 ? (double) gradeCompleteStatistics.getCount() / totalCount : 0.0);
            storeGradeCompleteStatistics.add(gradeCompleteStatistics);
        }
        StoreGradeCompleteStatistics allGradeCompleteStatistics = new StoreGradeCompleteStatistics();
        allGradeCompleteStatistics.setGrade("ALL");
        allGradeCompleteStatistics.setCount(totalCount);
        allGradeCompleteStatistics.setPercentage(1.0);
        storeGradeCompleteStatistics.add(allGradeCompleteStatistics);

        return storeGradeCompleteStatistics;
    }

    private String getStoreGrade(Integer capa, StoreGradeRule storeGradeRule) {
        if (Objects.isNull(storeGradeRule)) {
            return null;
        }
        if (capa >= storeGradeRule.getSMinCount()) {
            return "S";
        }
        if (capa >= storeGradeRule.getAMinCount()) {
            return "A";
        }
        if (capa >= storeGradeRule.getBMinCount()) {
            return "B";
        }
        if (capa >= storeGradeRule.getCMinCount()) {
            return "C";
        }
        if (capa >= storeGradeRule.getDMinCount()) {
            return "D";
        }
        return null;
    }

    @NotNull
    private CommonApproveLog buildCommonApproveLog(StoreGradeRuleReq storeGradeRuleReq, String user,
                                                   StoreGradeRule oldStoreGradeRule, StoreGradeRule storeGradeRule) {
        CommonApproveLog commonApproveLog = null;
        LambdaQueryWrapper<CommonApproveLog> queryWrapper = Wrappers.lambdaQuery();
        if (storeGradeRule.getId() != null) {
            queryWrapper.eq(CommonApproveLog::getBusinessId, storeGradeRule.getId());
        }
        if (storeGradeRuleReq.getRuleLogId() != null) {
            queryWrapper.eq(CommonApproveLog::getId, storeGradeRuleReq.getRuleLogId());
        }
        queryWrapper.eq(CommonApproveLog::getFlowStatus, ApprovalStatus.DRAFT.getCode());
        queryWrapper.eq(CommonApproveLog::getBusinessKey, ApprovalBusinessKeyEnum.STORE_GRADE_RULE.getValue())
                .orderByDesc(CommonApproveLog::getId);
        List<CommonApproveLog> commonApproveLogs = commonApproveLogMapper.selectList(queryWrapper);
        if (!CollectionUtils.isEmpty(commonApproveLogs)) {
            commonApproveLog = commonApproveLogs.get(0);
        } else {
            commonApproveLog = new CommonApproveLog();
        }

        if (Objects.nonNull(commonApproveLog.getId())) {
            commonApproveLog.setTargetBody(JsonUtils.toStr(storeGradeRule));
        } else {
            commonApproveLog.setBusinessKey(ApprovalBusinessKeyEnum.STORE_GRADE_RULE.getValue());
            commonApproveLog.setBusinessId(storeGradeRule.getId().toString());
            commonApproveLog.setCreatedAt(LocalDateTime.now());
            commonApproveLog.setUpdatedAt(LocalDateTime.now());
            commonApproveLog.setApplicationTime(LocalDateTime.now());
            commonApproveLog.setTargetBody(JsonUtils.toStr(storeGradeRule));
            if (Objects.nonNull(oldStoreGradeRule)) {
                commonApproveLog.setOriginBody(JsonUtils.toStr(oldStoreGradeRule));
            }
        }
        commonApproveLog.setCreatedBy(user);
        return commonApproveLog;
    }

    @NotNull
    private StoreGradeRule buildStoreGradeRule(StoreGradeRuleReq storeGradeRuleReq, String user,
                                               StoreGradeRule oldStoreGradeRule, boolean submitFlag) {
        StoreGradeRule storeGradeRule = new StoreGradeRule();
        // 复制基本属性
        BeanUtils.copyProperties(storeGradeRuleReq, storeGradeRule);

        storeGradeRule.setMethod(storeGradeRuleReq.getModificationMethod());
        // 设置零售商信息
        if (Objects.nonNull(storeGradeRuleReq.getRetailerCode())) {
            LambdaQueryWrapper<IntlRmsRetailer> retailerQueryWrapper = Wrappers.lambdaQuery();
            retailerQueryWrapper.eq(IntlRmsRetailer::getName, storeGradeRuleReq.getRetailerCode());
            List<IntlRmsRetailer> intlRmsRetailers = intlRmsRetailerMapper.selectList(retailerQueryWrapper);
            if (CollectionUtils.isEmpty(intlRmsRetailers)) {
                log.error("Retailer information is empty");
                throw new BizException(ErrorCodes.BIZ_ERROR, "Retailer information is empty");
            }
            storeGradeRuleReq.setRetailerName(intlRmsRetailers.get(0).getRetailerName());
            StoreGradeRuleReq.Retailer retailer = new StoreGradeRuleReq.Retailer();
            retailer.setName(intlRmsRetailers.get(0).getRetailerName());
            retailer.setCode(storeGradeRuleReq.getRetailerCode());
            storeGradeRuleReq.setRetailer(retailer);
            storeGradeRule.setRetailerName(intlRmsRetailers.get(0).getRetailerName());
            storeGradeRule.setRetailerCode(storeGradeRuleReq.getRetailerCode());
        }

        if (Objects.isNull(oldStoreGradeRule) ||
                !Objects.equals(RuleStatusEnum.IN_EFFECT.getCode(), oldStoreGradeRule.getRulesStatus())) {
            storeGradeRule.setRulesStatus(RuleStatusEnum.NOT_IN_EFFECT.getCode());
        }
        if (submitFlag) {
            // 设置规则状态为待审核
            storeGradeRule.setApplicationTime(LocalDateTime.now());
            storeGradeRule.setApproveStatus(ApprovalStatus.IN_APPROVAL.getCode());
        } else {
            // 待保存状态
            storeGradeRule.setApproveStatus(ApprovalStatus.DRAFT.getCode());
        }
        storeGradeRule.setApprovedTime(null);
        storeGradeRule.setCreateBy(user);
        storeGradeRule.setUpdateBy(user);
        if (Objects.nonNull(storeGradeRuleReq.getFileData())) {
            storeGradeRule.setFileData(JsonUtils.toStr(storeGradeRuleReq.getFileData()));
        }
        return storeGradeRule;
    }

    private void fillRetailInfo(StoreGradeRuleReq storeGradeRuleReq, StoreGradeRule storeGradeRule) {
        LambdaQueryWrapper<IntlRmsRetailer> retailerQueryWrapper = Wrappers.lambdaQuery();
        retailerQueryWrapper.eq(IntlRmsRetailer::getName, storeGradeRuleReq.getRetailerCode());
        List<IntlRmsRetailer> intlRmsRetailers = intlRmsRetailerMapper.selectList(retailerQueryWrapper);
        if (CollectionUtils.isEmpty(intlRmsRetailers)) {
            log.error("Retailer information is empty");
            throw new BizException(ErrorCodes.BIZ_ERROR, "Retailer information is empty");
        }
        storeGradeRuleReq.setRetailerName(intlRmsRetailers.get(0).getRetailerName());
        StoreGradeRuleReq.Retailer retailer = new StoreGradeRuleReq.Retailer();
        retailer.setName(intlRmsRetailers.get(0).getRetailerName());
        retailer.setCode(storeGradeRuleReq.getRetailerCode());
        storeGradeRuleReq.setRetailer(retailer);
        storeGradeRule.setRetailerName(intlRmsRetailers.get(0).getRetailerName());
        storeGradeRule.setRetailerCode(storeGradeRuleReq.getRetailerCode());
    }

    @Nullable
    private StoreGradeRule checkReSubmit(StoreGradeRuleReq storeGradeRuleReq) {
        StoreGradeRule oldStoreGradeRule = null;
        if (Objects.equals(ChannelTypeEnum.IR.getKey(), storeGradeRuleReq.getChannelType())) {
            LambdaQueryWrapper<StoreGradeRule> queryWrapper = Wrappers.lambdaQuery();
            queryWrapper.eq(StoreGradeRule::getCountryCode, storeGradeRuleReq.getCountryCode())
                    .eq(StoreGradeRule::getChannelType, storeGradeRuleReq.getChannelType())
                    .eq(BaseCountryEntity::getIsDeleted, 0);
            if (Objects.nonNull(storeGradeRuleReq.getId())) {
                queryWrapper.ne(StoreGradeRule::getId, storeGradeRuleReq.getId());
            }

            StoreGradeRule existingRule = this.getOne(queryWrapper);
            if (existingRule != null) {
                log.warn("IR rule already exists for current country {}, skipping save",
                        storeGradeRuleReq.getCountryCode());
                throw new BizException(ErrorCodes.BIZ_ERROR, "IR rule already exists for current country");
            }
        } else if (Objects.nonNull(storeGradeRuleReq.getChannelType()) &&
                Objects.nonNull(storeGradeRuleReq.getRetailerCode())) {
            LambdaQueryWrapper<StoreGradeRule> queryWrapper = Wrappers.lambdaQuery();
            queryWrapper.eq(StoreGradeRule::getRetailerCode, storeGradeRuleReq.getRetailerCode())
                    .eq(StoreGradeRule::getChannelType, storeGradeRuleReq.getChannelType())
                    .eq(BaseCountryEntity::getIsDeleted, 0);
            if (Objects.nonNull(storeGradeRuleReq.getId())) {
                queryWrapper.ne(StoreGradeRule::getId, storeGradeRuleReq.getId());
            }

            StoreGradeRule existingRule = this.getOne(queryWrapper);
            if (existingRule != null) {
                log.warn("Rule already exists for retailer code {}, skipping save",
                        storeGradeRuleReq.getRetailerCode());
                throw new BizException(ErrorCodes.BIZ_ERROR, "Rule already exists for retailer code");
            }
        }
        if (Objects.nonNull(storeGradeRuleReq.getId())) {
            oldStoreGradeRule = this.getById(storeGradeRuleReq.getId());
        }
        if (Objects.nonNull(oldStoreGradeRule) &&
                Objects.equals(oldStoreGradeRule.getApproveStatus(), ApprovalStatus.IN_APPROVAL.getCode())) {
            log.warn("The current rule is under approval, please do not add duplicate");
            throw new BizException(ErrorCodes.BIZ_ERROR,
                    "The current rule is under approval, please do not add duplicate");
        }
        if (Objects.nonNull(oldStoreGradeRule) &&
                Objects.equals(oldStoreGradeRule.getRulesStatus(), RuleStatusEnum.IN_EFFECT.getCode())) {
            if (Objects.equals(oldStoreGradeRule.getMethod(),
                    TieringModificationMethodEnum.SYSTEM_CALCULATION.getKey()) &&
                    Objects.equals(storeGradeRuleReq.getModificationMethod(),
                            TieringModificationMethodEnum.MANUAL_UPLOAD.getKey())) {
                log.error("Cannot modify from system calculation to manual upload");
                throw new BizException(ErrorCodes.BIZ_ERROR, "Cannot modify from system calculation to manual upload");
            }
        }
        return oldStoreGradeRule;
    }

    @Override
    public CommonApiResponse<Integer> check(StoreGradeRuleReq storeGradeRuleReq) {
        if (Objects.isNull(storeGradeRuleReq) || Objects.isNull(storeGradeRuleReq.getRetailerCode())) {
            return CommonApiResponse.failure(500, "Retailer code cannot be empty");
        }
        LambdaQueryWrapper<StoreGradeRule> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(StoreGradeRule::getRetailerCode, storeGradeRuleReq.getRetailerCode())
                .eq(BaseCountryEntity::getIsDeleted, 0);

        List<StoreGradeRule> ruleList = this.list(queryWrapper);
        return new CommonApiResponse<>(CollectionUtils.isEmpty(ruleList) ? 1 : 2);
    }

    @Override
    public CommonApiResponse<StoreGradeRuleDetailResp.RuleDetails> query(RuleQueryReq request) {
        try {
            if (request == null || (request.getRuleId() == null && request.getRuleLogId() == null)) {
                return null;
            }
            LambdaQueryWrapper<CommonApproveLog> queryWrapper = Wrappers.lambdaQuery();
            if (request.getRuleId() != null) {
                queryWrapper.eq(CommonApproveLog::getBusinessId, request.getRuleId());
            }
            if (request.getRuleLogId() != null) {
                queryWrapper.eq(CommonApproveLog::getId, request.getRuleLogId());
            }
            queryWrapper.eq(CommonApproveLog::getBusinessKey, ApprovalBusinessKeyEnum.STORE_GRADE_RULE.getValue())
                    .orderByDesc(CommonApproveLog::getId);
            List<CommonApproveLog> commonApproveLogs = commonApproveLogMapper.selectList(queryWrapper);
            if (CollectionUtils.isEmpty(commonApproveLogs)) {
                return null;
            }
            StoreGradeRule storeGradeRule =
                    JsonUtils.toObject(commonApproveLogs.get(0).getTargetBody(), StoreGradeRule.class);
            StoreGradeRuleDetailResp.RuleDetails ruleDetails = buildRuleDetails(storeGradeRule, Boolean.TRUE);
            return new CommonApiResponse<>(ruleDetails);
        } catch (Exception e) {
            log.error("Failed to query store grade rule", e);
            return CommonApiResponse.failure(500, e.getMessage());
        }
    }

    @Override
    public CommonApiResponse<StoreGradeRuleDetailResp.RuleDetails> queryDetail(RuleQueryReq request) {
        try {
            log.info("Start querying store grade rule details, request parameters: {}", request);

            if (request == null || request.getRuleId() == null) {
                throw new BizException(ErrorCodes.BIZ_ERROR, "Parameter error");
            }

            // 查询规则表填充基础信息
            LambdaQueryWrapper<StoreGradeRule> queryWrapper = Wrappers.lambdaQuery();
            queryWrapper.eq(StoreGradeRule::getId, request.getRuleId());

            StoreGradeRule storeGradeRule = this.getOne(queryWrapper);
            if (storeGradeRule == null) {
                throw new BizException(ErrorCodes.BIZ_ERROR, "Corresponding store grade rule not found");
            }

            // 构建规则详情
            StoreGradeRuleDetailResp.RuleDetails ruleDetails = buildRuleDetails(storeGradeRule, Boolean.FALSE);

            log.info("Query store grade rule details completed, rule ID: {}", request.getRuleId());
            return new CommonApiResponse<>(ruleDetails);

        } catch (Exception e) {
            log.error("Failed to query store grade rule details", e);
            return CommonApiResponse.failure(500, e.getMessage());
        }
    }

    @Override
    public CommonApiResponse<StoreGradeRuleDetailResp.RuleDetails> queryDraft(RuleQueryReq request) {
        try {
            log.info("Start querying store grade rule details, request parameters: {}", request);

            if (request == null || request.getRuleId() == null) {
                throw new BizException(ErrorCodes.BIZ_ERROR, "Parameter error");
            }

            // 查询规则表填充基础信息
            LambdaQueryWrapper<StoreGradeRule> queryWrapper = Wrappers.lambdaQuery();
            queryWrapper.eq(StoreGradeRule::getId, request.getRuleId());

            StoreGradeRule storeGradeRule = this.getOne(queryWrapper);
            if (storeGradeRule == null) {
                throw new BizException(ErrorCodes.BIZ_ERROR, "Corresponding store grade rule not found");
            }
            if (!Objects.equals(ApprovalStatus.APPROVED.getCode(), storeGradeRule.getApproveStatus())) {
                LambdaQueryWrapper<CommonApproveLog> logQueryWrapper = Wrappers.lambdaQuery();
                logQueryWrapper.eq(CommonApproveLog::getBusinessKey,
                                ApprovalBusinessKeyEnum.STORE_GRADE_RULE.getValue())
                        .eq(CommonApproveLog::getBusinessId, request.getRuleId().toString())
                        .orderByDesc(CommonApproveLog::getId);
                List<CommonApproveLog> commonApproveLogs = commonApproveLogMapper.selectList(logQueryWrapper);
                if (CollectionUtils.isEmpty(commonApproveLogs)) {
                    throw new BizException(ErrorCodes.BIZ_ERROR, "Corresponding store grade rule not found");
                }
                storeGradeRule = JsonUtils.toObject(commonApproveLogs.get(0).getTargetBody(), StoreGradeRule.class);
            }

            StoreGradeRuleDetailResp.RuleDetails ruleDetails = buildRuleDetails(storeGradeRule, Boolean.FALSE);
            // 构建规则详情

            log.info("Query store grade rule details completed, rule ID: {}", request.getRuleId());
            return new CommonApiResponse<>(ruleDetails);

        } catch (Exception e) {
            log.error("Failed to query store grade rule details", e);
            return CommonApiResponse.failure(500, e.getMessage());
        }
    }

    @Override
    public CommonApiResponse<Page<RuleModificationLog>> queryApprovalList(RuleQueryReq request) {
        try {
            log.info("Start querying store grade rule approval list, request parameters: {}", request);

            if (request == null || request.getRuleId() == null) {
                throw new BizException(ErrorCodes.BIZ_ERROR, "Parameter error");
            }

            // 创建分页对象
            Page<CommonApproveLog> page = new Page<>(request.getPageNum(), request.getPageSize());

            // 查询审批日志表填充修改记录
            LambdaQueryWrapper<CommonApproveLog> logQueryWrapper = Wrappers.lambdaQuery();
            logQueryWrapper.eq(CommonApproveLog::getBusinessKey, ApprovalBusinessKeyEnum.STORE_GRADE_RULE.getValue())
                    .eq(CommonApproveLog::getBusinessId, request.getRuleId().toString())
                    .orderByDesc(CommonApproveLog::getCreatedAt);

            Page<CommonApproveLog> approveLogPage = commonApproveLogMapper.selectPage(page, logQueryWrapper);

            // 转换为RuleModificationLog列表
            Page<RuleModificationLog> resultPage =
                    new Page<>(approveLogPage.getCurrent(), approveLogPage.getSize(), approveLogPage.getTotal());
            List<RuleModificationLog> modificationLogs = new ArrayList<>();

            List<Long> midList =
                    approveLogPage.getRecords().stream().map(CommonApproveLog::getCreatedBy).filter(Objects::nonNull)
                            .filter(str -> str.matches("\\d+"))  // 确保字符串只包含数字
                            .map(Long::valueOf).collect(Collectors.toList());
            Map<String, UserSensitiveInfoResp> userInfo = getUserInfo(midList);
            for (CommonApproveLog approveLog : approveLogPage.getRecords()) {
                RuleModificationLog modificationLog = new RuleModificationLog();

                modificationLog.setLogId(approveLog.getId());
                modificationLog.setChangedBy(approveLog.getCreatedBy());
                modificationLog.setApproveStatus(approveLog.getFlowStatus());
                modificationLog.setApproveStatusDesc(ApprovalStatus.getDescByCode(approveLog.getFlowStatus()));
                modificationLog.setApproveId(approveLog.getFlowInstId());
                String changeTime = IntlTimeUtil.parseTimestampToAreaTime(request.getCountryCode(),
                        DateTimeUtil.toTimestampMillis(approveLog.getCreatedAt()));
                modificationLog.setChangedTime(changeTime);
                modificationLog.setChangedByName(userInfo.get(approveLog.getCreatedBy()) == null ? null :
                        userInfo.get(approveLog.getCreatedBy()).getName());

                // 解析原始数据和新数据
                if (StrUtil.isNotBlank(approveLog.getOriginBody())) {
                    StoreGradeRule originRule = JsonUtils.toObject(approveLog.getOriginBody(), StoreGradeRule.class);
                    modificationLog.setOldValues(buildRuleValues(originRule));
                }

                if (StrUtil.isNotBlank(approveLog.getTargetBody())) {
                    StoreGradeRule targetRule = JsonUtils.toObject(approveLog.getTargetBody(), StoreGradeRule.class);
                    modificationLog.setNewValues(buildRuleValues(targetRule));
                }

                modificationLogs.add(modificationLog);
            }

            resultPage.setRecords(modificationLogs);
            log.info("Query store grade rule approval list completed, rule ID: {}", request.getRuleId());
            return new CommonApiResponse<>(resultPage);

        } catch (Exception e) {
            log.error("Failed to query store grade rule approval list", e);
            return CommonApiResponse.failure(500, e.getMessage());
        }
    }

    private Map<String, UserSensitiveInfoResp> getUserInfo(List<Long> midList) {
        if (CollectionUtil.isEmpty(midList)) {
            return Collections.emptyMap();
        }
        SearchUserSensitiveInfoRequest userRequest = new SearchUserSensitiveInfoRequest();
        userRequest.setMiIdList(midList);
        Result<List<UserSensitiveInfoResp>> listResult = userAdminProvider.searchUserSensitiveInfo(userRequest);
        if (listResult == null || listResult.getCode() != 0) {
            return Collections.emptyMap();
        }
        if (!CollectionUtil.isEmpty(listResult.getData())) {
            // 将用户信息按miId分组，一个miId可能对应多个岗位(positionId)
            // 修改返回类型为Map<String, UserSensitiveInfoResp>
            return listResult.getData().stream()
                    .collect(Collectors.toMap(user -> String.valueOf(user.getMiId()), user -> user));
        }
        return Collections.emptyMap();
    }

    /**
     * 构建规则详情
     *
     * @param storeGradeRule 门店等级规则实体
     * @return 规则详情
     */
    private StoreGradeRuleDetailResp.RuleDetails buildRuleDetails(StoreGradeRule storeGradeRule, Boolean isHistory) {
        StoreGradeRuleDetailResp.RuleDetails ruleDetails = new StoreGradeRuleDetailResp.RuleDetails();

        // 设置基本信息
        ruleDetails.setId(storeGradeRule.getId());
        ruleDetails.setChannelType(storeGradeRule.getChannelType());
        ruleDetails.setChannelTypeDesc(ChannelTypeEnum.getDescByCode(storeGradeRule.getChannelType()));
        ruleDetails.setModificationMethod(storeGradeRule.getMethod());
        ruleDetails.setModificationMethodDesc(TieringModificationMethodEnum.getValueByCode(storeGradeRule.getMethod()));
        ruleDetails.setRuleStatus(storeGradeRule.getRulesStatus());
        ruleDetails.setRuleStatusDesc(RuleStatusEnum.getDescByCode(storeGradeRule.getRulesStatus()));
        ruleDetails.setApprovalStatus(storeGradeRule.getApproveStatus());
        ruleDetails.setApprovalStatusDesc(ApprovalStatus.getDescByCode(storeGradeRule.getApproveStatus()));

        // 设置数量信息
        ruleDetails.setSMinCount(storeGradeRule.getSMinCount() != null ? storeGradeRule.getSMinCount() : null);
        ruleDetails.setAMinCount(storeGradeRule.getAMinCount() != null ? storeGradeRule.getAMinCount() : null);
        ruleDetails.setBMinCount(storeGradeRule.getBMinCount() != null ? storeGradeRule.getBMinCount() : null);
        ruleDetails.setCMinCount(storeGradeRule.getCMinCount() != null ? storeGradeRule.getCMinCount() : null);
        ruleDetails.setDMinCount(storeGradeRule.getDMinCount() != null ? storeGradeRule.getDMinCount() : null);

        // 设置零售商信息
        ruleDetails.setRetailerCode(storeGradeRule.getRetailerCode());
        ruleDetails.setRetailerName(storeGradeRule.getRetailerName());

        ruleDetails.setStoreGradeCompleteList(buildStoreGradeStatistics(storeGradeRule, isHistory));
        ruleDetails.setFileData(JsonUtils.toObject(storeGradeRule.getFileData(), StoreGradeRuleReq.FileData.class));

        return ruleDetails;
    }

    /**
     * 构建门店等级统计信息
     *
     * @param storeGradeRule 门店等级规则
     * @param isHistory 是否为历史数据
     * @return 门店等级统计列表
     */
    private List<StoreGradeCompleteStatistics> buildStoreGradeStatistics(StoreGradeRule storeGradeRule,
                                                                         Boolean isHistory) {
        Integer method = storeGradeRule.getMethod();

        // 判断是否应该使用预计算数据
        boolean shouldUsePreCalculationData = shouldUsePreCalculationData(storeGradeRule, isHistory);

        if (TieringModificationMethodEnum.SYSTEM_CALCULATION.getKey().equals(method)) {
            // 系统计算方式
            if (shouldUsePreCalculationData) {
                return JacksonUtil.parseArray(storeGradeRule.getPreCalculationData(),
                        StoreGradeCompleteStatistics.class);
            } else {
                return getCurrentGradeStatistics(storeGradeRule);
            }
        } else if (TieringModificationMethodEnum.MANUAL_UPLOAD.getKey().equals(method)) {
            // 手动上传方式
            if (shouldUsePreCalculationData) {
                return JacksonUtil.parseArray(storeGradeRule.getPreCalculationData(),
                        StoreGradeCompleteStatistics.class);
            } else {
                return getRelationGradeStatistics(storeGradeRule);
            }
        }

        // 默认返回空列表
        return new ArrayList<>();
    }

    /**
     * 判断是否应该使用预计算数据
     *
     * @param storeGradeRule 门店等级规则
     * @param isHistory 是否为历史数据
     * @return 是否应该使用预计算数据
     */
    private boolean shouldUsePreCalculationData(StoreGradeRule storeGradeRule, Boolean isHistory) {
        return isHistory || (Objects.equals(RuleStatusEnum.NOT_IN_EFFECT.getCode(), storeGradeRule.getRulesStatus()) &&
                Objects.equals(ApprovalStatus.IN_APPROVAL.getCode(), storeGradeRule.getApproveStatus()));
    }

    /**
     * 基于门店等级关系表统计的逻辑
     *
     * @param storeGradeRule 门店等级规则
     * @return 门店等级统计列表
     */
    private List<StoreGradeCompleteStatistics> getRelationGradeStatistics(StoreGradeRule storeGradeRule) {
        // 从RPC上下文中获取countryCode
        String countryCode = RpcContextUtil.getCurrentAreaId();
        Integer ruleId = storeGradeRule.getId();

        List<StoreGradeCompleteCount> gradeCompleteCounts =
                storeGradeRelationMapper.selectStoreGradeCountByRuleId(ruleId, countryCode);

        List<StoreGradeCompleteStatistics> result = new ArrayList<>();

        // 先计算总数
        Integer totalStoreCount = gradeCompleteCounts.stream().mapToInt(count -> count.getCount().intValue()).sum();

        for (StoreGradeCompleteCount count : gradeCompleteCounts) {
            String grade = count.getGrade() != null ? count.getGrade() : "EMPTY";
            Integer storeCount = count.getCount().intValue();

            // 计算占总数的百分比
            Double percentage = totalStoreCount > 0 ? (double) storeCount / totalStoreCount : 0.0;

            StoreGradeCompleteStatistics statistics =
                    new StoreGradeCompleteStatistics(grade, storeCount, percentage, 0L);

            result.add(statistics);
        }

        // 添加总计统计
        Integer totalCount = result.stream().mapToInt(StoreGradeCompleteStatistics::getCount).sum();

        Double totalPercentage = 1.0; // 总计占总数的100%

        StoreGradeCompleteStatistics totalStatistics =
                new StoreGradeCompleteStatistics("ALL", totalCount, totalPercentage, 0L);

        result.add(0, totalStatistics); // 将总计放在第一位

        return result;
    }

    private List<StoreGradeCompleteStatistics> getCurrentGradeStatistics(StoreGradeRule storeGradeRule) {
        // 从规则中获取渠道类型和渠道代码
        String retailerChannelType = String.valueOf(storeGradeRule.getChannelType());
        String retailerCode = storeGradeRule.getRetailerCode();

        // 从RPC上下文中获取countryCode
        String countryCode = RpcContextUtil.getCurrentAreaId();
        List<StoreGradeCompleteCount> gradeCompleteCounts =
                storeGradeMapper.selectStoreGradeCompleteCount(countryCode, retailerChannelType, retailerCode);

        List<StoreGradeCompleteStatistics> result = new ArrayList<>();

        // 先计算总数
        Integer totalStoreCount = gradeCompleteCounts.stream().mapToInt(count -> count.getCount().intValue()).sum();

        for (StoreGradeCompleteCount count : gradeCompleteCounts) {
            String grade = count.getGrade() != null ? count.getGrade() : "EMPTY";
            Integer storeCount = count.getCount().intValue();

            // 计算占总数的百分比
            Double percentage = totalStoreCount > 0 ? (double) storeCount / totalStoreCount : 0.0;

            StoreGradeCompleteStatistics statistics =
                    new StoreGradeCompleteStatistics(grade, storeCount, percentage, 0L);

            result.add(statistics);
        }

        // 添加总计统计
        Integer totalCount = result.stream().mapToInt(StoreGradeCompleteStatistics::getCount).sum();

        Double totalPercentage = 1.0; // 总计占总数的100%

        StoreGradeCompleteStatistics totalStatistics =
                new StoreGradeCompleteStatistics("ALL", totalCount, totalPercentage, 0L);

        result.add(0, totalStatistics); // 将总计放在第一位

        return result;
    }

    /**
     * 构建修改日志
     *
     * @param request
     * @return 修改日志列表
     */
    private List<RuleModificationLog> buildModificationLogs(RuleQueryReq request) {
        List<RuleModificationLog> modificationLogs = new ArrayList<>();

        try {
            // 查询审批日志
            LambdaQueryWrapper<CommonApproveLog> logQueryWrapper = Wrappers.lambdaQuery();
            logQueryWrapper.eq(CommonApproveLog::getBusinessKey, ApprovalBusinessKeyEnum.STORE_GRADE_RULE.getValue())
                    .eq(CommonApproveLog::getBusinessId, request.getRuleId().toString())
                    .orderByDesc(CommonApproveLog::getCreatedAt);

            List<CommonApproveLog> approveLogs = commonApproveLogMapper.selectList(logQueryWrapper);

            for (CommonApproveLog approveLog : approveLogs) {
                RuleModificationLog modificationLog = new RuleModificationLog();

                modificationLog.setChangedBy(approveLog.getCreatedBy());
                modificationLog.setApproveStatus(approveLog.getFlowStatus());
                modificationLog.setApproveId(approveLog.getFlowInstId());
                //modificationLog.setChangedTime(approveLog.getCreatedAt().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli());

                // 解析原始数据和新数据
                if (StrUtil.isNotBlank(approveLog.getOriginBody())) {
                    StoreGradeRule originRule = JsonUtils.toObject(approveLog.getOriginBody(), StoreGradeRule.class);
                    modificationLog.setOldValues(buildRuleValues(originRule));
                }

                if (StrUtil.isNotBlank(approveLog.getTargetBody())) {
                    StoreGradeRule targetRule = JsonUtils.toObject(approveLog.getTargetBody(), StoreGradeRule.class);
                    modificationLog.setNewValues(buildRuleValues(targetRule));
                }

                modificationLogs.add(modificationLog);
            }

        } catch (Exception e) {
            log.error("Failed to build modification logs", e);
        }

        return modificationLogs;
    }

    /**
     * 构建规则值
     *
     * @param storeGradeRule 门店等级规则实体
     * @return 规则值
     */
    private RuleModificationLog.RuleValues buildRuleValues(StoreGradeRule storeGradeRule) {
        if (storeGradeRule == null) {
            return null;
        }

        RuleModificationLog.RuleValues ruleValues = new RuleModificationLog.RuleValues();
        ruleValues.setSMinCount(storeGradeRule.getSMinCount() != null ? storeGradeRule.getSMinCount() : null);
        ruleValues.setAMinCount(storeGradeRule.getAMinCount() != null ? storeGradeRule.getAMinCount() : null);
        ruleValues.setBMinCount(storeGradeRule.getBMinCount() != null ? storeGradeRule.getBMinCount() : null);
        ruleValues.setCMinCount(storeGradeRule.getCMinCount() != null ? storeGradeRule.getCMinCount() : null);
        ruleValues.setDMinCount(storeGradeRule.getDMinCount() != null ? storeGradeRule.getDMinCount() : null);
        if (Objects.nonNull(storeGradeRule.getFileData())) {
            StoreGradeRuleReq.FileData fileData =
                    JsonUtils.toObject(storeGradeRule.getFileData(), StoreGradeRuleReq.FileData.class);
            ruleValues.setFileData(fileData);
        }
        return ruleValues;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonApiResponse<Boolean> deleteStoreGradeRule(Integer id) {
        try {
            log.info("Start deleting store grade rule, rule ID: {}", id);

            if (id == null) {
                log.error("Rule ID is empty");
                throw new BizException(ErrorCodes.BIZ_ERROR, "Parameter error");
            }

            // 查询现有记录
            StoreGradeRule existingRule = this.getById(id);
            if (existingRule == null) {
                log.error("Store grade rule with ID {} not found", id);
                throw new BizException(ErrorCodes.BIZ_ERROR, "Parameter error");
            }

            // 删除记录
            boolean result = this.removeById(id);

            log.info("Store grade rule deletion completed, deleted record ID: {}", id);
            return new CommonApiResponse<>(result);

        } catch (Exception e) {
            log.error("Failed to delete store grade rule", e);
            throw e;
        }
    }

    /**
     * 流程提交
     */
    @Override
    public SubmitResp submit(StoreGradeRuleReq req) {

        if (ObjectUtil.isEmpty(req) || req.getId() == null || StringUtils.isBlank(req.getCountryCode())) {
            throw new BizException(ErrorCodes.BIZ_ERROR, "The request data is incorrect");
        }
        SubmitResp resp = new SubmitResp();
        String countryCode = req.getCountryCode();
        Map<String, Object> approvers = getApprovers(countryCode);
        setNationalRetailManager(approvers, req);
        StoreGradeRuleFormDataReq formDataReq = getFormDataReq(req);
        if (StringUtils.isBlank(req.getFlowInstId())) {
            String businessKey = UUID.randomUUID().toString().replace("-", "");
            String procInst = createProcInst(approvers, formDataReq, businessKey);
            resp.setFlowInstId(businessKey);
            resp.setRequestApprovalBody(procInst);
        } else {
            String submit = bpmService.submit(req.getFlowInstId(), getAccount(), formDataReq, approvers);
            resp.setFlowInstId(req.getFlowInstId());
            resp.setRequestApprovalBody(submit);
        }
        return resp;
    }

    @Override
    public SubmitResp anewSubmit(StoreGradeRuleReq req) {
        String countryCode = req.getCountryCode();
        Map<String, Object> approvers = getApprovers(countryCode);
        setNationalRetailManager(approvers, req);
        StoreGradeRuleFormDataReq formDataReq = getFormDataReq(req);
        bpmService.submit(req.getFlowInstId(), getAccount(), formDataReq, approvers);
        SubmitResp resp = new SubmitResp();
        return resp;
    }

    /**
     * 设置零售经理参数
     */
    private void setNationalRetailManager(Map<String, Object> approvers, StoreGradeRuleReq req) {
        Object retailManager = approvers.get("retailManager");
        if (ObjectUtil.isNotEmpty(retailManager) && retailManager instanceof List) {
            List<String> retailManagerList = (List<String>) retailManager;
            if (!retailManagerList.isEmpty()) {
                req.setNationalRetailManager(String.join(",", retailManagerList));
            }
        }
    }

    /**
     * 创建流程实例
     *
     * @param approvers
     * @param formDataReq
     */
    private String createProcInst(Map<String, Object> approvers, StoreGradeRuleFormDataReq formDataReq,
                                  String businessKey) {
        //发起流程实例
        String storeGradeRule =
                bpmService.create(formDataReq, BpmApproveBusinessCodeEnum.STORE_GRADE_RULE, getAccount(), businessKey,
                        approvers, formDataReq.getRetailerName());
        log.info("发起流程实例成功，流程实例ID：{}", storeGradeRule);
        return storeGradeRule;
    }

    /**
     * 获取当前登录人信息
     *
     * @return
     */
    private String getAccount() {
        String email = RpcContextUtil.getCurrentEmail();
        if (StringUtils.isBlank(email)) {
            throw new BizException(ErrorCodes.BIZ_ERROR, "The current user is not logged in");
        }
        String emailPrefix = email != null && email.contains("@") ? email.substring(0, email.indexOf("@")) : null;
        if (StringUtils.isBlank(emailPrefix)) {
            throw new BizException(ErrorCodes.BIZ_ERROR, "The current emailPrefix is not logged in");
        }
        return emailPrefix;
    }

    /**
     * 封装表单数据
     *
     * @param req
     * @return
     */
    private StoreGradeRuleFormDataReq getFormDataReq(StoreGradeRuleReq req) {
        StoreGradeRuleFormDataReq formDataReq = new StoreGradeRuleFormDataReq();
        if (req.getRetailer() != null) {
            formDataReq.setRetailerName(req.getRetailer().getName());
            formDataReq.setRetailerCode(req.getRetailer().getCode());
        }
        formDataReq.setManager(req.getNationalRetailManager());
        formDataReq.setChannelType(ChannelTypeEnum.fromKey(req.getChannelType()).getValue());
        formDataReq.setCountry(req.getCountryName());
        formDataReq.setStoreGradeRule(String.format("%s-%s", req.getCountryName(), formDataReq.getChannelType()));
        formDataReq.setApplicationURL(getApplicationURL(req.getId()));
        if (req.getModificationMethod().equals(TieringModificationMethodEnum.SYSTEM_CALCULATION.getKey())) {
            formDataReq.setFlag("1");

            // 根据最小值设置容量范围
            if (req.getSMinCount() != null) {
                formDataReq.setCapaS("≥" + req.getSMinCount());
            }

            if (req.getAMinCount() != null) {
                // 这里需要根据业务逻辑确定最大值
                formDataReq.setCapaA(req.getAMinCount() + "-" + getAMaxCount(req));
            }

            if (req.getBMinCount() != null) {
                formDataReq.setCapaB(req.getBMinCount() + "-" + getBMaxCount(req));
            }

            if (req.getCMinCount() != null) {
                formDataReq.setCapaC(req.getCMinCount() + "-" + getCMaxCount(req));
            }

            if (req.getDMinCount() != null) {
                formDataReq.setCapaD("≤" + getDMaxCount(req));
            }

            formDataReq.setFileLink(req.getFileLink());
        } else if (req.getModificationMethod().equals(TieringModificationMethodEnum.MANUAL_UPLOAD.getKey())) {
            formDataReq.setFlag("3");
            StoreGradeRuleReq.FileData fileData = req.getFileData();
            StoreGradeRuleFormDataReq.UploadObject uploadObject = new StoreGradeRuleFormDataReq.UploadObject();
            uploadObject.setFileName(fileData.getFileName());
            uploadObject.setOriginFileName(fileData.getFileName());
            uploadObject.setUri(fileData.getUrl());
            List<StoreGradeRuleFormDataReq.UploadObject> files = new ArrayList<>();
            files.add(uploadObject);
            formDataReq.setFile(files);
        }

        return formDataReq;
    }

    /**
     * 获取规则申请链接
     *
     * @param ruleId
     * @return
     */
    public String getApplicationURL(Integer ruleId) {
        String routingConfiguration = storeGradeRuleConfig.getConfiguration();
        StringBuilder builder = new StringBuilder();
        builder.append(routingConfiguration).append(RouteConstants.MAIN_GLOBAL_POSITION_PATROL)
                .append(RouteConstants.URL_SEPARATOR).append(RouteConstants.STORE_TIERING)
                .append(RouteConstants.URL_SEPARATOR).append(RouteConstants.DETAIL).append(RouteConstants.QUESTION_MARK)
                .append(RouteConstants.PARAM_ID).append(RouteConstants.EQUALS_SIGN).append(ruleId);
        return builder.toString();
    }

    // 辅助方法，根据业务逻辑确定各等级的最大值
    private Integer getAMaxCount(StoreGradeRuleReq req) {
        return req.getSMinCount() != null ? req.getSMinCount() - 1 : null;
    }

    private Integer getBMaxCount(StoreGradeRuleReq req) {
        return req.getAMinCount() != null ? req.getAMinCount() - 1 : null;
    }

    private Integer getCMaxCount(StoreGradeRuleReq req) {
        return req.getBMinCount() != null ? req.getBMinCount() - 1 : null;
    }

    private Integer getDMaxCount(StoreGradeRuleReq req) {
        return req.getCMinCount() != null ? req.getCMinCount() - 1 : null;
    }

    /**
     * 获取审批人
     *
     * @param countryCode
     * @return
     */
    private Map<String, Object> getApprovers(String countryCode) {
        Map<Integer, List<String>> approvers = new HashMap<>();
        // 零售经理岗位id
        Integer retailManagerCode = storeGradeRuleConfig.getRetailManager();
        // 拓展经理岗位id
        Integer developmentManager = storeGradeRuleConfig.getDevelopmentManager();
        // 总部兜底岗位id
        Integer headquarterBackup = storeGradeRuleConfig.getHeadquarterBackup();
        List<Integer> positionIds = new ArrayList<>();
        positionIds.add(retailManagerCode);
        positionIds.add(developmentManager);
        positionIds.add(headquarterBackup);
        Map<Integer, List<Long>> midMap = new HashMap<>();
        for (Integer positionId : positionIds) {
            GetParentOrganPositionUserReq req = new GetParentOrganPositionUserReq();
            req.setOrganCode(countryCode);
            req.setPositionId(positionId);
            req.setScene(storeGradeRuleConfig.getScene());
            req.setManageChannelList(Arrays.asList(storeGradeRuleConfig.getManageChannelList()));
            Result<List<UserPosition>> result = userProvider.getParentOrganPositionUser(req);
            if (result == null || result.getCode() != 0) {
                throw new BizException(ErrorCodes.BIZ_ERROR, "get appFirstCategoryList error");
            }
            if (!CollectionUtil.isEmpty(result.getData())) {
                List<Long> miIds = result.getData().stream().map(UserPosition::getMiId).collect(Collectors.toList());
                midMap.put(positionId, miIds);
            }
        }
        List<Long> midList = new ArrayList<>();
        midMap.forEach((k, v) -> {
            if (v != null) {
                midList.addAll(v);
            }
        });
        if (!CollectionUtil.isEmpty(midList)) {
            SearchUserSensitiveInfoRequest request = new SearchUserSensitiveInfoRequest();
            request.setMiIdList(midList);
            Result<List<UserSensitiveInfoResp>> listResult = userAdminProvider.searchUserSensitiveInfo(request);
            if (listResult == null || listResult.getCode() != 0) {
                throw new BizException(ErrorCodes.BIZ_ERROR, "get appFirstCategoryList error");
            }
            if (!CollectionUtil.isEmpty(listResult.getData())) {
                // 将用户信息按miId分组，一个miId可能对应多个岗位(positionId)
                Map<Long, UserSensitiveInfoResp> userMap = listResult.getData().stream()
                        .collect(Collectors.toMap(UserSensitiveInfoResp::getMiId, user -> user));

                // 遍历midMap，构建approvers映射
                midMap.forEach((positionId, miIds) -> {
                    if (miIds != null) {
                        List<String> emailPrefixes = new ArrayList<>();
                        for (Long miId : miIds) {
                            UserSensitiveInfoResp user = userMap.get(miId);
                            if (user != null && StringUtils.isNotBlank(user.getEmail())) {
                                String emailPrefix = user.getEmail();
                                if (emailPrefix.contains("@")) {
                                    emailPrefix = emailPrefix.substring(0, emailPrefix.indexOf("@"));
                                }
                                if (emailPrefixes.size() < 4) {
                                    emailPrefixes.add(emailPrefix);
                                }
                            }
                        }
                        approvers.put(positionId, emailPrefixes);
                    }
                });
            }
        }
        Map<String, Object> rsp = new HashMap<>();

        List<String> developmentManagerUser =
                CollectionUtil.isEmpty(approvers.get(developmentManager)) ? approvers.get(headquarterBackup) :
                        approvers.get(developmentManager);
        List<String> retailManagerCodeUser =
                CollectionUtil.isEmpty(approvers.get(retailManagerCode)) ? approvers.get(headquarterBackup) :
                        approvers.get(retailManagerCode);
        rsp.put(CommonConstant.DEVELOPMENT_MANAGER, developmentManagerUser);
        rsp.put(CommonConstant.DEVELOPMENT_MANAGER_CARBON_COPY, developmentManagerUser);
        rsp.put(CommonConstant.RETAIL_MANAGER, retailManagerCodeUser);
        rsp.put(CommonConstant.RETAIL_MANAGER_CARBON_COPY, retailManagerCodeUser);

        List<String> headquartersPositionResult = getHeadquartersPosition(countryCode);
        headquartersPositionResult =
                CollectionUtil.isEmpty(headquartersPositionResult) ? approvers.get(headquarterBackup) :
                        headquartersPositionResult;
        rsp.put(CommonConstant.HEADQUARTERS_POSITION, headquartersPositionResult);
        rsp.put(CommonConstant.HEADQUARTERS_POSITION_CARBON_COPY, headquartersPositionResult);

        return rsp;
    }

    /**
     * 获取总部阵地审批人
     *
     * @param countryCode
     * @return
     */
    private List<String> getHeadquartersPosition(String countryCode) {
        List<String> headquartersPosition = new ArrayList<>();
        CountryRoleAdminReq req = new CountryRoleAdminReq();
        req.setCountry(countryCode);
        req.setRole("BP");
        Result<List<CountryRoleAdminResp>> countryRoleAdmins = channelCommonProvider.getCountryRoleAdmins(req);
        if (countryRoleAdmins == null || countryRoleAdmins.getCode() != 0) {
            throw new BizException(ErrorCodes.BIZ_ERROR, "The acquisition of headquarters position personnel is wrong");
        }
        for (CountryRoleAdminResp countryRoleAdminResp : countryRoleAdmins.getData()) {
            if (headquartersPosition.size() < 4) {
                headquartersPosition.add(countryRoleAdminResp.getAdmin());
            }
        }

        return headquartersPosition;
    }

    /**
     * 流程撤回
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonApiResponse<Boolean> recall(StoreGradeRuleRecallReq req) {
        try {
            if (req.getRuleId() == null) {
                throw new BizException(ErrorCodes.BIZ_ERROR, "The request data is incorrect");
            }
            StoreGradeRule existingRule = this.getById(req.getRuleId());
            if (existingRule == null) {
                throw new BizException(ErrorCodes.BIZ_ERROR,
                        "The store level rule for the corresponding ID was not found");
            }

            if (!existingRule.getApproveStatus().equals(ApprovalStatus.IN_APPROVAL.getCode())) {
                throw new BizException(ErrorCodes.BIZ_ERROR,
                        "The current store tier rule status is not allowed to be withdrawn");
            }

            CommonApproveReq commonApproveReq = new CommonApproveReq();
            commonApproveReq.setBusinessId(existingRule.getId().toString());
            commonApproveReq.setFlowStatus(ApprovalStatus.IN_APPROVAL.getCode());
            CommonApproveLog commonApproveResp = getCommonApproveResp(commonApproveReq);

            if (commonApproveResp == null) {
                throw new BizException(ErrorCodes.BIZ_ERROR,
                        "The current approval record status does not allow retraction");
            }
            recallProcInst(req, commonApproveResp.getFlowInstId());
            commonApproveResp.setFlowStatus(ApprovalStatus.DRAFT.getCode());
            commonApproveLogMapper.updateById(commonApproveResp);
            existingRule.setApproveStatus(ApprovalStatus.DRAFT.getCode());
            this.updateById(existingRule);
            if (!Objects.equals(existingRule.getRulesStatus(), RuleStatusEnum.IN_EFFECT.getCode())) {
                existingRule.setRulesStatus(RuleStatusEnum.NOT_IN_EFFECT.getCode());
            }
            existingRule.setApproveStatus(ApprovalStatus.DRAFT.getCode());
            this.updateById(existingRule);

            LambdaUpdateWrapper<StoreGradeRelation> updateWrapper = Wrappers.lambdaUpdate();
            updateWrapper.eq(StoreGradeRelation::getStoreGradeRuleId, existingRule.getId());
            List<StoreGradeRelation> storeGradeRelations = storeGradeRelationMapper.selectList(updateWrapper);
            if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(storeGradeRelations)) {
                storeGradeRelationMapper.deleteBatchIds(storeGradeRelations);
            }
            return new CommonApiResponse<>(true);
        } catch (BizException e) {
            log.error("StoreGradeRuleServiceImpl.recall error", e);
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return CommonApiResponse.failure(500, e.getMessage());
        }
    }

    /**
     * 流程撤回请求
     *
     * @param req
     */
    private void recallProcInst(StoreGradeRuleRecallReq req, String businessKey) {
        bpmService.recall(businessKey, getAccount(), req.getComment());
        log.info("BPM_RECALL_LOG,businessKey:{}----->user:{}", businessKey, "store_grade_rule");
    }

    /**
     * 根据条件查询审批记录
     *
     * @param
     * @return
     */
    private CommonApproveLog getCommonApproveResp(CommonApproveReq req) {
        LambdaQueryWrapper<CommonApproveLog> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(req.getBusinessId()),
                        CommonApproveLog::getBusinessId, req.getBusinessId())
                .eq(com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(req.getBusinessKey()),
                        CommonApproveLog::getBusinessKey, req.getBusinessKey())
                .eq(com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(req.getFlowInstId()),
                        CommonApproveLog::getFlowInstId, req.getFlowInstId())
                .eq(req != null, CommonApproveLog::getFlowStatus, req.getFlowStatus());
        CommonApproveLog approveLog = this.commonApproveLogMapper.selectOne(queryWrapper);
        return approveLog;
    }

    /**
     * 查看审批记录
     */
    @Override
    public CommonApiResponse<List<CommonApproveHistoryResp>> listProcessLog(CommonApproveHistoryReq req) {
        CommonApproveLog commonApproveResp = commonApproveLogMapper.selectById(req.getRuleLogId());
        if (ObjectUtil.isEmpty(commonApproveResp)) {
            throw new BizException(ErrorCodes.BIZ_ERROR, "No corresponding approval record found");
        }
        StoreGradeRule gradeRule = this.getById(Long.valueOf(commonApproveResp.getBusinessId()));
        List<CommonApproveHistoryResp> resps = new ArrayList<>();
        if (StringUtils.isBlank(commonApproveResp.getFlowInstId())) {
            return new CommonApiResponse<>(resps);
        }
        List<ApprovalTaskListResp> approvalTaskListResps =
                bpmService.listProcessLog(commonApproveResp.getFlowInstId().toString(), null);
        if (!CollectionUtils.isEmpty(approvalTaskListResps)) {
            for (ApprovalTaskListResp resp : approvalTaskListResps) {
                CommonApproveHistoryResp historyResp = new CommonApproveHistoryResp();
                historyResp.setPhase(resp.getTaskName());
                historyResp.setRuleLogId(req.getRuleLogId());
                if (resp.getAssignee() != null) {
                    historyResp.setApprover(resp.getAssignee().getUserName());
                }
                if (resp.getOperation() != null) {
                    historyResp.setApproveStatus(resp.getOperation().getCode());
                }
                historyResp.setComment(resp.getComment());
                ZonedDateTime endTime = resp.getEndTime();
                if (endTime != null) {
                    long endTimeMillis = endTime.toInstant().toEpochMilli();
                    String countryTime =
                            IntlTimeUtil.parseTimestampToAreaDateSecFormat(gradeRule.getCountryCode(), endTimeMillis);
                    historyResp.setApproverTime(countryTime);
                }
                ZonedDateTime createTime = resp.getCreateTime();
                if (createTime != null) {
                    long createTimeMillis = createTime.toInstant().toEpochMilli();
                    String countryTime = IntlTimeUtil.parseTimestampToAreaDateSecFormat(gradeRule.getCountryCode(),
                            createTimeMillis);
                    historyResp.setCreateTime(countryTime);
                }
                resps.add(historyResp);
            }
        }

        return new CommonApiResponse<>(resps);
    }

    @Override
    public CommonApiResponse<StoreGradeResp> queryStoreGrade(StoreGradeReq req) {
        StoreGradeResp resp = new StoreGradeResp();
        if (ObjectUtil.isNotEmpty(req) && StringUtils.isNotBlank(req.getRetailerCode()) && req.getKapa() != null) {
            LambdaQueryWrapper<IntlRmsRetailer> queryWrapper = Wrappers.lambdaQuery();
            queryWrapper.eq(IntlRmsRetailer::getName, req.getRetailerCode());
            IntlRmsRetailer intlRmsRetailer = intlRmsRetailerMapper.selectOne(queryWrapper);

            StoreGradeRule storeGradeRule = null;
            if (intlRmsRetailer == null) {
                throw new BizException(ErrorCodes.BIZ_ERROR, "Current retailers do not exist");
            }

            if (!intlRmsRetailer.getRetailerChannelType().equals(ChannelTypeEnum.IR.getKey())) {

                storeGradeRule = this.lambdaQuery().eq(StoreGradeRule::getRetailerCode, req.getRetailerCode())
                        .eq(StoreGradeRule::getRulesStatus, RuleStatusEnum.IN_EFFECT.getCode()).one();
            } else {

                LambdaQueryWrapper<IntlRmsCountryTimezone> wrapper = Wrappers.lambdaQuery();
                wrapper.eq(IntlRmsCountryTimezone::getCountryId, intlRmsRetailer.getCountryId());
                IntlRmsCountryTimezone intlRmsCountryTimezone = intlRmsCountryTimezoneMapper.selectOne(wrapper);
                if (intlRmsCountryTimezone == null) {
                    throw new BizException(ErrorCodes.BIZ_ERROR, "Country timezone does not exist");
                }
                storeGradeRule =
                        this.lambdaQuery().eq(StoreGradeRule::getCountryCode, intlRmsCountryTimezone.getCountryCode())
                                .eq(StoreGradeRule::getChannelType, ChannelTypeEnum.IR.getKey())
                                .eq(StoreGradeRule::getRulesStatus, RuleStatusEnum.IN_EFFECT.getCode())
                                .orderByDesc(StoreGradeRule::getUpdateTime).last("LIMIT 1").one();
            }

            if (storeGradeRule != null) {
                Long kapa = Long.valueOf(req.getKapa());
                String grade = determineGradeByKapa(kapa, storeGradeRule);
                resp.setStoreGrade(grade);
                resp.setStoreGradeKey(StoreGradeEnum.fromValue(grade).getKey());
                resp.setRetailerCode(req.getRetailerCode());
            }
        }
        return new CommonApiResponse<>(resp);
    }

    @Override
    public SubmitResp batchUpdateStoreGrade(StoreGradeRuleReq req) {
        try {
            StoreGradeRuleReq.FileData fileData = req.getFileData();

            SubmitResp resp = new SubmitResp();
            List<StoreGradeBatchData> dataList = ExcelUtil.parseExcelFile(fileData);
            checkDataList(dataList, req);
            Map<String, Object> approvers = getApprovers(req.getCountryCode());
            setNationalRetailManager(approvers, req);
            StoreGradeRuleFormDataReq formDataReq = getFormDataReq(req);
            if (StringUtils.isBlank(req.getFlowInstId())) {
                String businessKey = UUID.randomUUID().toString().replace("-", "");
                String procInst = createProcInst(approvers, formDataReq, businessKey);
                resp.setFlowInstId(businessKey);
                resp.setRequestApprovalBody(procInst);
            } else {
                String submit = bpmService.submit(req.getFlowInstId(), getAccount(), formDataReq, approvers);
                resp.setFlowInstId(req.getFlowInstId());
                resp.setRequestApprovalBody(submit);
            }

            List<String> storeCodes =
                    dataList.stream().map(StoreGradeBatchData::getStoreCode).collect(Collectors.toList());
            LambdaUpdateWrapper<StoreGradeRelation> updateWrapper = Wrappers.lambdaUpdate();
            updateWrapper.eq(StoreGradeRelation::getStoreGradeRuleId, req.getId());
            updateWrapper.in(StoreGradeRelation::getStoreCode, storeCodes);
            List<StoreGradeRelation> delStoreGradeRelations = storeGradeRelationMapper.selectList(updateWrapper);
            if (!CollectionUtils.isEmpty(delStoreGradeRelations)) {
                this.storeGradeRelationMapper.deleteBatchIds(delStoreGradeRelations);
            }

            List<StoreGradeRelation> storeGradeRelations = new ArrayList<>();
            for (StoreGradeBatchData data : dataList) {
                StoreGradeRelation storeGradeRelation = new StoreGradeRelation();
                storeGradeRelation.setCountryCode(req.getCountryCode());
                storeGradeRelation.setStoreCode(data.getStoreCode());
                storeGradeRelation.setStoreGradeRuleId(req.getId());
                storeGradeRelation.setStoreGrade(data.getStoreTiering());
                storeGradeRelations.add(storeGradeRelation);
            }
            if (!CollectionUtils.isEmpty(storeGradeRelations)) {
                this.storeGradeRelationMapper.insertBatch(storeGradeRelations);
            }
            return resp;
        } catch (Exception e) {
            throw new BizException(ErrorCodes.BIZ_ERROR, e);
        }
    }

    /**
     * 判断数据是否合法
     */
    private void checkDataList(List<StoreGradeBatchData> dataList, StoreGradeRuleReq req) {
        if (CollectionUtil.isEmpty(dataList)) {
            throw new BizException(ErrorCodes.BIZ_ERROR, "No data found in the Excel file");
        }
        dataList.forEach(data -> {
            StoreGradeEnum.fromValue(data.getStoreTiering());
        });
        String firstRetailerCode = dataList.get(0).getRetailerCode();
        if (StringUtils.isNotBlank(req.getRetailerCode())) {
            boolean allSameRetailer =
                    dataList.stream().allMatch(data -> firstRetailerCode.equals(data.getRetailerCode()));
            if (!allSameRetailer) {
                throw new BizException(ErrorCodes.BIZ_ERROR, "The retailer code in the Excel file is not consistent");
            }
            String retailerCode = dataList.get(0).getRetailerCode();
            if (!retailerCode.equals(req.getRetailerCode())) {
                throw new BizException(ErrorCodes.BIZ_ERROR,
                        "The retailer in the Excel file is different from the one you selected");
            }
            LambdaQueryWrapper<IntlRmsRetailer> queryRetailerWrapper = Wrappers.lambdaQuery();
            queryRetailerWrapper.eq(IntlRmsRetailer::getName, req.getRetailerCode());
            IntlRmsRetailer retailer = intlRmsRetailerMapper.selectOne(queryRetailerWrapper);
            if (retailer.getRetailerChannelType() == null) {
                throw new BizException(ErrorCodes.BIZ_ERROR,
                        "This Retailer's Channel Type is empty in RMS, " + "please maintain it in RMS-retailer");
            }

            if (!retailer.getRetailerChannelType().equals(req.getChannelType())) {
                throw new BizException(ErrorCodes.BIZ_ERROR,
                        "This Retailer's Channel Type is not consistent with the Channel Type " +
                                "selected in the rule");
            }
        }

        // 获取dataList中的storeCode集合
        List<String> storeCodes =
                dataList.stream().map(StoreGradeBatchData::getStoreCode).filter(StringUtils::isNotBlank)
                        .collect(Collectors.toList());

        if (CollectionUtil.isNotEmpty(storeCodes)) {
            // 根据storeCode集合查询门店信息
            LambdaQueryWrapper<IntlRmsStore> queryStoreWrapper = Wrappers.lambdaQuery();
            queryStoreWrapper.in(IntlRmsStore::getCrssCode, storeCodes);
            List<IntlRmsStore> stores = intlRmsStoreMapper.selectList(queryStoreWrapper);

            // 检查是否有未找到的门店
            if (stores.size() != storeCodes.size()) {
                throw new BizException(ErrorCodes.BIZ_ERROR, "Some store codes in the Excel file could not be found");
            }

            // 检查所有门店的retailerId是否与请求中的retailerCode对应的retailer一致
            for (IntlRmsStore store : stores) {
                if (!req.getRetailerCode().equals(store.getRetailerIdName())) {
                    throw new BizException(ErrorCodes.BIZ_ERROR,
                            "The stores in the Excel file does not belong to the retailer you selected");
                }
            }
        }
    }

    @Override
    public CommonApiResponse<String> getFileTemplate(FileTemplateReq req) {
        String url = "";
        if (req.getType().equals(FileTemplateEnum.STORE_GRADE_RULE_TEMPLATE.getCode())) {
            url = storeGradeRuleConfig.getStoreGradeRule();
        }
        return new CommonApiResponse<>(url);
    }

    @Override
    public CommonApiResponse<List<RetailerQueryResp>> queryRetailerList(RetailerQueryReq request) {
        String countryId = "";
        if (StringUtils.isNotBlank(request.getCountryCode())) {
            IntlRmsCountryTimezone countryByCode = getCountryByCode(request.getCountryCode());
            if (countryByCode == null) {
                throw new BizException(ErrorCodes.BIZ_ERROR, "Country code is not found");
            }
            countryId = countryByCode.getCountryId();
        }

        List<RetailerQueryResp> resps = new ArrayList<>();
        LambdaQueryWrapper<IntlRmsRetailer> queryRetailerWrapper = Wrappers.lambdaQuery();
        queryRetailerWrapper.eq(StringUtils.isNotBlank(countryId), IntlRmsRetailer::getCountryId, countryId);
        queryRetailerWrapper.eq(request.getChannelType() != null, IntlRmsRetailer::getRetailerChannelType,
                request.getChannelType());
        if (StringUtils.isNotBlank(request.getSearch())) {
            queryRetailerWrapper.nested(wrapper -> wrapper.like(IntlRmsRetailer::getName, request.getSearch()).or()
                    .like(IntlRmsRetailer::getRetailerName, request.getSearch()));
        }
        queryRetailerWrapper.last("LIMIT 50");
        List<IntlRmsRetailer> retailers = intlRmsRetailerMapper.selectList(queryRetailerWrapper);
        if (CollectionUtil.isEmpty(retailers)) {
            return new CommonApiResponse<>(resps);
        }
        for (IntlRmsRetailer retailer : retailers) {
            RetailerQueryResp resp = new RetailerQueryResp();
            resp.setRetailerCode(retailer.getName());
            resp.setCrmCode(retailer.getCrmCode());
            resp.setRetailerName(retailer.getRetailerName());
            resp.setCountryId(retailer.getCountryId());
            resp.setCountryName(retailer.getCountryName());
            resp.setCountryCode(retailer.getCountryCode());
            resp.setChannelType(retailer.getRetailerChannelType());
            resps.add(resp);
        }

        return new CommonApiResponse<>(resps);
    }

    @Override
    public CommonApiResponse<UploadManuallyRuleDetailsResp> getUploadManuallyRuleDetails(Long ruleId) {

        UploadManuallyRuleDetailsResp detailsResp = new UploadManuallyRuleDetailsResp();
        StoreGradeRule rule = baseMapper.selectById(ruleId);
        if (rule == null) {
            throw new BizException(ErrorCodes.BIZ_ERROR, "Invalid ruleId: " + ruleId);
        }
        DetailsTopmostResp topmostResp = new DetailsTopmostResp();
        topmostResp.setChannelType(String.valueOf(rule.getChannelType()));
        StoreGradeRuleReq.FileData fileData = JsonUtils.toObject(rule.getFileData(), StoreGradeRuleReq.FileData.class);
        topmostResp.setExcelFile(Objects.nonNull(fileData) ? fileData.getUrl() : null);
        topmostResp.setRetailerCode(rule.getRetailerCode());
        topmostResp.setRetailerName(rule.getRetailerName());
        topmostResp.setId(ruleId);
        // 除了生效中，别的状态都为未生效
        String rulesCurrentStatus =
                rule.getRulesStatus().equals(RuleStatusEnum.IN_EFFECT.getCode()) ? RuleStatusEnum.IN_EFFECT.getValue() :
                        "Not In Effect";
        topmostResp.setRulesCurrentStatus(rulesCurrentStatus);
        detailsResp.setDetailsTopmostResp(topmostResp);

        List<DetailsBelowResp> detailsBelowRespList = new ArrayList<>();
        LambdaQueryWrapper<CommonApproveLog> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(CommonApproveLog::getBusinessId, ruleId);
        queryWrapper.orderByAsc(CommonApproveLog::getId);
        List<CommonApproveLog> logs = commonApproveLogMapper.selectList(queryWrapper);
        String createBy = rule.getCreateBy();
        LocalDateTime applicationTime = rule.getApplicationTime();
        if (CollectionUtil.isEmpty(logs)) {
            DetailsBelowResp belowResp = new DetailsBelowResp();
            belowResp.setChangedBy(createBy);
            belowResp.setChangedTime(applicationTime);
            belowResp.setNewValues(Objects.nonNull(fileData) ? fileData.getUrl() : null);
            detailsBelowRespList.add(belowResp);
        } else {
            String oldValues = null;
            for (CommonApproveLog log : logs) {
                DetailsBelowResp belowResp = new DetailsBelowResp();
                belowResp.setChangedBy(createBy);
                belowResp.setChangedTime(applicationTime);
                String originBody = log.getOriginBody();
                if (StrUtil.isNotBlank(originBody)) {
                    Map<String, Object> originMap = JacksonUtil.parseObj(originBody, Map.class);
                    String url = (String) originMap.get("fileUrl");
                    belowResp.setOldValues(oldValues);
                    belowResp.setNewValues(url);
                    oldValues = url;
                }
                detailsBelowRespList.add(belowResp);
            }
        }
        detailsResp.setDetailsBelowRespList(detailsBelowRespList);

        return new CommonApiResponse<>(detailsResp);
    }

    @Override
    public CommonApiResponse<String> exportStoreGradeRule(StoreGradeExportReq request) {
        if (StringUtils.isNotBlank(request.getCountryCode())) {
            IntlRmsCountryTimezone countryByCode = getCountryByCode(request.getCountryCode());
            if (countryByCode == null) {
                throw new BizException(ErrorCodes.BIZ_ERROR, "Country code is not found");
            }
            request.setCountryId(countryByCode.getCountryId());
        }
        if (request.getRuleId() != null) {
            StoreGradeRule gradeRule = this.getById(request.getRuleId());
            if (gradeRule == null) {
                throw new BizException(ErrorCodes.BIZ_ERROR, "The current record rule ID is incorrect");
            }
            if (!gradeRule.getChannelType().equals(ChannelTypeEnum.IR.getKey())) {
                request.setRetailerCode(gradeRule.getRetailerCode());
            } else {
                // 获取ir下所有零售商编码
                List<String> retailerCodes = getRetailerCodes(request.getCountryId(), ChannelTypeEnum.IR.getKey());
                request.setRetailerCodes(retailerCodes);
            }
        }

        Page<IntlRmsStore> page = new Page<>(1, 10);
        Page<IntlRmsStore> rmsStorePage = getRmsStorePage(page, request);
        if (CollectionUtils.isEmpty(rmsStorePage.getRecords())) {
            return CommonApiResponse.failure(1, "No store data found");
        }

        TriggerJobRequestDTO jobReq = new TriggerJobRequestDTO();
        String account = RpcContextUtil.getCurrentAccount();
        if (StringUtils.isBlank(account)) {
            throw new BizException(ErrorCodes.BIZ_ERROR, "Unable to acquire the current user");
        }
        String taskName =
                String.format("导出商店等级%s", DateFormatUtils.format(System.currentTimeMillis(), "yyyyMMddHHmmss"));
        jobReq.setTaskName(taskName);
        jobReq.setTaskParam(JacksonUtil.toStr(request));
        System.out.println(jobReq.getTaskParam());
        jobReq.setTaskDesc("导出商店等级");
        jobReq.setJobKey(NrJobConst.STORE_GRADE_RULE_EXPORT_JOB_KEY);
        jobReq.setOwner(account);
        String exportTask = createExportTask(jobReq);
        return new CommonApiResponse<>(exportTask);
    }

    private List<String> getRetailerCodes(String countryId, Integer channelType) {
        List<String> retailerCodes = new ArrayList<>();
        LambdaQueryWrapper<IntlRmsRetailer> storeQueryWrapper = Wrappers.lambdaQuery();
        storeQueryWrapper.eq(StringUtils.isNotBlank(countryId), IntlRmsRetailer::getCountryId, countryId);
        storeQueryWrapper.eq(channelType != null, IntlRmsRetailer::getRetailerChannelType, channelType);
        List<IntlRmsRetailer> intlRmsRetailers = intlRmsRetailerMapper.selectList(storeQueryWrapper);
        if (CollectionUtils.isEmpty(intlRmsRetailers)) {
            throw new BizException(ErrorCodes.BIZ_ERROR, "There is no retailer data under the current channel");
        }
        retailerCodes = intlRmsRetailers.stream().map(IntlRmsRetailer::getName).filter(Objects::nonNull)
                .collect(Collectors.toList());
        return retailerCodes;
    }

    /**
     * 获取国家信息
     *
     * @param countryCode
     * @return
     */
    private IntlRmsCountryTimezone getCountryByCode(String countryCode) {
        LambdaQueryWrapper<IntlRmsCountryTimezone> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(IntlRmsCountryTimezone::getCountryCode, countryCode);
        return intlRmsCountryTimezoneMapper.selectOne(queryWrapper);
    }

    /**
     * 创建定时任务
     *
     * @param request
     * @return
     */
    private String createExportTask(TriggerJobRequestDTO request) {
        request.setProjectId(projectId);
        // request.setProjectName(projectName);
        request.setProjectName(NrJobConst.PROJECT_NAME);
        log.info("NrJobGatewayImpl.createExportTask request: {}", request);

        Result<String> result = triggerJobWithLogging(request);

        validateResult(result);

        return result.getData();
    }

    /**
     * 触发任务
     *
     * @param request
     * @return
     */
    private Result<String> triggerJobWithLogging(TriggerJobRequestDTO request) {
        try {
            long start = System.currentTimeMillis();
            Result<String> result = nrJobService.triggerJob(request);
            long duration = System.currentTimeMillis() - start;
            log.info("NrJobGatewayImpl.createExportTask call triggerJob finished, cost time: {}ms, result: {}",
                    duration, result);
            return result;
        } catch (Exception e) {
            log.error("NrJobGatewayImpl.createExportTask call nrJob error", e);
            throw new BizException(ErrorCodes.BIZ_ERROR, "Failed to create task via task center");
        }
    }

    private void validateResult(Result<String> result) {
        if (result.getCode() != GeneralCodes.OK.getCode()) {
            throw new BizException(ErrorCodes.RPC_CALL_ERROR, result.getMessage());
        }

        String status = getStatusFromResult(result);
        handleStatusError(status);
    }

    private String getStatusFromResult(Result<String> result) {
        if (result.getAttachments() != null) {
            return result.getAttachments().get("status");
        }
        return null;
    }

    private void handleStatusError(String status) {
        if (status == null) {
            return;
        }

        switch (status) {
            case NrJobConst.TASK_NUM_LIMIT_EXCEEDED_CODE:
                throw new BizException(ErrorCodes.RPC_CALL_ERROR, "The number of export tasks exceeds the limit");
            case NrJobConst.TASK_IS_RUNNING_ERROR_CIDE:
                throw new BizException(ErrorCodes.RPC_CALL_ERROR, "The task is in progress, please try again later");
            default:
                throw new BizException(ErrorCodes.RPC_CALL_ERROR, "Calling the task center to create a task failed");
        }
    }

    /**
     * 商店等级导出异步回调
     */
    @NrJob(NrJobConst.STORE_GRADE_RULE_EXPORT_JOB_HANDLER)
    public Result<HandleMsg> exportHandler() {
        String param = JobHelper.getJobParam();
        log.info("exportHandler param:{}", param);
        StoreGradeExportReq exportReq = JacksonUtil.parseObj(param, StoreGradeExportReq.class);

        File file = null;
        List<String> retailerCodes = new ArrayList<>();
        List<StoreGradeRule> gradeRules =
                this.lambdaQuery().eq(StoreGradeRule::getRulesStatus, RuleStatusEnum.IN_EFFECT.getCode()).list();
        retailerCodes = gradeRules.stream().map(StoreGradeRule::getRetailerCode).filter(Objects::nonNull)
                .collect(Collectors.toList());
        List<String> countryCodes =
                gradeRules.stream().filter(l -> l.getChannelType().equals(ChannelTypeEnum.IR.getKey()))
                        .map(StoreGradeRule::getCountryCode).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(countryCodes)) {
            retailerCodes = getRetailerCodesByCountryCodes(countryCodes, retailerCodes);
        }

        try {
            file = File.createTempFile("StoreGrade", ExcelTypeEnum.XLSX.getValue());
            Page<IntlRmsStore> page = new Page<>(1, 500);
            ExcelWriter excelWriter = null;
            WriteSheet writeSheet = null;
            boolean writerInitialized = false;

            try {
                do {
                    Page<IntlRmsStore> rmsStorePage = getRmsStorePage(page, exportReq);
                    List<IntlRmsStore> records = rmsStorePage.getRecords();
                    if (!CollectionUtils.isEmpty(records)) {
                        List<StoreGradeExprot> gradeExprotList = getGradeExprotList(records, retailerCodes);
                        if (!writerInitialized) {
                            excelWriter = EasyExcelFactory.write(file, StoreGradeExprot.class).build();
                            writeSheet = EasyExcelFactory.writerSheet("StoreGrade").build();
                            writerInitialized = true;
                        }
                        if (excelWriter == null) {
                            throw new BizException(ErrorCodes.BIZ_ERROR, "Excel写入器未正确初始化");
                        }
                        excelWriter.write(gradeExprotList, writeSheet);
                        log.debug("成功写入{}条数据", gradeExprotList.size());
                    }

                    page.setCurrent(page.getCurrent() + 1);
                } while (page.hasNext());

            } finally {
                if (excelWriter != null) {
                    excelWriter.finish();
                    log.info("Excel文件写入完成");
                }
            }

            // 文件上传
            String objectName = "INSPECTION_FILE_NAME_PREFIX/" + UUID.randomUUID() + "/" + "StoreGrade.xlsx";
            FdsUploadResult upload = fdsService.upload(objectName, file, true);
            HandleMsg resp = new HandleMsg();
            resp.setFileUrl(upload.getUrl());
            String fileUrl = JacksonUtil.toStr(resp);
            JobHelper.handleSuccess(fileUrl);
            log.info("exportHandler url:{}", fileUrl);
            return Result.success(resp);

        } catch (Exception e) {
            log.error("导出异常 {}", e.getMessage(), e);
            JobHelper.handleFail(e.getMessage());
            return null;
        } finally {
            // 清理临时文件
            if (file != null && file.exists()) {
                file.delete();
            }
        }
    }

    /**
     * 获取国家代码对应的零售商
     *
     * @param countryCodes
     * @param retailerCodes
     */
    private List<String> getRetailerCodesByCountryCodes(List<String> countryCodes, List<String> retailerCodes) {
        if (retailerCodes == null) {
            retailerCodes = new ArrayList<>();
        }
        LambdaQueryWrapper<IntlRmsCountryTimezone> wrapper = Wrappers.lambdaQuery();
        wrapper.in(IntlRmsCountryTimezone::getCountryCode, countryCodes);
        List<IntlRmsCountryTimezone> intlRmsCountryTimezones = intlRmsCountryTimezoneMapper.selectList(wrapper);
        if (!CollectionUtils.isEmpty(intlRmsCountryTimezones)) {
            List<String> countryIds = intlRmsCountryTimezones.stream().map(IntlRmsCountryTimezone::getCountryId)
                    .collect(Collectors.toList());
            LambdaQueryWrapper<IntlRmsRetailer> queryWrapper = Wrappers.lambdaQuery();
            queryWrapper.in(IntlRmsRetailer::getCountryId, countryIds);
            queryWrapper.eq(IntlRmsRetailer::getRetailerChannelType, ChannelTypeEnum.IR.getKey());
            List<IntlRmsRetailer> intlRmsRetailers = intlRmsRetailerMapper.selectList(queryWrapper);
            if (!CollectionUtils.isEmpty(intlRmsRetailers)) {
                List<String> retailerCodeList =
                        intlRmsRetailers.stream().map(IntlRmsRetailer::getName).collect(Collectors.toList());
                retailerCodes.addAll(retailerCodeList);
            }
        }
        return retailerCodes;
    }

    /**
     * 转化为导出对象
     *
     * @param intlRmsStores
     * @param retailerCodes
     * @return
     */
    private List<StoreGradeExprot> getGradeExprotList(List<IntlRmsStore> intlRmsStores, List<String> retailerCodes) {
        List<StoreGradeExprot> storeGradeExprotList = new ArrayList<>();

        // 提前获取所有门店的capa数据，避免在循环中重复调用
        List<String> storeCodes = intlRmsStores.stream().map(IntlRmsStore::getCrssCode).filter(Objects::nonNull)
                .collect(Collectors.toList());
        Map<String, Integer> capaMap = getStoreCapa(storeCodes);

        for (IntlRmsStore intlRmsStore : intlRmsStores) {
            StoreGradeExprot storeGradeExprot = new StoreGradeExprot();
            String tieringComplete = intlRmsStore.getGradeCalFlag() == 0 ? "N" : "Y";
            storeGradeExprot.setTieringComplete(tieringComplete);
            storeGradeExprot.setStoreTiering(intlRmsStore.getGradeName());
            storeGradeExprot.setCountry(intlRmsStore.getCountryIdName());
            storeGradeExprot.setProvince(intlRmsStore.getProvinceLabel());
            storeGradeExprot.setCity(intlRmsStore.getCityIdName());
            storeGradeExprot.setStoreName(intlRmsStore.getName());
            storeGradeExprot.setStoreCode(intlRmsStore.getCrssCode());
            storeGradeExprot.setRetailerName(intlRmsStore.getRetailerName());
            storeGradeExprot.setRetailerCode(intlRmsStore.getRetailerIdName());
            storeGradeExprot.setChannelType(intlRmsStore.getChannelTypeName());
            Date modifiedOn = intlRmsStore.getModifiedOn();
            if (modifiedOn != null) {
                storeGradeExprot.setChangedDate(DateUtil.toLocalDateTime(intlRmsStore.getModifiedOn()));
            }

            // 使用预先获取的capaMap
            storeGradeExprot.setStoreCAPA(capaMap.get(intlRmsStore.getCrssCode()));

            if (intlRmsStore.getGradeCalFlag() == 0) {
                List<String> failureReasons = new ArrayList<>();

                if (!retailerCodes.contains(intlRmsStore.getRetailerIdName())) {
                    failureReasons.add("No Corresponding Rule");
                }
                if (StringUtils.isBlank(storeGradeExprot.getChannelType())) {
                    failureReasons.add("No Channel Type");
                }
                if (storeGradeExprot.getStoreCAPA() == null || storeGradeExprot.getStoreCAPA() == 0) {
                    failureReasons.add("No Store CAPA");
                }
                if (StringUtils.isBlank(storeGradeExprot.getRetailerCode())) {
                    failureReasons.add("No Retailer");
                }

                // 按顺序设置失败原因
                if (failureReasons.size() > 0) storeGradeExprot.setFailureReason1(failureReasons.get(0));
                if (failureReasons.size() > 1) storeGradeExprot.setFailureReason2(failureReasons.get(1));
                if (failureReasons.size() > 2) storeGradeExprot.setFailureReason3(failureReasons.get(2));
                if (failureReasons.size() > 3) storeGradeExprot.setFailureReason4(failureReasons.get(3));
            }
            storeGradeExprotList.add(storeGradeExprot);
        }
        return storeGradeExprotList;
    }

    /**
     * 根据门店编码获取门店capa
     *
     * @param storeCodes
     * @return
     */
    public Map<String, Integer> getStoreCapa(List<String> storeCodes) {
        Map<String, Integer> capaMap = new HashMap<>();
        String[] filters =
                new String[] {DomainEnum.EXTENSION.getName(), DomainEnum.MERGE.getName(), DomainEnum.BASE.getName(),
                        DomainEnum.CATEGORY.getName(), DomainEnum.SERVICE.getName()};
        OrgResponse orgResponse = mainDataRpc.selectStoreByOrgIds(storeCodes, filters);

        if (orgResponse != null && CollectionUtil.isNotEmpty(orgResponse.getOrgList())) {
            orgResponse.getOrgList().forEach(orgDataDto -> {
                OrgExtension orgExtension = orgDataDto.getOrgExtension();
                if (orgExtension != null) {
                    Integer capaMonth = orgExtension.getCapaMonth();
                    String orgId = orgExtension.getOrgId();
                    if (StringUtils.isNotBlank(orgId)) {
                        capaMap.put(orgId, capaMonth);
                    }
                }
            });
        }

        return capaMap;
    }

    /**
     * 根据条件分页返回数据
     *
     * @param page
     * @param exportReq
     * @return
     */
    private Page<IntlRmsStore> getRmsStorePage(Page<IntlRmsStore> page, StoreGradeExportReq exportReq) {
        LambdaQueryWrapper<IntlRmsStore> storeQueryWrapper = Wrappers.lambdaQuery();
        storeQueryWrapper.eq(StringUtils.isNotBlank(exportReq.getCountryId()), IntlRmsStore::getCountryId,
                exportReq.getCountryId());
        storeQueryWrapper.eq(StringUtils.isNotBlank(exportReq.getRetailerCode()), IntlRmsStore::getRetailerIdName,
                exportReq.getRetailerCode());
        storeQueryWrapper.in(!CollectionUtils.isEmpty(exportReq.getRetailerCodes()), IntlRmsStore::getRetailerIdName,
                exportReq.getRetailerCodes());
        storeQueryWrapper.isNotNull(IntlRmsStore::getCrssCode).ne(IntlRmsStore::getCrssCode, "");
        Page<IntlRmsStore> intlRmsStorePage = intlRmsStoreMapper.selectPage(page, storeQueryWrapper);
        return intlRmsStorePage;
    }

    /**
     * 根据kapa值和等级规则确定门店等级
     * 按照S->A->B->C->D的顺序从上到下判断
     *
     * @param kapa 实际容量值
     * @param rule 等级规则
     * @return 对应的等级
     */
    private String determineGradeByKapa(Long kapa, StoreGradeRule rule) {
        // S等级判断
        if (rule.getSMinCount() != null && kapa >= rule.getSMinCount()) {
            return "S";
        }
        // A等级判断
        if (rule.getAMinCount() != null && kapa >= rule.getAMinCount()) {
            return "A";
        }
        // B等级判断
        if (rule.getBMinCount() != null && kapa >= rule.getBMinCount()) {
            return "B";
        }
        // C等级判断
        if (rule.getCMinCount() != null && kapa >= rule.getCMinCount()) {
            return "C";
        }
        // D等级判断（默认等级）
        if (rule.getDMinCount() != null && kapa >= rule.getDMinCount()) {
            return "D";
        }

        // 如果都不满足，返回最低等级D
        return "D";
    }

    /**
     * 分页查询门店等级规则
     *
     * @param request 分页查询请求
     * @return 分页查询结果
     */
    @Override
    public CommonApiResponse<StoreGradeRulePageResp> pageQuery(StoreGradeRulePageQueryReq request) {
        try {
            log.info("Start paging to query store level rules and request parameters: {}", request);

            if (request == null) {
                log.error("Paging query request object is empty");
                throw new BizException(ErrorCodes.BIZ_ERROR, "Incorrect parameters");
            }

            // 构建查询条件
            LambdaQueryWrapper<StoreGradeRule> queryWrapper = Wrappers.lambdaQuery();

            // 规则状态查询（数组）
            if (request.getRuleStatus() != null && !request.getRuleStatus().isEmpty()) {
                queryWrapper.in(StoreGradeRule::getRulesStatus, request.getRuleStatus());
            }

            // 审批状态查询（数组）
            if (request.getApprovalStatus() != null && !request.getApprovalStatus().isEmpty()) {
                queryWrapper.in(StoreGradeRule::getApproveStatus, request.getApprovalStatus());
            }

            // 渠道类型查询（数组）
            if (request.getChannelType() != null && !request.getChannelType().isEmpty()) {
                queryWrapper.in(StoreGradeRule::getChannelType, request.getChannelType());
            }

            // 修改方式查询（数组）
            if (request.getModificationMethod() != null && !request.getModificationMethod().isEmpty()) {
                queryWrapper.in(StoreGradeRule::getMethod, request.getModificationMethod());
            }

            // 零售商名称或编码模糊搜索
            if (StrUtil.isNotBlank(request.getRetailerNameCode())) {
                queryWrapper.and(qw -> qw.like(StoreGradeRule::getRetailerName, request.getRetailerNameCode()).or()
                        .like(StoreGradeRule::getRetailerCode, request.getRetailerNameCode()));
            }

            if (Objects.nonNull(request.getCountryCode()) && !Objects.equals("GLOBAL", request.getCountryCode())) {
                queryWrapper.eq(StoreGradeRule::getCountryCode, request.getCountryCode());
            }

            // 申请日期区间查询
            if (request.getApplicationDate() != null) {
                if (Objects.nonNull(request.getApplicationDate().getStart())) {
                    queryWrapper.ge(StoreGradeRule::getApplicationTime,
                            DateTimeUtil.timestampToLocalDateTime(request.getApplicationDate().getStart()));
                }
                if (Objects.nonNull(request.getApplicationDate().getEnd())) {
                    queryWrapper.le(StoreGradeRule::getApplicationTime,
                            DateTimeUtil.timestampToLocalDateTime(request.getApplicationDate().getEnd()));
                }
            }

            // 审批日期区间查询
            if (request.getApprovedDate() != null) {
                if (Objects.nonNull(request.getApprovedDate().getStart())) {
                    queryWrapper.ge(StoreGradeRule::getApprovedTime,
                            DateTimeUtil.timestampToLocalDateTime(request.getApprovedDate().getStart()));
                }
                if (Objects.nonNull(request.getApprovedDate().getEnd())) {
                    queryWrapper.le(StoreGradeRule::getApprovedTime,
                            DateTimeUtil.timestampToLocalDateTime(request.getApprovedDate().getEnd()));
                }
            }

            // 按申请时间倒序排列
            queryWrapper.orderByDesc(StoreGradeRule::getApplicationTime);

            // 执行分页查询
            Page<StoreGradeRule> page = new Page<>(request.getPageNum(), request.getPageSize());
            Page<StoreGradeRule> resultPage = this.page(page, queryWrapper);

            // 构建响应对象
            StoreGradeRulePageResp response = new StoreGradeRulePageResp();
            response.setPageNum((int) resultPage.getCurrent());
            response.setPageSize((int) resultPage.getSize());
            response.setTotal(resultPage.getTotal());
            response.setTotalPages((int) resultPage.getPages());

            // 转换数据列表
            List<StoreGradeRulePageResp.StoreGradeRuleItem> itemList =
                    resultPage.getRecords().stream().map(this::convertToPageItem).collect(Collectors.toList());
            response.setList(itemList);

            log.info("Paging query store grade rules completed, total records: {}", resultPage.getTotal());
            return new CommonApiResponse<>(response);

        } catch (Exception e) {
            log.error("Failed to paging query store grade rules", e);
            return CommonApiResponse.failure(500, e.getMessage());
        }
    }

    /**
     * 将实体对象转换为分页响应项
     *
     * @param entity 实体对象
     * @return 分页响应项
     */
    private StoreGradeRulePageResp.StoreGradeRuleItem convertToPageItem(StoreGradeRule entity) {
        StoreGradeRulePageResp.StoreGradeRuleItem item = new StoreGradeRulePageResp.StoreGradeRuleItem();

        // 复制基本属性
        item.setId(entity.getId());
        item.setChannelType(entity.getChannelType());
        item.setMethod(entity.getMethod() != null ? entity.getMethod() : null); // 兼容处理
        item.setRetailerName(entity.getRetailerName());
        item.setRetailerCode(entity.getRetailerCode());
        item.setSMinCount(entity.getSMinCount() != null ? entity.getSMinCount() : null);
        item.setAMinCount(entity.getAMinCount() != null ? entity.getAMinCount() : null);
        item.setBMinCount(entity.getBMinCount() != null ? entity.getBMinCount() : null);
        item.setCMinCount(entity.getCMinCount() != null ? entity.getCMinCount() : null);
        item.setDMinCount(entity.getDMinCount() != null ? entity.getDMinCount() : null);

        // 设置规则状态
        item.setRuleStatus(entity.getRulesStatus());
        item.setRuleStatusDesc(RuleStatusEnum.getDescByCode(entity.getRulesStatus()));

        // 设置审批状态
        item.setApprovalStatus(entity.getApproveStatus());
        item.setApprovalStatusDesc(ApprovalStatus.getDescByCode(entity.getApproveStatus()));

        // 格式化时间
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");
        if (entity.getApplicationTime() != null) {
            String applicationTime = IntlTimeUtil.parseTimestampToAreaTimeByFormat(entity.getCountryCode(),
                    DateTimeUtil.toTimestampMillis(entity.getApplicationTime()), dateTimeFormatter);
            item.setApplicationTime(applicationTime);
        }
        if (entity.getApprovedTime() != null) {
            String approvedTime = IntlTimeUtil.parseTimestampToAreaTimeByFormat(entity.getCountryCode(),
                    DateTimeUtil.toTimestampMillis(entity.getApprovedTime()), dateTimeFormatter);
            item.setApprovedTime(approvedTime);
        }

        return item;
    }

    /**
     * 转换规则状态
     *
     * @param rulesStatus 规则状态字符串
     * @return 规则状态数字
     */
    private Integer convertRuleStatus(String rulesStatus) {
        if (StrUtil.isBlank(rulesStatus)) {
            return 0;
        }
        switch (rulesStatus.toUpperCase()) {
            case "IN EFFECT":
                return 1;
            case "NOT IN EFFECT":
                return 0;
            default:
                return 0;
        }
    }

    /**
     * 获取规则状态描述
     *
     * @param ruleStatus 规则状态数字
     * @return 规则状态描述
     */
    private String getRuleStatusDesc(Integer ruleStatus) {
        if (ruleStatus == null) {
            return "Unknown";
        }
        switch (ruleStatus) {
            case 1:
                return "In Effect";
            case 0:
                return "Not In Effect";
            default:
                return "Unknown";
        }
    }

    /**
     * 转换审批状态
     *
     * @param approveStatus 审批状态字符串
     * @return 审批状态数字
     */
    private Integer convertApprovalStatus(String approveStatus) {
        if (StrUtil.isBlank(approveStatus)) {
            return 0;
        }
        switch (approveStatus.toUpperCase()) {
            case "APPROVED":
                return 1;
            case "IN APPROVAL":
                return 0;
            case "REJECTED":
                return 2;
            default:
                return 0;
        }
    }

    /**
     * 获取审批状态描述
     *
     * @param approvalStatus 审批状态数字
     * @return 审批状态描述
     */
    private String getApprovalStatusDesc(Integer approvalStatus) {
        if (approvalStatus == null) {
            return "Unknown";
        }
        switch (approvalStatus) {
            case 1:
                return "Approved";
            case 0:
                return "In Approval";
            case 2:
                return "Rejected";
            default:
                return "Unknown";
        }
    }
}
