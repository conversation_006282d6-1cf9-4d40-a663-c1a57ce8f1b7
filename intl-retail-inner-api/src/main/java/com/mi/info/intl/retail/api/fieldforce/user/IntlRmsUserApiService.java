package com.mi.info.intl.retail.api.fieldforce.user;

import java.util.List;

import com.mi.info.intl.retail.dto.IntlRmsUserDTO;

/**
 * <AUTHOR>
 * @date 2025/7/31
 **/
public interface IntlRmsUserApiService {

  List<IntlRmsUserDTO> getRmsUserByMiIds(List<Long> miIdList);

  /**
   * 根据uniqueName获取rms用户信息，对应intl_rms_user.domain_name。 先查缓存，没有再查数据库
   * 
   * @param uniqueName jwt解析的unique_name
   * @return rms用户信息
   */
  IntlRmsUserDTO getRmsUserByUniqueName(String uniqueName);
}
