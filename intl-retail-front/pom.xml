<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <artifactId>intl-retail</artifactId>
        <groupId>com.mi.info.intl.retail</groupId>
        <version>${revision}</version>
    </parent>

    <artifactId>intl-retail-front</artifactId>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <nr-eiam-api.version>2.0.5-SNAPSHOT</nr-eiam-api.version>
    </properties>

    <dependencies>
        <!-- 核心依赖 -->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xiaomi.nr</groupId>
            <artifactId>nr-job-core</artifactId>
            <version>2.6.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.xiaomi.nr</groupId>
            <artifactId>nr-eiam-api</artifactId>
            <version>${nr-eiam-api.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-reload4j</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>prometheus-starter</artifactId>
                    <groupId>com.xiaomi.youpin</groupId>
                </exclusion>
                <exclusion>
                    <groupId>asm</groupId>
                    <artifactId>asm</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>javax.servlet</groupId>
                    <artifactId>servlet-api</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>spring-cloud-starter-alibaba-sentinel</artifactId>
                    <groupId>com.alibaba.cloud</groupId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-autoconfigure</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.xiaomi.nr</groupId>
            <artifactId>nr-eiam-common</artifactId>
            <version>2.0.5-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>prometheus-starter</artifactId>
                    <groupId>com.xiaomi.youpin</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.xiaomi.nr</groupId>
            <artifactId>nr-eiam-admin</artifactId>
            <version>${nr-eiam-api.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-reload4j</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>prometheus-starter</artifactId>
                    <groupId>com.xiaomi.youpin</groupId>
                </exclusion>
                <exclusion>
                    <groupId>asm</groupId>
                    <artifactId>asm</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>javax.servlet</groupId>
                    <artifactId>servlet-api</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>spring-cloud-starter-alibaba-sentinel</artifactId>
                    <groupId>com.alibaba.cloud</groupId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-autoconfigure</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.mi.info.intl.retail</groupId>
            <artifactId>intl-retail-common</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.xiaomi.nr</groupId>
            <artifactId>nr-job-api</artifactId>
            <version>2.5.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <artifactId>nr-upload-client</artifactId>
            <groupId>com.xiaomi.nr</groupId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <artifactId>dubbo-auth</artifactId>
            <groupId>com.xiaomi.newretail</groupId>
            <version>1.2.0</version>
        </dependency>
        <dependency>
            <groupId>com.mi.info.intl.retail</groupId>
            <artifactId>intl-retail-core</artifactId>
        </dependency>

        <!-- 任务调度能力依赖 - 通过cooperation模块提供 -->
        <dependency>
            <groupId>com.mi.info.intl.retail</groupId>
            <artifactId>intl-retail-cooperation</artifactId>
            <version>${project.version}</version>
        </dependency>

        <!-- 其他业务模块依赖 -->
        <dependency>
            <groupId>com.xiaomi.infra.galaxy</groupId>
            <artifactId>galaxy-fds-sdk-java</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xiaomi.nr</groupId>
            <artifactId>nr-job-core</artifactId>
            <version>2.5.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.xiaomi.nr</groupId>
            <artifactId>nr-job-api</artifactId>
            <version>2.6.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.mi.info.intl.retail</groupId>
            <artifactId>intl-retail-fieldforce</artifactId>
            <version>${project.version}</version>
        </dependency>
         <dependency>
            <groupId>com.xiaomi.cnzone</groupId>
            <artifactId>store-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-annotation</artifactId>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
        </dependency>
        <!-- API文档相关 -->
        <dependency>
            <groupId>com.xiaomi.mone</groupId>
            <artifactId>http-docs-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xiaomi.mone</groupId>
            <artifactId>miapi-doc-annos</artifactId>
        </dependency>

        <dependency>
            <groupId>com.mi.info.intl.retail</groupId>
            <artifactId>intl-retail-infra</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.mioffice</groupId>
            <artifactId>guard-mail-spring-boot-starter</artifactId>
            <version>1.0.5</version>
        </dependency>
        <dependency>
            <groupId>com.xiaomi.cnzone</groupId>
            <artifactId>brain-platform-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.mi.info.idd</groupId>
            <artifactId>alarm-client-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-inline</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.mi.info.intl.retail</groupId>
            <artifactId>intl-retail-sales</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.xiaomi.cnzone.commons</groupId>
            <artifactId>commons-util</artifactId>
            <version>3.7-SP6</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-slf4j-impl</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.google.zxing</groupId>
            <artifactId>core</artifactId>
            <version>3.4.1</version>
        </dependency>
        <dependency>
            <groupId>com.mi.info.intl.retail</groupId>
            <artifactId>intl-retail-inner-api</artifactId>
            <version>${project.version}</version>
        </dependency>

    </dependencies>
<build>
    <plugins>
        <plugin>
            <groupId>com.mi.xms</groupId>
            <artifactId>neptune-maven-plugin</artifactId>
            <version>1.0-SNAPSHOT</version>
            <executions>
                <execution>
                    <phase>compile</phase>
                    <goals>
                        <goal>translate</goal>
                    </goals>
                </execution>
            </executions>
            <configuration>
                <appId>605cfb858b254dd89f4d5d49b4ddcb33</appId>
                <env>${neptune_env}</env>
            </configuration>
        </plugin>
    </plugins>
</build>

</project>