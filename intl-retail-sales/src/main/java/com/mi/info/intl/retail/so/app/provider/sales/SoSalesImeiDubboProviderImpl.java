package com.mi.info.intl.retail.so.app.provider.sales;

import javax.annotation.Resource;

import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.mi.info.intl.retail.exception.ErrorCodes;
import com.mi.info.intl.retail.intlretail.service.api.so.sales.dto.ReportingRoleRespDto;
import com.mi.info.intl.retail.intlretail.service.api.so.sales.dto.SalesImeiRespDto;
import com.mi.info.intl.retail.intlretail.service.api.so.sales.dto.VerificationResultRespDto;
import com.mi.info.intl.retail.intlretail.service.api.so.sales.provider.SoSalesImeiDubboProvider;
import com.mi.info.intl.retail.intlretail.service.api.so.sales.request.SalesImeiReqDto;
import com.mi.info.intl.retail.intlretail.util.AppProviderUtil;
import com.mi.info.intl.retail.model.CommonApiResponse;
import com.mi.info.intl.retail.model.UserInfo;
import com.mi.info.intl.retail.so.domain.sales.service.IntlSoImeiDomainService;
import com.mi.info.intl.retail.utils.CommonApiRespUtil;
import com.mi.info.intl.retail.utils.UserInfoUtil;
import com.xiaomi.mone.docs.annotations.dubbo.ApiDoc;
import com.xiaomi.mone.docs.annotations.dubbo.ApiModule;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@DubboService(group = "${center.dubbo.group:}", interfaceClass = SoSalesImeiDubboProvider.class)
@ApiModule(value = "国际渠道零售服务", apiInterface = SoSalesImeiDubboProvider.class)
public class SoSalesImeiDubboProviderImpl implements SoSalesImeiDubboProvider {

    @Resource
    private IntlSoImeiDomainService intlSoImeiDomainService;

    @ApiDoc(value = "/api/so/v1/salesImei/getSalesImeiForPage", description = "Imei数据分页查询")
    @Override
    public CommonApiResponse<IPage<SalesImeiRespDto>> getSalesImeiForPage(SalesImeiReqDto salesImeiReqDto) {
        if (null == salesImeiReqDto) {
            log.error("[method]getSalesImeiForPage:salesImeiReqDto is null......");
            return CommonApiResponse.failure(ErrorCodes.PARAM_IS_EMPTY.getCode(), "The parameter 'salesImeiReqDto' is empty");
        }
        return AppProviderUtil.wrap(log, "SoSalesImeiDubboProvider::getSalesImeiForPage", salesImeiReqDto,
                intlSoImeiDomainService::getSalesImeiForPage, false);
    }

    @ApiDoc(value = "/api/so/v1/salesImei/getImeiVerificationResultData", description = "Imei获取验证结果数据")
    @Override
    public CommonApiResponse<VerificationResultRespDto> getImeiVerificationResultData(SalesImeiReqDto salesImeiReqDto) {
        return AppProviderUtil.wrap(log, "SoSalesImeiDubboProvider::getImeiVerificationResultData", salesImeiReqDto,
                intlSoImeiDomainService::getImeiVerificationResultData, false);
    }

    @ApiDoc(value = "/api/so/v1/salesImei/getImeiReportingRoleData", description = "Imei获取上报角色数据")
    @Override
    public CommonApiResponse<ReportingRoleRespDto> getImeiReportingRoleData(SalesImeiReqDto salesImeiReqDto) {
        return AppProviderUtil.wrap(log, "SoSalesImeiDubboProvider::getImeiReportingRoleData", salesImeiReqDto,
                intlSoImeiDomainService::getImeiReportingRoleData, false);
    }

    @ApiDoc(value = "/api/so/v1/salesImei/isPlaintextImeiUser", description = "是否明文IMEI用户")
    @Override
    public CommonApiResponse<Boolean> isPlaintextImeiUser() {
        try {
            UserInfo userInfo = UserInfoUtil.getUserContext();
            if (null == userInfo) {
                return CommonApiResponse.failure(ErrorCodes.PARAM_IS_EMPTY.getCode(), "Failed to obtain login user information");
            }
            log.info("SoSalesImeiDubboProvider::isPlaintextImeiUser, userInfo:{}", userInfo);
            return CommonApiResponse.success(intlSoImeiDomainService.isPlaintextImeiUser(userInfo));
        } catch (Exception e) {
            log.error("is Plaintext Imei User Exception", e);
            return CommonApiRespUtil.failed4Ex(e);
        }
    }

    @ApiDoc(value = "/api/so/v1/salesImei/export", description = "IMEI数据下载")
    @Override
    public CommonApiResponse<String> export(SalesImeiReqDto salesImeiReqDto) {
        return AppProviderUtil.wrap(log, "SoSalesImeiDubboProvider::export", salesImeiReqDto,
                intlSoImeiDomainService::export, false);
    }
}
