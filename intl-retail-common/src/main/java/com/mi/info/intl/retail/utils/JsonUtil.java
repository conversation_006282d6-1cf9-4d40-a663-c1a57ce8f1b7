package com.mi.info.intl.retail.utils;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.type.TypeFactory;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Slf4j
public class JsonUtil {
    private static final ObjectMapper INSTANCE = new ObjectMapper();

    private JsonUtil() {
    }

    static {
        INSTANCE.configure(JsonParser.Feature.ALLOW_UNQUOTED_FIELD_NAMES, true);
        //忽略未定义字段
        INSTANCE.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        //忽略值为null的字段
        INSTANCE.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        // 配置日期格式
        INSTANCE.configure(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
        INSTANCE.setDateFormat(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));
        INSTANCE.registerModule(new JavaTimeModule());
    }

    public static String bean2json(Object bean) {
        try {
            return INSTANCE.writeValueAsString(bean);
        } catch (IOException e) {
            log.error("{} bean2json exception: {}", bean, e.getMessage());
            return null;
        }
    }

    public static <T> T json2bean(String json, Class<T> clazz) {
        if (Strings.isNullOrEmpty(json)) {
            return null;
        }
        try {
            return INSTANCE.readValue(json, clazz);
        } catch (IOException e) {
            log.error("{} json2bean exception: {}", json, e.getMessage());
            return null;
        }
    }

    public static <T> T json2bean(String json, TypeReference<T> typeReference) {
        if (Strings.isNullOrEmpty(json)) {
            return null;
        }
        try {
            return INSTANCE.readValue(json, typeReference);
        } catch (IOException e) {
            log.error("{} json2bean exception: {}", json, e.getMessage());
            return null;
        }
    }

    public static <T> List<T> jsonArr2beanList(String json, Class<T> clazz) {
        if (Strings.isNullOrEmpty(json)) {
            return Collections.emptyList();
        }
        try {
            return INSTANCE.readValue(json, TypeFactory.defaultInstance().constructCollectionType(List.class, clazz));
        } catch (IOException e) {
            log.error("{} jsonArr2beanList exception: {}", json, e.getMessage());
            return Collections.emptyList();
        }
    }

    public static <K, V> Optional<String> map2json(Map<K, V> map) {
        try {
            return Optional.ofNullable(INSTANCE.writeValueAsString(map));
        } catch (IOException e) {
            log.error("map2json exception: {}", e.getMessage(), e);
            return Optional.empty();
        }
    }

    public static <K, V> Map<K, V> json2map(String json) {
        if (json == null || json.isEmpty()) {
            return Collections.emptyMap();
        }
        try {
            return INSTANCE.readValue(json, new TypeReference<Map<K, V>>() {
            });
        } catch (IOException e) {
            log.error("json2map exception: {}", e.getMessage(), e);
            return Collections.emptyMap();
        }
    }

    public static <K, V> Map<K, V> json2map(String json, TypeReference<Map<K, V>> typeReference) {
        if (json == null || json.isEmpty()) {
            return Collections.emptyMap();
        }
        try {
            return INSTANCE.readValue(json, typeReference);
        } catch (IOException e) {
            log.error("json2map exception: {}", e.getMessage(), e);
            return Collections.emptyMap();
        }
    }
}
