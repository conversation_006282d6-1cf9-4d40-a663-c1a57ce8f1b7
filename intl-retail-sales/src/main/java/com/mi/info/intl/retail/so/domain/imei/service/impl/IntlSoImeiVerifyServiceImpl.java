package com.mi.info.intl.retail.so.domain.imei.service.impl;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.Optional;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import com.mi.info.intl.retail.core.feishu.service.SendMessageService;
import com.mi.info.intl.retail.so.app.provider.enums.DataFromEnum;
import com.mi.info.intl.retail.so.app.provider.enums.FailedReasonEnum;
import com.mi.info.intl.retail.so.app.provider.enums.VerifyingStateEnum;
import com.mi.info.intl.retail.so.app.provider.enums.VerificationResultEnum;
import com.mi.info.intl.retail.so.domain.imei.constant.VerifyResultDetailConstant;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import com.mi.info.intl.retail.so.app.dto.SnImeiActiveInfoDTO;
import com.mi.info.intl.retail.so.app.dto.SnImeiActiveReq;
import com.mi.info.intl.retail.so.domain.imei.service.IntlSoImeiVerifyService;
import com.mi.info.intl.retail.so.domain.upload.config.ImeiVerifyConfig;
import com.mi.info.intl.retail.so.domain.upload.entity.IntlSoImei;
import com.mi.info.intl.retail.so.domain.upload.entity.IntlSoOrgInfo;
import com.mi.info.intl.retail.so.domain.upload.service.IntlSoImeiService;
import com.mi.info.intl.retail.so.domain.upload.service.IntlSoOrgInfoService;
import com.mi.info.intl.retail.so.infra.service.SnImeiActiveQueryService;
import com.mi.info.intl.retail.utils.intl.IntlTimeUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class IntlSoImeiVerifyServiceImpl implements IntlSoImeiVerifyService {

    @Resource
    private ImeiVerifyConfig imeiVerifyConfig;

    @Resource
    private IntlSoImeiService intlSoImeiService;

    @Resource
    private IntlSoOrgInfoService intlSoOrgInfoService;

    @Resource
    private SnImeiActiveQueryService snImeiActiveQueryService;

    @Resource
    private SendMessageService sendMessageService;

    private static final Integer BATCH_QUERY_SIZE = 3000;

    @Value("${miwork.alarm.groupId.p0}")
    private String groupId;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<Long> verifyActivationStatusOfImei() {
        try {

            int normalStatus = VerifyingStateEnum.NORMAL.getValue();
            int verifyingResult = VerificationResultEnum.VERIFYING.getValue();

            //2.获取需要校验的imei，正序3000条
            List<IntlSoImei> needVerifyImeiList = intlSoImeiService.getNeedVerifyImeiList(normalStatus, verifyingResult,
                    DataFromEnum.MIRETAIL.getValue(), BATCH_QUERY_SIZE);
            log.debug("verifyActivationStatusOfImei needVerifyImeiList size:{}", needVerifyImeiList.size());

            if (CollectionUtils.isEmpty(needVerifyImeiList)) {
                return Collections.emptyList();
            }

            log.debug("verifyActivationStatusOfImei needVerifyImeiList idList:{}",
                    needVerifyImeiList.stream().map(IntlSoImei::getId).map(Object::toString).toString());
            //3.遍历needVerifyImeiList，获取所有的orgInfoId
            List<Long> orgInfoIdList = needVerifyImeiList.stream().map(IntlSoImei::getOrgInfoId).distinct()
                    .collect(Collectors.toList());

            List<IntlSoOrgInfo> orgInfoList = intlSoOrgInfoService.batchGetByIds(orgInfoIdList);
            Map<Long, String> countryMap = orgInfoList.stream()
                    .collect(Collectors.toMap(IntlSoOrgInfo::getId, IntlSoOrgInfo::getCountryCode));

            //4.读取dayu配置获取哈希国家
            List<String> hashCountryList = imeiVerifyConfig.getHashCountryList();

            //5.构造IMEI激活服务查询参数、区分sn和snHex
            Map<Long, IntlSoImei> idToDetailMap = new HashMap<>();
            List<SnImeiActiveReq> snImeiActiveReqs = new ArrayList<>();
            List<String> crossExcludeClusters = Collections.singletonList("zjyprc-hadoop");

            for (IntlSoImei intlSoImei : needVerifyImeiList) {
                String country = countryMap.get(intlSoImei.getOrgInfoId());
                String sn = StringUtils.EMPTY;
                String snHexIdx = StringUtils.EMPTY;

                if (StringUtils.isBlank(country) || !hashCountryList.contains(country)) {
                    sn = intlSoImei.getSn();
                } else {
                    snHexIdx = intlSoImei.getSnHash();
                }
                idToDetailMap.put(intlSoImei.getId(), intlSoImei);
                SnImeiActiveReq req = new SnImeiActiveReq();
                req.setCross(true);
                req.setCrossExcludeClusters(crossExcludeClusters);
                req.setSoImeiId(intlSoImei.getId());
                req.setSn(sn);
                req.setSnHexIdx(snHexIdx);
                snImeiActiveReqs.add(req);
            }

            //6.批量查询IMEI激活信息
            List<Long> mqList = new ArrayList<>();
            final List<IntlSoImei> result = snImeiActiveQueryService.batchQueryImeiActiveInfo(snImeiActiveReqs,
                    (req, snImeiInfoDto) -> {
                        IntlSoImei intlSoImei = idToDetailMap.get(req.getSoImeiId());
                        String countryCode = countryMap.get(intlSoImei.getOrgInfoId());
                        return this.processImeiActivationInfo(snImeiInfoDto, intlSoImei, countryCode, mqList);
                    });


            //7.批量更新数据库
            intlSoImeiService.batchSoftUpdateById(result);
            return mqList;
        } catch (Exception e) {
            log.error("verifyActivationStatusOfImei 校验过程中发生异常", e);
            throw new RuntimeException("verifyActivationStatusOfImei failed:" + e.getMessage());
        }
    }

    /**
     * 处理IMEI激活信息
     *
     * @param snImeiInfoDto 激活信息
     * @param intlSoImei IMEI详情
     * @param countryCode 国家映射
     * @param mqList MQ列表
     * @return IMEI列表
     */
    private IntlSoImei processImeiActivationInfo(SnImeiActiveInfoDTO snImeiInfoDto, IntlSoImei intlSoImei,
                                                 String countryCode, List<Long> mqList) {

        //接口调用失败的imei处理流程
        if (null == snImeiInfoDto) {
            return this.convertContinueVerifyDetail(intlSoImei);
        }

        //个保法不可查询，均为失败
        if (snImeiInfoDto.getInfoExclude() == 1) {
            return this.convertFailedDetail(intlSoImei, mqList);
        }

        if (null == snImeiInfoDto.getInfo() || StringUtils.isBlank(snImeiInfoDto.getInfo().getActiveTime())) {
            //接口没有返回激活信息
            this.convertNoActiveInfo(intlSoImei, countryCode, mqList);
        } else {
            //接口返回激活信息
            this.convertActiveInfo(snImeiInfoDto.getInfo(), intlSoImei, countryCode, mqList);
        }

        return intlSoImei;
    }

    private void convertNoActiveInfo(IntlSoImei intlSoImei, String countryCode, List<Long> mqList) {

        if (StringUtils.isBlank(countryCode)) {
            return;
        }

        // 将销售时间和激活时间转换为对应国家时区的日期字符串（格式：yyyyMMdd）
        String salesDateStr = IntlTimeUtil.parseTimestampToFormatAreaTime(
                IntlTimeUtil.TIME_FORMAT_DAY, countryCode, intlSoImei.getSalesTime());
        String currentDateStr = IntlTimeUtil.parseTimestampToFormatAreaTime(
                IntlTimeUtil.TIME_FORMAT_DAY, countryCode, System.currentTimeMillis());

        Optional<Boolean> isWithinRule = isActivationTimeWithinRule(intlSoImei.getId(), salesDateStr, currentDateStr,
                intlSoImei.getImeiRuleBefore(), intlSoImei.getImeiRuleAfter() + 3);

        // 判断激活时间是否在销售时间前后规则范围内
        if (isWithinRule.isPresent() && Optional.of(Boolean.FALSE).equals(isWithinRule)) {
            // 不在范围内，激活校验失败
            intlSoImei.setVerificationResult(VerificationResultEnum.FAILED.getValue());
            intlSoImei.setVerifyResultDetail(VerifyResultDetailConstant.NO_VERIFICATION);
            intlSoImei.setFailedReason(FailedReasonEnum.NOT_ACTIVATED.getCode());
            mqList.add(intlSoImei.getId());
        }

        int frequency = getDefaultIfZero(intlSoImei.getActivationFrequency()) + 1;
        intlSoImei.setActivationFrequency(frequency);
        intlSoImei.setActivationVerificationTime(System.currentTimeMillis());
        intlSoImei.setModifiedOn(System.currentTimeMillis());
    }

    private void convertActiveInfo(SnImeiActiveInfoDTO.Info activeInfo, IntlSoImei intlSoImei, String countryCode, List<Long> mqList) {

        // 获取国家代码
        if (StringUtils.isBlank(countryCode)) {
            return;
        }

        // 将激活时间字符串转换为长整型时间戳
        long activeTime = (long) Double.parseDouble(activeInfo.getActiveTime()) * 1000;

        // 将销售时间和激活时间转换为对应国家时区的日期字符串（格式：yyyyMMdd）
        String salesDateStr = IntlTimeUtil.parseTimestampToFormatAreaTime(
                IntlTimeUtil.TIME_FORMAT_DAY, countryCode, intlSoImei.getSalesTime());
        String activeDateStr = IntlTimeUtil.parseTimestampToFormatAreaTime(
                IntlTimeUtil.TIME_FORMAT_DAY, countryCode, activeTime);

        int frequency = getDefaultIfZero(intlSoImei.getActivationFrequency()) + 1;
        intlSoImei.setActivationFrequency(frequency);
        intlSoImei.setActivationVerificationTime(System.currentTimeMillis());

        Optional<Boolean> isWithinRule = isActivationTimeWithinRule(intlSoImei.getId(), salesDateStr, activeDateStr,
                intlSoImei.getImeiRuleBefore(), intlSoImei.getImeiRuleAfter());

        if (!isWithinRule.isPresent()) {
            return;
        }

        // 判断激活时间是否在销售时间前后规则范围内
        if (Optional.of(Boolean.TRUE).equals(isWithinRule)) {
            // 在范围内，激活校验成功
            intlSoImei.setVerificationResult(VerificationResultEnum.SUCCESSFULLY.getValue());
            intlSoImei.setVerifyResultDetail(VerifyResultDetailConstant.SUCCESS);
        } else {
            // 不在范围内，激活校验失败
            intlSoImei.setVerificationResult(VerificationResultEnum.FAILED.getValue());
            intlSoImei.setVerifyResultDetail(VerifyResultDetailConstant.TIME_EXCEED_RULE);
            intlSoImei.setFailedReason(FailedReasonEnum.EXCEEDS_EVENT_PERIOD.getCode());
        }
        intlSoImei.setActivationTime(activeTime);
        intlSoImei.setActivationSite(activeInfo.getActiveCountry());
        intlSoImei.setModifiedOn(System.currentTimeMillis());

        mqList.add(intlSoImei.getId());
    }

    /**
     * 获取默认值，如果原值为null或0
     *
     * @param value 原值
     * @return 如果原值为null或0则返回默认值0，否则返回原值
     */
    private int getDefaultIfZero(Integer value) {
        return value != null ? value : 0;
    }

    /**
     * 判断激活时间是否在销售时间前后规则范围内
     *
     * @param id 激活记录ID
     * @param salesDateStr 销售日期字符串（格式：yyyyMMdd）
     * @param targetDateStr 激活日期/目标日期字符串（格式：yyyyMMdd）
     * @param before 激活时间与销售时间之间的最小天数差
     * @param after 激活时间与销售时间之间最大的天数差
     * @return 是否在规则范围内
     */
    private Optional<Boolean> isActivationTimeWithinRule(Long id, String salesDateStr, String targetDateStr, Integer before, Integer after) {
        try {
            // 解析日期字符串为LocalDate对象
            LocalDate salesDate = LocalDate.parse(salesDateStr, DateTimeFormatter.ofPattern(IntlTimeUtil.TIME_FORMAT_DAY));
            LocalDate activeDate = LocalDate.parse(targetDateStr, DateTimeFormatter.ofPattern(IntlTimeUtil.TIME_FORMAT_DAY));

            // 计算激活日期与销售日期之间的天数差
            long daysBetween = ChronoUnit.DAYS.between(salesDate, activeDate);

            // 设置默认值
            if (before == null) {
                before = 0;
            }
            if (after == null) {
                after = 0;
            }

            // 判断激活时间是否在销售时间前后规则范围内
            return Optional.of(daysBetween >= -before && daysBetween <= after);
        } catch (Exception e) {
            log.error("判断激活时间是否在规则范围内时发生异常: {}", e.getMessage());
            sendMessageService.sendGroupTextMessage(groupId, "", "激活校验id: " + id + " 判断激活时间是否在规则范围内时发生异常." + e.getMessage());
            return Optional.empty();
        }
    }

    private IntlSoImei convertContinueVerifyDetail(IntlSoImei intlSoImei) {
        int frequency = intlSoImei.getActivationFrequency() + 1;
        intlSoImei.setActivationFrequency(frequency);
        intlSoImei.setActivationVerificationTime(System.currentTimeMillis());
        intlSoImei.setModifiedOn(System.currentTimeMillis());
        return intlSoImei;
    }

    private IntlSoImei convertFailedDetail(IntlSoImei intlSoImei, List<Long> mqList) {
        int frequency = intlSoImei.getActivationFrequency() + 1;
        intlSoImei.setActivationFrequency(frequency);
        intlSoImei.setActivationVerificationTime(System.currentTimeMillis());
        intlSoImei.setVerificationResult(VerificationResultEnum.FAILED.getValue());
        intlSoImei.setVerifyResultDetail(VerifyResultDetailConstant.NO_VERIFICATION);
        intlSoImei.setFailedReason(FailedReasonEnum.NOT_ACTIVATED.getCode());
        intlSoImei.setModifiedOn(System.currentTimeMillis());
        mqList.add(intlSoImei.getId());
        return intlSoImei;
    }
}